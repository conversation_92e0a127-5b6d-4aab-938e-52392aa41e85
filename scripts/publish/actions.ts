import ora from 'ora';
import sh from 'shelljs';


import { git } from '../utils/git.js';
import { getAllPublicPackages } from '../utils/packages.js';
import { version } from '../utils/version.js';

import { genChangeset, replaceVersion, updateVersion } from './changeset.js';


interface PublishOpts {
    /**
     * 发布的版本
     */
    version?: 'major' | 'minor' | 'patch';

    /**
     * 发布到的环境，目前仅使用 `me` 和 `cn` 两个环境
     */
    env?: 'net' | 'me' | 'cn' | 'com';

    /**
     * 是否为正式版本
     *
     * 正式版本会打tag
     */
    master?: boolean;

    /**
     * 是否检测分支，ci 环境是 checkout，无法检测
     */
    check?: boolean;

    /**
     * 立即发布
     *
     * 测试版本需要本地发布，正式版本通过ci发布
     */
    publish?: boolean;

    /**
     * 更新版本号
     */
    bumpVersions?: boolean;

    /**
     * 只跑通流程，不发布
     */
    dry?: boolean;
}

const REGISTRY = {
    net: 'http://npm.qiyuesuo.net/',
    me: 'http://npm.qiyuesuo.me/',
    cn: 'https://npm.qiyuesuo.cn/',
    com: 'https://npm.qiyuesuo.com/',
};

// 获取所有可发布的包名
const pkgs = getAllPublicPackages();

/** 可用的正式版本分支 */
const availableReleaseBranches = ['master'];

/** 可用的测试版本分支 */
const availableTestBranches = ['alpha', 'beta'];

const spinner = ora();

export const runAction = async (opts: PublishOpts) => {
    const branch = (await git.branch()).current;

    if (opts.check) {
        if (opts.master && !availableReleaseBranches.includes(branch)) {
            console.log('❌ 仅可在 master 分支发布正式版本');
            return;
        }
        else if (!opts.master && !availableTestBranches.includes(branch)) {
            console.log('❌ 仅可在 alpha 或 beta 分支发布测试版本');
            return;
        }
    }

    // 重试的时候不需要 bump 版本
    if (opts.bumpVersions) {
        // 1. bump versions
        await bumpVersions(opts);
    }

    if (opts.publish) {
        // 2. publish
        await publish(opts, branch);
    }

    if (opts.check && !opts.dry) {
        // 3. git push
        await gitPush(opts);
    }

    console.log('\n🎉 All Done!');
};


const bumpVersions = async (opts: PublishOpts) => {
    console.log('🎈 Bumping versions...\n');

    // 1. 生成一个 changeset
    const bump = opts.version;
    await genChangeset(bump, opts.master, pkgs);

    // 2. update version
    updateVersion();
    await replaceVersion();

    console.log('\n');
    spinner.succeed('Bump versions done!');
};


const gitPush = async (opts: PublishOpts) => {
    const pkgVersion = version();

    console.log('\n');
    spinner.start('📡 Pushing...');

    await git.add('.');
    await git.commit(`release: v${pkgVersion}`);
    await git.push();

    spinner.succeed('Push Done!');

    // 正式版本需要打 tag
    if (opts.master || /^([0-9]+\.){2}[0-9]+$/.test(pkgVersion)) {
        await git.addTag(`v${pkgVersion}`);
        await git.pushTags();
        console.log(`🏷️ git tag [v${pkgVersion}]`);
    }
};


const publish = async (opts: PublishOpts, branch: string) => {
    return buildAllPackages()
        .then(() => publishAllPackages(opts, branch));
};


const updateProgress = (progress: any) => {
    if (progress.stdout.includes('build: Done')) {
        const startIndex = progress.stdout.indexOf('packages');
        const endIndex = progress.stdout.indexOf('build: Done') + 12;
        spinner.succeed(`${++progress.percent}/${pkgs.length} ${progress.stdout.slice(startIndex, endIndex)}`);
        spinner.start('📦 Building...\n');
    }
};


const buildAllPackages = () => {
    console.log('\n');

    const progress = {
        stdout: '',
        percent: 0,
    };

    return new Promise<void>((resolve, reject) => {
        const child = sh.exec('pnpm build', { silent: true, async: true });

        spinner.start('📦 Building...\n');

        child.stdout?.on('data', (data) => {
            progress.stdout = data;
            updateProgress(progress);
        });

        child.stderr?.on('data', (error) => {
            console.log(error);
            git.clean('f');
            git.checkout('.');
            spinner.fail(`\n ❌ 1.Build Failed! \n ${progress.stdout}`);
            reject();
        });

        child.stdout?.on('close', () => {
            if (progress.percent === pkgs.length) {
                spinner.succeed('Build Success!');
                resolve();
            } else {
                git.clean('f');
                git.checkout('.');
                spinner.fail(`\n ❌ 2.Build Failed! \n ${progress.stdout}`);
                reject();
            }
        });
    });
};


const publishAllPackages = async (opts: PublishOpts, branch: string) => {
    const registry = `--registry=${REGISTRY[opts.env ?? 'me']}`;

    // 正式版本更新 latest tag
    // 测试版本按照分支（alpha或beta）打tag
    const tag = `--tag=${opts.master ? 'latest' : branch}`;

    const dry = opts.dry ? '--dry-run' : '';

    return new Promise<void>((resolve, reject) => {
        console.log('\n📡 Publishing...\n');
        spinner.start(' ');
        sh.exec(`pnpm --filter "@mitra-design/*" --filter "@mitra-modern/*" publish --no-git-checks --force ${dry} ${tag} ${registry}`, { silent: true }, (code, stdout, stderr) => {
            if (code === 0) {
                spinner.stop();
                console.log(`✨ [${version()}] Publish Success!`);
                resolve();
            } else {
                spinner.stop();
                console.log('stderr:\n', stderr);
                console.log('stdout:\n', stdout);
                console.log('\n❌ Publish Failed!');
                reject();
            }
        });
    });
};
