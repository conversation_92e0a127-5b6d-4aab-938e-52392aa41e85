import { Command } from 'commander';

import { runAction } from './actions.js';

const program = new Command();

program
    .description('quickly publish all packages to the specified environment')
    .option('--major', 'update major version')
    .option('--minor', 'update minor version')
    .option('--patch', 'update patch version')
    .option('--env <value>', 'publish environment')
    .option('--dry', 'dry run mode')
    .option('--master', 'publish official version')
    .option('--check', 'no check branch')
    .option('--bump-versions', 'bump all packages versions')
    .option('--publish', 'build and publish all packages')
    .action(async () => {
        runAction(program.opts());
    });

program.parse();
