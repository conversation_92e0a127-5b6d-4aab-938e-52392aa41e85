{"name": "@mitra-design/theme", "version": "2.9.5", "description": "组件库配套主题系统", "scripts": {"generate": "pnpm build:theme && tsx scripts/generate.ts", "parse-interface": "pnpm build:theme && tsx scripts/parseInterface.ts", "build": "pnpm build:theme && pnpm build:theme-style && pnpm build:tsc", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json", "build:theme": "mitra-design-cli build:component --vue=none", "build:theme-style": "mitra-design-cli build:theme-style"}, "main": "es/index.js", "files": ["es"], "sideEffects": ["*.css"], "devDependencies": {"@mitra-design/cli": "workspace:*"}, "dependencies": {"@mitra-design/color": "workspace:*", "@mitra-design/shared": "workspace:*"}}