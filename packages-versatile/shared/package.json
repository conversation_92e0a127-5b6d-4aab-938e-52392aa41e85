{"name": "@mitra-design/shared", "version": "2.9.5", "description": "mitra-design 通用工具包", "type": "module", "scripts": {"build": "pnpm build:component && pnpm build:tsc", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json"}, "exports": {"./composables": "./es/composables/index.js", "./constants": "./es/constants/index.js", "./date": "./es/date/index.js", "./directives": "./es/directives/index.js", "./global-configs": "./es/global-configs/index.js", "./styles/*": "./es/styles/*", "./utils": "./es/utils/index.js", "./vue-decorators": "./es/vue-decorators/index.js", "./src/*": "./src/*", "./es/*": "./es/*", "./package.json": "./package.json"}, "files": ["es", "src"], "typesVersions": {"*": {"composables": ["src/composables/index.ts"], "constants": ["src/constants/index.ts"], "date": ["src/date/index.ts"], "directives": ["src/directives/index.ts"], "global-configs": ["src/global-configs/index.ts"], "utils": ["src/utils/index.ts"], "vue-decorators": ["src/vue-decorators/index.ts"]}}, "peerDependencies": {"vue": "2.7.4", "vue-class-component": "7.2.6"}, "dependencies": {"dayjs": "1.10.4", "overlayscrollbars": "~2.4.7", "resize-observer-polyfill": "^1.5.1", "uppercamelcase": "3.0.0", "vue-demi": "0.14.10", "dexie": "3.2.7"}, "devDependencies": {"@mitra-design/cli": "workspace:*", "@types/uppercamelcase": "3.0.0"}}