{"name": "@mitra-modern/universal", "version": "2.9.5", "description": "", "scripts": {"build": "pnpm build:component && pnpm build:style", "build:style": "mitra-design-cli build:style --vue=3", "build:component": "mitra-design-cli build:component --vue=3", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json"}, "main": "dist/index.js", "files": ["dist"], "homepage": "https://mitra-design.qiyuesuo.net/web", "peerDependencies": {"vue": ">=3.4"}, "dependencies": {"@mitra-design/shared": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-modern/icons": "workspace:*", "@mitra-modern/internal": "workspace:*"}, "devDependencies": {"vue": "^3.4.0", "@mitra-design/cli": "workspace:*"}}