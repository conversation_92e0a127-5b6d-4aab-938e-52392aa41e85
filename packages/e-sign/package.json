{"name": "@mitra-design/e-sign", "version": "2.9.5", "description": "电子签场景业务组件", "scripts": {"build": "pnpm build:component && pnpm build:style && pnpm build:tsc", "build:style": "mitra-design-cli build:style", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json"}, "main": "es/index.js", "exports": {".": "./es/index.js", "./*": "./es/*", "./es/*": "./es/*", "./locale/lang": "./es/locale/lang/index.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"locale/lang": ["es/locale/lang/index.d.ts"]}}, "files": ["es"], "sideEffects": ["*.css"], "homepage": "https://mitra-design.qiyuesuo.net/e-sign", "peerDependencies": {"dayjs": "1.10.4", "lodash": "4.17.21", "vue": "2.7.4", "vue-class-component": "7.2.6", "vue-property-decorator": "9.1.2"}, "dependencies": {"@mitra-design/locale": "workspace:*", "@mitra-design/web": "workspace:*", "@mitra-design/icons": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/element-ui": "workspace:*", "@mitra-design/pro-components": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/helpers": "workspace:*"}, "devDependencies": {"@mitra-design/cli": "workspace:*"}}