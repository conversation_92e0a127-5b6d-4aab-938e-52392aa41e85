<template>
    <div>
        <mt-button @click="handleClick">点击打开</mt-button>
    </div>
</template>
<script setup lang="ts">
import { Message } from 'element-ui';
import { onMounted, ref } from 'vue';

import type { TagManagerProps } from '../types';

import { invoke_tagManager } from '@/tag-manager';
import { mockApis } from '@/tag-manager/__demos__/mock';

const features: TagManagerProps['features'] =  [
    ['COMMON', {
        icon: 'icon-personal-o-',
        name: '公共',
        iconStyle: { color: '#F0A128' },
        nameToolTip: '这里是公共标签',
        config: {
            getHistories: async (pageNow, pageSize, keyword) => {
                const res = await mockApis.apiFetchList({ type: 'COMMON', pageNow, pageSize, keyword });
                return [
                    res.list,
                    res.total,
                ];
            },
            getWritableHistories: async (pageNow, pageSize, keyword) => {
                const res = await mockApis.apiFetchList({ type: 'COMMON', pageNow, pageSize, keyword });
                return [
                    res.list,
                    res.total,
                ];
            },
            checkNew: (tagName) => mockApis.apiCheck(tagName),
            deleteTag: async (tag) => {
                const res = await mockApis.apiDelete(tag.value);
                res
                    ? Message.success('删除成功')
                    : Message.error('删除失败');
            },
            editTag: async (tag, newName) => {
                const res = await mockApis.apiEdit(tag.value, newName);
                res
                    ? Message.success('修改成功')
                    : Message.error('修改失败');
            },
        },
    }],
    ['PERSON', {
        icon: 'icon-plus',
        name: '个人',
        iconStyle: { color: '#015BEC' },
        nameToolTip: '这里是个人标签',
        config: {
            getHistories: async (pageNow, pageSize, keyword) => {
                const res = await mockApis.apiFetchList({ type: 'PERSON', pageNow, pageSize, keyword });
                return [
                    res.list,
                    res.total,
                ];
            },
            getWritableHistories: async (pageNow, pageSize, keyword) => {
                const res = await mockApis.apiFetchList({ type: 'PERSON', pageNow, pageSize, keyword });
                return [
                    res.list,
                    res.total,
                ];
            },
            checkNew: (tagName) => mockApis.apiCheck(tagName),
            deleteTag: async (tag) => {
                const res = await mockApis.apiDelete(tag.value);
                res
                    ? Message.success('删除成功')
                    : Message.error('删除失败');
            },
            editTag: async (tag, newName) => {
                const res = await mockApis.apiEdit(tag.value, newName);
                res
                    ? Message.success('修改成功')
                    : Message.error('修改失败');
            },
        },
    }],
];

const value = ref<TagManagerProps['value']>([]);

onMounted(async () => {
    value.value = await mockApis.apiFetchCurrentTags();
});


const handleConfirm = async (payload: TagManagerProps['value']) => {
    // 保存至服务端
    await mockApis.apiKeep(payload);

    // 从服务端拉取最新标签集合
    value.value = await mockApis.apiFetchCurrentTags();
};

const handleClick = async () => {
    invoke_tagManager({
        value: value.value,
        features,
        beforeConfirm: async (payload) => {
            await handleConfirm(payload);
        },
    });
};


</script>
