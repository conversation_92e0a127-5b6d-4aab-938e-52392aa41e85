import type { FilterList } from '@mitra-design/pro-components';
import type { TreeNode } from 'element-ui/types/tree';
import type { CreateElement } from 'vue';

import type { GlobalSettings } from './global-settings';

/**
 * 组织架构选择器 - 功能选项 - 配置
 *
 * 用于控制（一个功能的）展现及行为的配置
 * @interface Config
 */
export interface Config {
    /**
     * 获取组织结构树根节点的方法
     *
     * - 必须返回一个Promise对象
     */
    getTreeRoot?: () => Promise<any>;

    /**
     * ### 仅当 mode 为 `tree-sub` 时生效
     *
     * 树顶部筛选节点的数据，函数返回单条数据或者一个数组
     */
    treeTopFilterData?: () => TreeTopFilterNodeData | TreeTopFilterNodeData[] | undefined | false;

    /**
     * 获取组织结构树子节点的方法
     * - 必须返回一个Promise对象
     */
    getTreeChildren?: (id: string, type?: string) => Promise<any>;

    /**
     * 获取筛选后的组织结构树数据
     * - 当搜索关键词变化时 或 高级筛选对象变化时 调用
     * - 第一个参数为搜索关键词，第二个参数为高级筛选对象
     * - 必须返回一个Promise对象
     */
    getTreeFilterData?: (keywords?: string, filters?: any) => Promise<any>;

    /**
     * 组织结构树节点是否展示多选框
     * - 可接受 boolean 和 (data,node)=>boolean 两种类型的传参
     * - 传入boolean时，对全局生效
     * - 传入回调函数时，针对每个节点分别运算，若返回值为true则展示，否则不展示
     *
     * - !与选择器的选择结果绑定，若出现则默认为 多选
     * - treeCheckboxShow 与 treeRadioShow 必须至少有一个为false，否则将引发数据错误
     */
    treeCheckboxShow?: ((data: any, node: TreeNode<string, any>) => boolean) | boolean;

    /**
     * 组织结构树节点是否展示单选框
     * - 可接受 Boolean 和 (data,node)=>Boolean 两种类型的传参
     * - 传入Boolean时，对全局生效
     * - 传入回调函数时，针对每个节点分别运算，若返回值为true则展示，否则不展示
     *
     * - !与选择器的选择结果绑定，若出现则默认为 单选
     * - treeCheckboxShow 与 treeRadioShow 必须至少有一个为false，否则将引发数据错误
     */
    treeRadioShow?: ((data: any, node: TreeNode<string, any>) => boolean) | boolean;

    /**
     * 数据的唯一识别符
     *
     * - 在result列表中找到对应数据的唯一识别符（数据的某个key）
     * - result 设置或查找时可依赖此项
     */
    dataIdentifier?: string;

    /**
     * subjectType 到 featuresKey 的映射，当预设的 key 与 resultSetter 中返回的 subjectType 不一致时，
     * 需要传入改属性来保证 subjectType 可以被正确分配到 features 中
     *
     * - 默认情况下组件内部会调用 resultSetter 以取到 subjectType。
     * - mapping 的 key 为 features 对应的 key，value 为 subjectType => `{ [featureKey]: [subjectType] }`
     * - ### 当你的 subjectType 是动态的时候，必须传入此属性！
     * - 如果 subjectType 有多个，subjectTypeMapping 的 value 可以是数组
     * @example
     *
     * ```js
     * const selectorOrgFeatures = [
     *     ['DEPARTMENT', {
     *         config: {
     *             subjectTypeMapping: {
     *                 DEPARTMENT: ['COMPANY', 'CORPORATE', 'CHILD', 'SECTION'],
     *             }
     *         }
     *     }]
     * ]
     * ```
     */
    subjectTypeMapping?: Record<string, string | string[]>;

    /**
     * 自定义结果项的 icon，仅支持传入组件
     *
     * - 第二个参数为结果项的 item，仅有 resultSetter 中返回的字段
     */
    resultIcon?: (h: CreateElement, item: any) => JSX.Element;

    /**
     * 是否展示结果项中的 extra 区域，对应 result 字段中的 `subjectExtra` 属性
     */
    resultExtraShow?: boolean | ((data: any) => boolean);

    /**
     * 数据选择时（单选或多选框选中时）向最终结果中推入的数据的转化计算
     *
     * - 一般用在选择控件所绑定数据的set中
     * - 输入data（如果在树中，支持传入treeNode类型的第二个参数参与计算）
     * - 输出需要推入的内容
     */
    resultSetter?: (data: any, node?: TreeNode<string, any>) => any;

    /**
     * 多选 数据选择判定时，用于计算当前选择器是否被选中的函数
     *
     * - 一般用在选择控件所绑定数据的get中
     * - 输入当前节点的data和最终结果
     * - 输出布尔值
     * (多选控件不绑定任何值，则其默认自身独立、出入数值均为布尔型)
     */
    resultGetterCheckbox?: (result: any[], data: any, globalSettings: GlobalSettings, subjectType?: string) => boolean;

    /**
     * 选择器中的radio所绑定值（el-radio的label）的计算函数
     *
     * !请注意与resultGetterRadio保持对应
     */
    resultValueRadio?: (data: any, subjectType?: string) => string;

    /**
     * 单选 数据选择判定时，用于计算当前选择器是否被选中的函数
     *
     * - 一般用在选择控件所绑定数据的get中
     * - 输入结果
     * - 输出字符串
     * (单选控件需要设置绑定值，选择时v-model的值与绑定值相同则视为选中，因此为string类型)
     */
    resultGetterRadio?: (result: any[], globalSettings: GlobalSettings, subjectType?: string) => string;

    /**
     * 双栏选择/仅数据栏选择 中，获取数据列表的方法
     *
     * - 必须返回一个Promise对象
     * - Promise对象的resolve结果为一个数组，第一项为结果列表，第二项为结果总数
     * - 用法同 `infinite-scroll-list` 的 `get-data` 函数
     */
    getSubList?: (data: any, pageNo: number, pageSize: number) => Promise<[any[], number]>;

    /**
     * 数据列表的页码数，不传则默认为30
     */
    subListPageSize?: number;


    /**
     * 双栏选择/仅数据栏选择 中，数据列表中每项的内容
     * - 函数可以返回字符串或JSX
     */
    subListItemContent?: (h: CreateElement, item: any) => string | JSX.Element;



    /**
     * select-org-tree 自定义 tree-node
     */
    treeNodeContent?: (h: CreateElement, item: any, data: any) => string | JSX.Element;


    /**
     * 数据列表是否展示多选框
     * - 可接受 boolean 和 (data)=>boolean 两种类型的传参
     * - 传入boolean时，对全局生效
     * - 传入回调函数时，针对每个节点分别运算，若返回值为true则展示，否则不展示
     *
     * - !与选择器的选择结果绑定，若出现则默认为 多选
     * - treeCheckboxShow 与 treeRadioShow 必须至少有一个为false，否则将引发数据错误
     */
    subListCheckboxShow?: ((data: any) => boolean) | boolean;

    /**
     * 数据列表是否展示单选框
     * - 可接受 Boolean 和 (data)=>Boolean 两种类型的传参
     * - 传入Boolean时，对全局生效
     * - 传入回调函数时，针对每个节点分别运算，若返回值为true则展示，否则不展示
     *
     * - !与选择器的选择结果绑定，若出现则默认为 单选
     * - treeCheckboxShow 与 treeRadioShow 必须至少有一个为false，否则将引发数据错误
     */
    subListRadioShow?: ((data: any) => boolean) | boolean;

    /**
     * 是否展示组织机构树的检索框
     */
    treeFilterShow?: boolean;

    /**
     * 组织结构树检索框的占位提示文字
     */
    treeFilterPlaceholder?: string;

    /**
     * 在数据层面上控制tree的子节点是否展示（在获取数据后立即进行过滤）
     */
    treeNodeShow?: (data: any) => boolean;

    /**
     * 是否在已展示的树节点上展示选择子级的按钮
     */
    treeNodeSelectChildrenShow?: boolean;

    /**
     * 是否展示“包括下级”按钮
     */
    treeNodeIncludeChildrenShow?: ((data: any, node: TreeNode<string, any>) => boolean) | boolean;

    /**
     * 根据data和node计算是否为叶子节点
     */
    treeNodeIsLeaf?: (data: any, node: TreeNode<string, any>) => boolean;

    /**
     * 在树的节点中是否展示公司图标
     *
     * - 支持传入布尔值或一个函数返回布尔值，函数的参数是节点的 data 和 node
     */
    treeNodeCompanyIcon?: boolean | ((data: any, node: TreeNode<string, any>) => boolean);

    /**
     * 在树的节点中展示的文字的计算规则
     */
    treeNodeText?: (data: any, node: TreeNode<string, any>) => string;

    /**
     * 自定义树的过滤方法
     */
    treeNodeFilterMethod?: (value: string, data: any, node: TreeNode<string, any>) => boolean;

    /**
     * 是否展示数据列表检索
     */
    subSearchShow?: boolean;

    /**
     * 数据列表检索框的提示
     *
     * - 检索关键词为空时，展示在下拉面板中的提示文字
     */
    subSearchTips?: string | string[];

    /**
     * 数据列表检索数据为空时，显示的空白提示文字
     *
     * - 默认是「暂无检索结果」
     */
    subSearchListBlankText?: string;

    /**
     * 子列表数据为空时，显示的空白提示文字
     *
     * - 默认是「暂无数据」
     */
    subListBlankText?: string;

    /**
     * 获取数据列表检索结果的方法
     *
     * - 必须返回一个Promise对象
     * - Promise对象的resolve结果为一个数组，第一项为结果列表，第二项为结果总数
     */
    getSubSearchList?: (currentNodeData: any, keywords: any, pageNo: number, pageSize: number, restParams?: RestParams) => Promise<[any[], number]>;


    /**
     * 配置高级筛选，传入了即开启高级筛选
     */
    proFiltersConfig?: ProFiltersConfig;


    /**
     * 功能检索框是否展示
     */
    featureSearchShow?: boolean;


    /**
     * 功能检索框是否为单选模式
     */
    featureSearchSingleSelection?: boolean;

    /**
     * 获取功能检索结果列表
     *
     * - 必须返回一个Promise对象
     * - Promise对象的resolve结果为一个数组，第一项为结果列表，第二项为结果总数
     */
    getFeatureSearchList?: (keywords: any, pageNo: number, pageSize: number, restParams?: RestParams) => Promise<[any[], number]>;

    /**
     * 功能检索结果列表每项的内容
     * - 函数可以返回字符串或JSX
     */
    featureSearchItemContent?: (h: CreateElement, item: any) => string | JSX.Element;

    /**
     * 功能检索提示
     * - 检索关键词为空时，展示在下拉面板中的提示文字
     */
    featureSearchTips?: string | string[];


    /**
     * 选项是否禁用
     * - 可接受 boolean 和 (data,node)=>boolean 两种类型的传参
     * - 传入boolean时，对全局生效
     * - 对于'tree-only'类型，回调函数有两个参数(node,data)
     * - 对于'tree-sub'类型，回调函数有一个参数(node)
     */
    selectorDisabled?: ((data: any, node?: TreeNode<string, any>) => boolean) | boolean;

    /**
     * 选项被选中前的回调，可以用来决定当前选项是否能够被选中/反选
     * - 返回值为 true 时，允许操作
     * - 返回值为 false 时，不允许操作
     * - 参数为当前选项的选中状态和当前选项的数据
     */
    beforeSelect?: (isChecked: boolean, data: any) => Promise<boolean>;
}

export interface TreeTopFilterNodeData {
    /** 节点 id */
    id?: string;
    /** 节点显示名称 */
    name?: string;
    [key: string]: any;
}


interface ProFiltersConfig {
    /**
     * 高级筛选列表，接入了 ProFilters，类型同 ProFilters 的 FilterList
     */
    filterList: FilterList | (() => FilterList);

    /**
     * 高级筛选绑定的值，接收键值对类型，或者函数返回一个键值对类型
     */
    bindValue: Record<string, any> | (() => Record<string, any>);

    /**
     * 高级筛选默认值，如果传入了 defaultValue，则重置的时候内部会将 defaultValue 作为默认值
     * 否则会将 bindValue 作为默认值
     */
    defaultValue?: Record<string, any> | (() => Record<string, any>);

    /**
     * 默认展示的筛选项的 dataKey
     *
     * - 如果是数组类型的 dataKey，取第一个为 key
     */
    fixedFilterList?: string[];
}


/**
 * 获取数据列表检索结果的方法的剩余附加参数
 */
interface RestParams {
    /**
     * 高级筛选的值
     */
    filters?: any;
}




export interface Option {
    /**
     * 选项的类型
     * - none：没有选择组件，不能选择
     * - radio：单选
     * - checkbox：多选
     */
    type: 'none' | 'radio' | 'checkbox';

    /**
     * 选项的禁用状态
     */
    selectorDisabled: boolean;

    /**
     * 向最终结果中推入的数据的转化计算
     */
    resultSetter: any;

    /**
     * 多选的选项是否选中（最终的计算结果）
     */
    resultGetterCheckbox?: boolean;

    /**
     * 单选框的value（最终的计算结果）
     */
    resultValueRadio?: string;

    /**
     * 单选框当前的选中值（最终的计算结果）
     */
    resultGetterRadio?: string;

    /**
     * 用于移除数据时在结果列表中定位数据的查找函数（将作为findIndex的参数使用）
     * - 参数与findIndex的回调函数参数一致
     * - currentVal：当前检查的项的值
     * - index：当前检查项的序号
     * - arr：待检查的数组
     */
    finder: (currentVal: any, index: number, arr: any[]) => boolean;

    /**
     * 选项被选中前的回调，可以用来决定当前选项是否能够被选中
     * - 返回值为 true 时，允许选中
     * - 返回值为 false 时，阻止选中
     * - 参数为当前选项的选中状态
     */
    beforeSelect?: (isChecked: boolean) => Promise<boolean> | undefined;
}
