<template>
    <div :class="bem()">
        <div
            v-if="config?.treeFilterShow"
            :class="bem('search')"
        >
            <selector-org-filters
                v-model="keywords"
                :filters.sync="filters"
                :config="config"
                :popover-width="464"
                popover-placement="bottom-end"
                :placeholder="config.treeFilterPlaceholder"
                @search="onKeywordsChange"
                @confirm="onKeywordsChange"
                @reset="onReset"
            />
        </div>
        <div
            v-scroll
            v-loading="loading"
            :class="bem('container')"
            element-loading-spinner="el-icon-loading"
        >
            <div :class="bem('wrapper')">
                <template v-if="filterNodeData.length > 0">
                    <div
                        v-for="data of filterNodeData"
                        :key="data.id"
                        :class="bem('tree-top-filter', { active: data.active })"
                        @click="onClickTopFilter(data)"
                    >
                        {{ data.name }}
                    </div>
                </template>
                <el-tree
                    ref="tree"
                    v-bind="elTreeProps"
                    v-on="elTreeEvents"
                >
                    <template #default="{ node, data }">
                        <selector-tree-node-wrapper
                            v-model="currentValue"
                            :node="node"
                            :data="data"
                            :config="config"
                        />
                    </template>
                </el-tree>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import type {  ElTree } from '@mitra-design/element-ui';
import { vScroll } from '@mitra-design/internal';
import { vLoading } from '@mitra-design/web';
import type { TreeData, TreeNode } from 'element-ui/types/tree';

import type { Config, TreeTopFilterNodeData, featureMode } from '../../_model';
import SelectorOrgFilters from '../filters/filters.vue';

import SelectorTreeNodeWrapper from './tree-node-wrapper';

import { I18n } from '@/locale';


/**
 * 组织架构选择器 - 组织结构树
 * @class SelectorOrgTree
 * @augments {Mixins(twowayFactory<any[]>())}
 */
@Bem('selector-org-tree')
@I18n('selector-org')
@Component({
    components: {
        SelectorTreeNodeWrapper,
        SelectorOrgFilters,
    },
    directives: {
        loading: vLoading,
        scroll: vScroll,
    },
})
export default class SelectorOrgTree extends Mixins(twowayFactory<any[]>()) {
    /**
     * 功能配置
     */
    @Prop()
    readonly config?: Config;

    /**
     * 功能模式
     */
    @Prop()
    mode!: featureMode;

    /**
     * 当前选中的树节点数据
     */
    @PropSync('currentNodeData')
    syncedCurrentNodeData!: any;

    /**
     * $ref: el-tree 组件
     */
    @Ref('tree')
    refTree!: ElTree;

    /**
     * 树的数据
     */
    treeData: TreeData[] = new Array();

    /**
     * 加载中标记
     */
    loading = false;

    /**
     * 默认展开节点
     */
    defaultExpandedKeys = new Array<string>();

    /**
     * 检索框的绑定值
     */
    keywords = '';

    /**
     * 高级筛选的绑定值
     */
    filters = null;

    /**
     * 顶部筛选节点是否被选中
     */
    topFilterActive = false;

    /**
     * 顶部筛选节点的数据
     */
    filterNodeData: TreeTopFilterNodeData[] = [];

    /**
     * el-tree 的配置项
     * @readonly
     */
    get elTreeProps() {
        return {
            data: this.treeData,
            nodeKey: 'id',
            props: {
                isLeaf: (data: any, node: TreeNode<string, any>) => {
                    return this.config?.treeNodeIsLeaf?.(data, node);
                },
            },
            lazy: true,
            emptyText: this.keywords ? this.t('emptySearch') : this.t('emptyOrganization'),
            load: this.fetchChildren,
            highlightCurrent: this.mode === 'tree-sub',
            defaultExpandedKeys: this.defaultExpandedKeys,
            expandOnClickNode: false,
            filterNodeMethod: this.config?.treeNodeFilterMethod,
        };
    }

    /**
     * el-tree的绑定事件
     * @readonly
     */
    get elTreeEvents() {
        return {
            'current-change': (data: any, node: TreeNode<string, any>) => {
                this.syncedCurrentNodeData = data;
                this.filterNodeData = this.filterNodeData.map(fd => {
                    fd.active = false;
                    return fd;
                });
            },
        };
    }

    /**
     * el-tree 使用的 获取树的子节点的函数
     * @param {TreeNode<string, any>} node 树节点
     * @param {Function} resolve 抛出返回值的函数
     */
    fetchChildren(node: TreeNode<string, any>, resolve: (...args: any[]) => any[]) {
        if (node.level === 0) resolve([]);
        else if (node.level === 1 && this.config?.treeNodeIsLeaf?.(node.data, node)) {
            resolve([]);
        } else {
            const { id, children, type } = node.data;
            if (children) {
                resolve(children);
                this.refTree.filter(this.keywords);
            } else {
                this.config?.getTreeChildren?.(id, type).then((data) => {
                    const filteredData = data.filter(this.config?.treeNodeShow);
                    resolve(filteredData);
                    this.refTree.filter(this.keywords);
                });
            }

        }
    }

    async onKeywordsChange() {
        this.loading = true;

        try {
            if (this.keywords || this.filters) {
                const data = await this.config?.getTreeFilterData?.(this.keywords, this.filters);

                if (!data?.length) {
                    this.refTree.filter(this.keywords);
                    this.loading = false;
                    return;
                }

                const handleOpenTreeNode = async (child: any) => {
                    const node = this.refTree.getNode(child.id);
                    if (node) {
                        let data = [];

                        if (!node.loaded && child.child) {
                            const currChild = node.data?.children;

                            data = currChild?.length ? currChild : await this.config?.getTreeChildren?.(child.id);
                            this.refTree.updateKeyChildren(child.id, data);

                            node.isLeaf = false;
                            node.loaded = true;
                            node.expanded = true;

                        } else {
                            node.expanded = true;
                        }
                    }

                    return Promise.resolve();
                };

                /**
                 *
                 */
                // eslint-disable-next-line no-inner-declarations
                function flatTreeNode(node?: any[]): any[] {
                    if (!node?.length) return [];

                    const result = node.flatMap((item) => {
                        const children = item.children;

                        Reflect.deleteProperty(item, 'children');
                        return [item, ...flatTreeNode(children)];
                    });

                    return result;
                }

                const handlerArray = flatTreeNode(data).map(node => () => handleOpenTreeNode(node));

                /**
                 *
                 */
                // eslint-disable-next-line no-inner-declarations
                function* gen() {
                    // eslint-disable-next-line @typescript-eslint/prefer-for-of
                    for (let i = 0; i < handlerArray.length; i++) {
                        yield handlerArray[i];
                    }
                }

                const genInstance = gen();

                const execOpenTreeNode = async () => {
                    const { value, done } = genInstance.next();
                    if (!done) {
                        await value?.();
                        await execOpenTreeNode();
                    }
                };

                await execOpenTreeNode();

                this.refTree.filter(this.keywords);

            } else {
                this.refTree.filter(this.keywords);
            }
        } catch (error) {
            console.error('[select-org] error:', error);
        }

        this.loading = false;
    }

    /**
     * 获取树的根节点
     */
    fetchTreeRoot() {
        if (this.config?.getTreeRoot) {
            this.loading = true;
            this.config.getTreeRoot().then((result) => {
                console.log('get tree root');
                const data = Array.isArray(result) ? result[0] : result;
                this.defaultExpandedKeys = [data.id];
                this.treeData = Array.isArray(result) ? result : [result];

                // 没有顶部筛选节点时，选中根节点
                if (this.mode === 'tree-sub' && !this.filterNodeData.length) {
                    this.$nextTick(() => {
                        this.syncedCurrentNodeData = data;
                        this.refTree.setCurrentKey(data.id);
                    });
                }

            }).finally(() => {
                this.loading = false;
            });
        }
    }

    async onClickTopFilter(data?: TreeTopFilterNodeData) {
        if (!data) return;

        // 将当前选中数据改为「顶部筛选节点」
        this.syncedCurrentNodeData = {
            ...data,
            id: data.id,
            companyName: data.name,
            isFilterNode: true, // 标识为筛选节点
        };

        // 取消树中高亮的节点
        this.refTree.setCurrentKey(null);

        // 改变高亮节点
        this.filterNodeData = this.filterNodeData.map(item => {
            item.active = item.id === data.id;
            return item;
        });
    }

    fetchTopFilterData() {
        let data = this.config?.treeTopFilterData?.();

        if (!data) return;

        if (!Array.isArray(data)) {
            data = [data];
        }

        // 如果存在顶部筛选节点，则默认选中第一个
        const defaultActiveNode = data[0];
        defaultActiveNode.active = true;

        this.filterNodeData = data as TreeTopFilterNodeData[];

        this.syncedCurrentNodeData = {
            ...defaultActiveNode,
            id: defaultActiveNode.id,
            companyName: defaultActiveNode.name,
            isFilterNode: true, // 标识为筛选节点
        };
    }

    /**
     * 高级筛选中的重置事件
     *
     * - 当搜索关键词为空时，需要手动触发一次搜索
     * - 当搜索关键词不为空时，内部会触发 `search` 事件
     */
    onReset() {
        if (!this.keywords) {
            this.onKeywordsChange();
        }
    }

    mounted() {
        if (this.mode === 'tree-sub') {
            this.fetchTopFilterData();
        }

        this.fetchTreeRoot();
    }
}

</script>
