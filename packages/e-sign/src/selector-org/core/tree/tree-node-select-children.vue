<template>
    <span
        v-if="node.loaded && !node.isLeaf && exitsChildrenSelectable"
        :class="bem()"
        @click.stop="handleBtnClick"
    >
        <el-tooltip
            placement="top"
            :enterable="false"
            :content="`${allChildrenSelected ? t('cancel') : ''}${t('selectSubOrg')}`"
        >
            <mt-button
                size="mini"
                variant="text-major"
                :class="bem('select-btn')"
            >
                {{ allChildrenSelected ? t('cancelSelect') : t('selectChildren') }}
            </mt-button>
        </el-tooltip>
    </span>
</template>
<script lang="ts">
import { Button } from '@mitra-design/web';
import type { TreeNode } from 'element-ui/types/tree';

import type { Config } from '../../_model';
import { resultAddItem, resultRemoveItem } from '../../resolver';
import { globalSettingsInjectorMixin } from '../_mixins';

import { I18n } from '@/locale';

/**
 * 组织架构选择器 - 组织结构树 - 树节点 - 选择下级组织架构按钮
 *
 * @export
 * @class SelectorOrgTreeNodeSelectChildren
 * @augments {Mixins(twowayFactory<any[]>())}
 */
@Bem('selector-org-tree-node-select-children')
@I18n('selector-org')
@Component({
    components: {
        'mt-button': Button,
    },
})
export default class SelectorOrgTreeNodeSelectChildren extends Mixins(twowayFactory<any[]>(), globalSettingsInjectorMixin) {
    /**
     * 树节点的数据
     *
     * @type {*}
     * @readonly
     */
    @Prop()
    readonly data: any;


    /**
     * 树节点的节点对象（来源于el-tree）
     *
     * @type {TreeNode<string, any>}
     * @readonly
     */
    @Prop()
    readonly node!: TreeNode<string, any>;

    /**
     * 功能配置
     *
     * @type {Config}
     * @readonly
     */
    @Prop()
    readonly config!: Config;

    /**
     * 是否存在可以被选择的子节点
     *
     * - 若不存在则不展示组件
     *
     * @readonly
     */
    get exitsChildrenSelectable() {
        for (let childNode of this.node.childNodes) {
            const checkboxShow = this.calcCheckboxShow(childNode.data, childNode);

            if (checkboxShow) {
                return true;
            }
        }
        return false;
    }

    /**
     * 直接子级节点是否已被全部选中
     *
     * - 若为否，则按钮为选择下级全部组织结构
     * - 若为是，则按钮为全部取消下级选择
     *
     * @readonly
     */
    get allChildrenSelected() {
        for (let childNode of this.node.childNodes) {
            const checkboxShow = this.calcCheckboxShow(childNode.data, childNode);
            const checkboxChecked = this.calcCheckboxChecked(childNode.data, childNode);

            if (checkboxShow && !checkboxChecked) {
                return false;
            }
        }
        return true;
    }

    /**
     * 为每一个子级节点 计算其checkbox是否展示
     *
     * - 传入节点相关的数据，调用config.treeCheckboxShow
     *
     * @param {*} data 节点的数据
     * @param {TreeNode<string, any>} node 树节点
     * @returns 是否展示checkbox
     */
    calcCheckboxShow(data: any, node: TreeNode<string, any>) {
        const { treeCheckboxShow } = this.config;
        if (typeof (treeCheckboxShow) === 'boolean') return treeCheckboxShow;
        else if (typeof (treeCheckboxShow) === 'function') return treeCheckboxShow(data, node);

        return false;
    }

    /**
     * 为每一个子级节点 计算其选择器是否禁用是否展示
     *
     * - 传入节点相关的数据，调用config.disabled
     *
     * @param {*} data 节点的数据
     * @param {TreeNode<string, any>} node 树节点
     * @returns 是否展示checkbox
     */
    calcDisabled(data: any, node: TreeNode<string, any>) {
        const { selectorDisabled } = this.config;
        if (typeof (selectorDisabled) === 'boolean') return selectorDisabled;
        else if (typeof (selectorDisabled) === 'function') return selectorDisabled(data, node);

        return false;
    }

    /**
     * 为每一个子级节点 计算其checkbox是否已被选中
     *
     * - - 传入节点相关的数据，调用config.resultGetterCheckbox
     *
     * @param {*} data 节点的数据
     * @param {TreeNode<string, any>} node 树节点
     * @returns checkbox是否已被选中
     */
    calcCheckboxChecked(data: any, node: TreeNode<string, any>) {
        return this.config?.resultGetterCheckbox?.(this.currentValue, data, this.getCombinedGlobalSettings());
    }

    /**
     * 处理选择下级架构按钮（本组件）的点击事件
     *
     * 若子节点未全部选中，则点击时执行选择全部的功能，若子级节点是否已被全部选中，则点击时全部取消选中
     *
     * - 无论选中还是取消，均会遵循globalSettings中关于结果数据的操作控制，与依次点击选项本身进行选择/取消选择时的行为会保持一致
     */
    handleBtnClick() {
        const { childNodes } = this.node;

        if (!this.allChildrenSelected) {
            for (let childNode of childNodes) {
                const checkboxShow = this.calcCheckboxShow(childNode.data, childNode);  // 这行似乎已经不需要了
                const checkboxChecked = this.calcCheckboxChecked(childNode.data, childNode);
                const checkboxDisabled = this.calcDisabled(childNode.data, childNode);

                if (checkboxShow && !checkboxChecked && !checkboxDisabled) {
                    const item = this.config?.resultSetter?.(childNode.data, childNode);

                    if (item)
                        resultAddItem(this.currentValue, item, this.getCombinedGlobalSettings());
                }
            }
        }
        else {
            for (let childNode of childNodes) {
                const checkboxShow = this.calcCheckboxShow(childNode.data, childNode); // 这行似乎已经不需要了
                const checkboxChecked = this.calcCheckboxChecked(childNode.data, childNode);
                const checkboxDisabled = this.calcDisabled(childNode.data, childNode);

                if (checkboxShow && checkboxChecked && !checkboxDisabled) {
                    const checker = (item: any) => item?.[this.getCombinedGlobalSettings()?.resultIdentifier ?? ''] === childNode.data?.[this.config?.dataIdentifier ?? '']; // !，表示认定此时一定有此值
                    resultRemoveItem(this.currentValue, checker, this.getCombinedGlobalSettings());
                }
            }
        }
    }
}

</script>
