<template>
    <div :class="bem()">
        <selector-org-option
            v-model="currentValue"
            :option="option"
        >
            <!-- text -->
            <span :class="bem('text-wrap')">
                <span
                    :class="bem('text')"
                    :title="treeNodeText"
                >
                    {{ treeNodeText }}
                </span>
                <el-tooltip
                    v-if="companyIconShow"
                    placement="top"
                    :content="t('companyIconTooltips')"
                >
                    <icon-authenticated-fill :class="bem('icon-auth')" />
                </el-tooltip>
            </span>

            <div :class="bem('select-btn')">
                <selector-org-tree-node-select-children
                    v-if="config.treeNodeSelectChildrenShow"
                    v-model="currentValue"
                    v-bind="{ data, node, config }"
                />

                <selector-org-tree-node-include-children
                    v-if="includeChildrenShow && !isDisabled"
                    v-model="currentValue"
                    v-bind="{ data, node, config }"
                />
            </div>
        </selector-org-option>
    </div>
</template>
<script lang="ts">
import { SvgIcon } from '@mitra-design/web';
import type { TreeNode } from 'element-ui/types/tree';
import { Component, Prop, Mixins } from 'vue-property-decorator';

import type { Config, Option } from '../../_model';
import { globalSettingsInjectorMixin } from '../_mixins';
import SelectorOrgOption from '../option/option.vue';

import SelectorOrgTreeNodeIncludeChildren from './tree-node-include-children.vue';
import SelectorOrgTreeNodeSelectChildren from './tree-node-select-children.vue';

import { I18n } from '@/locale';

/**
 * 组织架构选择器 - 组织结构树 - 树节点
 * @export
 * @class SelectorOrgTreeNode
 * @augments {Mixins(twowayFactory<any[]>())}
 */
@Bem('selector-org-tree-node')
@I18n('selector-org')
@Component({
    components: {
        SelectorOrgTreeNodeSelectChildren,
        SelectorOrgTreeNodeIncludeChildren,
        SelectorOrgOption,
        'mt-svg-icon': SvgIcon,
    },
})
export default class SelectorOrgTreeNode extends Mixins(twowayFactory<any[]>(), globalSettingsInjectorMixin) {
    /**
     * 树节点的数据
     * @type {*}
     */
    @Prop()
    readonly data: any;


    /**
     * 树节点的节点对象（来源于el-tree）
     * @type {TreeNode<string, any>}
     */
    @Prop()
    readonly node!: TreeNode<string, any>;

    /**
     * 功能配置
     * @type {Config}
     */
    @Prop()
    readonly config!: Config;

    /**
     * 是否禁用选择
     *
     */
    @Prop()
    isDisabled!: boolean;

    @Prop()
    checkboxShow !: boolean;


    get treeNodeText() {
        return this.config.treeNodeText!(this.data, this.node);
    }


    /**
     * 是否展示RadioButton
     * @readonly
     */
    get radioShow() {
        const { treeRadioShow } = this.config;
        if (typeof (treeRadioShow) === 'boolean') return treeRadioShow;
        else if (typeof (treeRadioShow) === 'function') return treeRadioShow(this.data, this.node);

        return false;
    }


    /**
     * 传递给selector-org-option的选择项属性
     * @readonly
     */
    get option() {
        const resultSetter = this.config?.resultSetter?.(this.data, this.node);
        const beforeSelect = this.config?.beforeSelect;
        const option: Option = {
            type: this.radioShow ? 'radio' : this.checkboxShow ? 'checkbox' : 'none',
            selectorDisabled: this.isDisabled,
            beforeSelect: (isChecked) => beforeSelect?.(isChecked, this.data) ?? Promise.resolve(true),
            resultSetter: resultSetter,
            resultGetterCheckbox: this.config?.resultGetterCheckbox?.(this.currentValue ?? [], this.data, this.getCombinedGlobalSettings(), resultSetter.subjectType),
            resultValueRadio: this.config?.resultValueRadio?.(this.data, resultSetter.subjectType),
            resultGetterRadio: this.config?.resultGetterRadio?.(this.currentValue ?? [], this.getCombinedGlobalSettings(), resultSetter.subjectType),
            finder: item => item?.[this.getCombinedGlobalSettings().resultIdentifier!] === this.data?.[this.config.dataIdentifier!] && item?.subjectType === resultSetter.subjectType,
        };
        return option;
    }

    /**
     * 是否展示已认证公司的标记
     */
    get companyIconShow() {
        return typeof this.config?.treeNodeCompanyIcon === 'function'
            ? this.config.treeNodeCompanyIcon?.(this.data, this.node)
            : typeof (this.config?.treeNodeCompanyIcon) === 'boolean'
                ? this.config.treeNodeCompanyIcon
                : false;
    }

    get includeChildrenShow() {
        return typeof (this.config?.treeNodeIncludeChildrenShow) === 'function'
            ? this.config.treeNodeIncludeChildrenShow?.(this.data, this.node)
            : typeof (this.config?.treeNodeIncludeChildrenShow) === 'boolean'
                ? this.config.treeNodeIncludeChildrenShow
                : false;
    }
}

</script>
