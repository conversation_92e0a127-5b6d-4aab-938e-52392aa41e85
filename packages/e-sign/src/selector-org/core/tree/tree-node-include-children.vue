<template>
    <span
        :class="bem()"
        @click.stop="handleBtnClick"
    >
        <el-tooltip
            placement="top"
            :enterable="false"
            :content="toolTips"
        >
            <mt-button
                size="mini"
                variant="text-major"
                :class="bem('select-btn')"
            >
                {{ !isActive ? t('includeChildren') : t('cancelIncludeChildren') }}
            </mt-button>
        </el-tooltip>
    </span>
</template>
<script lang="ts">
import { SvgIcon } from '@mitra-design/web';
import type { TreeNode } from 'element-ui/types/tree';

import type { Config } from '../../_model';
import { resultModifyAttrs } from '../../resolver';
import { globalSettingsInjectorMixin } from '../_mixins';

import { I18n } from '@/locale';


@Bem('selector-org-tree-node-include-children')
@I18n('selector-org')
@Component({
    components: {
        'mt-svg-icon': SvgIcon,
    },
})
export default class SelectorOrgTreeNodeIncludeChildren extends Mixins(twowayFactory<any[]>(), globalSettingsInjectorMixin) {
    /**
     * 树节点的数据
     * @type {*}
     * @readonly
     */
    @Prop()
    readonly data: any;


    /**
     * 树节点的节点对象（来源于el-tree）
     * @type {TreeNode<string, any>}
     * @readonly
     */
    @Prop()
    readonly node!: TreeNode<string, any>;

    /**
     * 功能配置
     * @type {Config}
     * @readonly
     */
    @Prop()
    readonly config!: Config;

    get isNodeSelected() {
        return this.currentValue?.some((item: any) => item?.[this.getCombinedGlobalSettings()?.resultIdentifier ?? ''] === this.data?.[this.config?.dataIdentifier ?? '']);
    }

    get isActive() {
        return this.currentValue?.some((item: any) =>
            item?.[this.getCombinedGlobalSettings()?.resultIdentifier ?? ''] === this.data?.[this.config?.dataIdentifier ?? '']
            && item.includeChildren
        );
    }

    get toolTips() {
        return !this.isNodeSelected
            ? this.t('selectIncludeChildren')
            : !this.isActive
                ? this.t('setIncludeChildren')
                : this.t('cancelSetIncludeChildren');
    }

    handleBtnClick() {
        const resultFinder = (item: any) => item?.[this.getCombinedGlobalSettings()?.resultIdentifier ?? ''] === this.data?.[this.config?.dataIdentifier ?? ''];
        const attrs = { includeChildren: !this.isActive };
        const result = this.config?.resultSetter?.(this.data, this.node);

        resultModifyAttrs(this.currentValue, result, resultFinder, attrs, this.getCombinedGlobalSettings());
    }
}
</script>
