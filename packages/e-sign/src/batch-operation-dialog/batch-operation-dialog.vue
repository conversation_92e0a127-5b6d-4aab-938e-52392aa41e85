<template>
    <el-dialog
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        width="560px"
        :custom-class="bem()"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        :append-to-body="true"
        :before-close="handleClose"
    >
        <template #title>
            <span :class="bem('dialog-title')">
                <icon-left
                    v-if="isViewAllData"
                    :class="bem('back-icon')"
                    :size="18"
                    @click="changeViewStatus"
                />
                <span>{{ dialogTitle }}</span>
            </span>
        </template>
        <template v-if="isViewAllData">
            <all-data-list
                v-bind="{ ...$attrs, dialogTitle, type, batchResult, unit, taskDetailTableColumns, getTaskDetailTableData }"
                :task="viewAllTaskDetail"
            />
        </template>
        <template v-else-if="showHistoryList">
            <history-list
                v-bind="{ ...$attrs, dialogTitle, type, batchResult, unit, getHistoryTableData, historyTableColumns, historyOperationBtnList }"
            />
        </template>
        <template v-else-if="emptyData">
            <no-data />
        </template>
        <template v-else>
            <dialog-body
                v-loading="{ active: loading, text: loadingText, background: 'white' }"
                v-bind="{ ...$attrs, dialogTitle, type, batchResult, count, taskCount, status, unit, isMultipleTask, needFilterTable, taskDetailTableColumns, getTaskDetailTableData, operationBtnList, readMark, customViewAllBtn, cancelTask }"
                @view-all="viewAll"
                @do-close-dialog="afterCancelClose"
            />
            <span
                v-if="loading || status === 'in-progress'"
                slot="footer"
            >
                <span
                    :class="bem('footer-text')"
                    @click="fold"
                >
                    <icon-pushpin
                        :size="14"
                        :class="bem('footer-icon')"
                    />
                    {{ t('fixToHeader') }}
                </span>
            </span>
        </template>
    </el-dialog>
</template>
<script lang="ts">
export default {
    directives: {
        loading: vLoading,
    },
};
</script>
<script setup lang="ts">
import { ElDialog } from '@mitra-design/element-ui';
import type { BaseCountMap } from '@mitra-design/internal';
import { useTaskData } from '@mitra-design/internal';
import { vLoading } from '@mitra-design/web';
import { useVModel } from '@vueuse/core';

import AllDataList from './components/all-data-list/index.vue';
import DialogBody from './components/dialog-body/index.vue';
import HistoryList from './components/history-list/index.vue';
import NoData from './components/status/no-data.vue';
import { batchOperationDialogInjectKey } from './provide-inject-keys';
import type { BatchOperationDialogProps, BatchOperationDialogEvents } from './types';

import { useBem } from '@/composables';
import { useI18n } from '@/locale';

const componentName = 'batch-operation-dialog';

const props = withDefaults(defineProps<BatchOperationDialogProps>(), {
    loopTime: 3000,
    unit: '份',
    showHistoryList: false,
    fieldMap: () => {
        return {
            successField: 'success',
            restField: 'rest',
            totalField: 'total',
            failureField: 'failure',
            taskTypeField: 'batchTaskType',
            batchIdField: 'id',
            timeField: 'createTime',
        };
    },
});

const emit = defineEmits<BatchOperationDialogEvents>();

const { bem } = useBem(componentName);
const { t } = useI18n(componentName);


const {
    timer,
    loading,
    batchResult,
    emptyData,
    count,
    status,
    isMultipleTask,
    startLoop,
} = useTaskData(props);

const dialogVisible = useVModel(props, 'visible', emit, { eventName: 'update:visible' });

const refreshTableData  = ref(false);
const viewAllTaskDetail = ref<BaseCountMap>({ id: '', success: 0, failure: 0, total: 0, rest: 0 });
const isViewAllData = ref(false);

const taskCount = computed(() => {
    return batchResult.value?.list.length || 1;
});

const loadingText = computed(() => {
    return t('loadingText');
});


const viewAll = (task: BaseCountMap) => {
    isViewAllData.value = true;
    viewAllTaskDetail.value = task;
};

const changeViewStatus = () => {
    isViewAllData.value = false;
};


watch(
    () => dialogVisible.value,
    (val: boolean) => {
        if (!val) {
            window.clearInterval(timer.value);
        } else if (!props.showHistoryList) {
            startLoop();
        }
    },
    { immediate: true }
);

const closeDialog = () => {
    window.clearInterval(timer.value);
    isViewAllData.value = false;
    dialogVisible.value = false;
};

const handleClose = () => {
    if (status.value === 'finish') {
        readMarkAll();
    } else {
        closeDialog();
    }
};

// 标记已读
const readMarkAll = () => {
    if (!props.showHistoryList && props.readMark) {
        props.readMark(props.type);
        emit('dialog-close');
    }
    closeDialog();
};

const fold = () => {
    emit('change-popover-status', false);
    closeDialog();
};

const afterCancelClose = () => {
    emit('dialog-close');
    closeDialog();
};

const doRefreshBatchTaskDetail = (status: boolean) => {
    refreshTableData.value = status;
};

defineExpose({
    closeDialog,
    doRefreshBatchTaskDetail,
});

// ====================== Provide ======================

provide(batchOperationDialogInjectKey, {

    refreshBatchTask: () => {
        return {
            refreshTableData: refreshTableData.value,
        };
    },
});
</script>
