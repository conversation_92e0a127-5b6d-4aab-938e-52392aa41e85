import { debounce, head, tail } from 'lodash';
import Vue from 'vue';

import { addClass, removeClass, on, off, getStyle } from '@/utils/dom';
import { generateId } from '@/utils/util';
import Popper from '@/utils/vue-popper';

export default {
    name: 'el-tooltip',

    mixins: [Popper],

    props: {
        openDelay: {
            type: Number,
            default: 300,
        },
        disabled: Boolean,
        manual: Boolean,
        effect: {
            type: String,
            default: 'dark',
        },
        arrowOffset: {
            type: Number,
            default: 0,
        },
        popperClass: String,
        content: String,
        visibleArrow: {
            default: true,
        },
        transition: {
            type: String,
            default: 'el-fade-in-linear',
        },
        popperOptions: {
            default() {
                return {
                    boundariesPadding: 10,
                    gpuAcceleration: false,
                };
            },
        },
        enterable: {
            type: Boolean,
            default: true,
        },
        hideAfter: {
            type: Number,
            default: 0,
        },
        tabindex: {
            type: Number,
            default: 0,
        },
        listContent: {
            type: [String, Array],
            default: '',
        },
        showListMarker: {
            type: Boolean,
            default: true,
        },
        placement: {
            type: String,
            default: 'top',
        },
        onlyActiveWhenOverflow: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            tooltipId: `el-tooltip-${generateId()}`,
            timeoutPending: null,
            focusing: false,
        };
    },
    computed: {
        wrappedListContent() {
            if (!this.listContent) return [];
            else if (!Array.isArray(this.listContent)) return [this.listContent];
            return this.listContent;
        },

        firstContent() {
            return head(this.wrappedListContent);
        },

        tailContent() {
            return tail(this.wrappedListContent);
        },
    },
    watch: {
        focusing(val) {
            if (val) {
                addClass(this.referenceElm, 'focusing');
            } else {
                removeClass(this.referenceElm, 'focusing');
            }
        },
    },
    beforeCreate() {
        if (this.$isServer) return;

        this.popperVM = new Vue({
            data: { node: '' },
            render(h) {
                return this.node;
            },
        }).$mount();

        this.debounceClose = debounce(() => this.handleClosePopper(), 200);
    },

    mounted() {
        this.referenceElm = this.$el;
        if (this.$el.nodeType === 1) {
            this.$el.setAttribute('aria-describedby', this.tooltipId);
            this.$el.setAttribute('tabindex', this.tabindex);
            on(this.referenceElm, 'mouseenter', this.show);
            on(this.referenceElm, 'mouseleave', this.hide);
            on(this.referenceElm, 'focus', () => {
                if (!this.$slots.default?.length) {
                    this.handleFocus();
                    return;
                }
                const instance = this.$slots.default[0].componentInstance;
                if (instance?.focus) {
                    instance.focus();
                } else {
                    this.handleFocus();
                }
            });
            on(this.referenceElm, 'blur', this.handleBlur);
            on(this.referenceElm, 'click', this.removeFocusing);
        }
        // fix issue https://github.com/ElemeFE/element/issues/14424
        if (this.value && this.popperVM) {
            this.popperVM.$nextTick(() => {
                if (this.value) {
                    this.updatePopper();
                }
            });
        }
    },

    beforeDestroy() {
        this.popperVM?.$destroy();
    },

    destroyed() {
        const reference = this.referenceElm;
        if (reference.nodeType === 1) {
            off(reference, 'mouseenter', this.show);
            off(reference, 'mouseleave', this.hide);
            off(reference, 'focus', this.handleFocus);
            off(reference, 'blur', this.handleBlur);
            off(reference, 'click', this.removeFocusing);
        }
    },
    methods: {
        show() {
            this.setExpectedState(true);
            this.handleShowPopper();
        },

        hide() {
            this.setExpectedState(false);
            this.debounceClose();
        },
        handleFocus() {
            this.focusing = true;
            this.show();
        },
        handleBlur() {
            this.focusing = false;
            this.hide();
        },
        removeFocusing() {
            this.focusing = false;
        },

        addTooltipClass(prev) {
            if (!prev) {
                return 'el-tooltip';
            }
            return `el-tooltip ${prev.replace('el-tooltip', '')}`;

        },

        handleShowPopper() {
            if (!this.expectedState || this.manual) return;
            if (this.onlyActiveWhenOverflow) { // 自动判断元素是否超出省略
                const firstElement = this.getFirstElement();
                if (firstElement?.elm) {
                    const range = document.createRange();
                    range.setStart(firstElement.elm, 0);
                    range.setEnd(firstElement.elm, firstElement.elm.childNodes.length);
                    const rangeWidth = range.getBoundingClientRect().width;
                    const padding = (parseInt(getStyle(firstElement.elm, 'paddingLeft'), 10) || 0)
                    + (parseInt(getStyle(firstElement.elm, 'paddingRight'), 10) || 0);
                    // 完全展示时不展示提示
                    if (Math.round(rangeWidth + padding) <= firstElement.elm.offsetWidth) return;
                }
            }

            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.showPopper = true;
            }, this.openDelay);

            if (this.hideAfter > 0) {
                this.timeoutPending = setTimeout(() => {
                    this.showPopper = false;
                }, this.hideAfter);
            }
        },

        handleClosePopper() {
            if (this.enterable && this.expectedState || this.manual) return;
            clearTimeout(this.timeout);

            if (this.timeoutPending) {
                clearTimeout(this.timeoutPending);
            }
            this.showPopper = false;

            if (this.disabled) {
                this.doDestroy();
            }
        },

        setExpectedState(expectedState) {
            if (expectedState === false) {
                clearTimeout(this.timeoutPending);
            }
            this.expectedState = expectedState;
        },

        getFirstElement() {
            const slots = this.$slots.default;
            if (!Array.isArray(slots)) return null;
            let element = null;
            for (let index = 0; index < slots.length; index++) {
                if (slots[index]?.tag) {
                    element = slots[index];
                    break;
                }
            }
            return element;
        },
    },

    render(h) {
        if (this.popperVM) {
            this.popperVM.node = (
                <transition
                    name={this.transition}
                    onAfterLeave={this.doDestroy}>
                    <div
                        onMouseleave={() => { this.setExpectedState(false); this.debounceClose(); }}
                        onMouseenter={() => { this.setExpectedState(true); }}
                        ref="popper"
                        role="tooltip"
                        id={this.tooltipId}
                        aria-hidden={(this.disabled || !this.showPopper) ? 'true' : 'false'}
                        v-show={!this.disabled && this.showPopper}
                        class={
                            ['el-tooltip__popper', `is-${this.effect}`, this.popperClass]
                        }>
                        {this.$slots.content || this.content}
                        {this.wrappedListContent.length
                            ? <div class="el-tooltip__list-content">
                                <div class={`el-tooltip__list-content-title ${this.wrappedListContent.length > 1 ? 'is-bold' : ''}`}>
                                    {this.firstContent}
                                </div>
                                <ul>
                                    {this.tailContent.map((text) => (
                                        <li key={text} class={`el-tooltip__list-item ${this.showListMarker ? 'show-marker' : ''}`}>{text}</li>
                                    ))}
                                </ul>
                            </div>
                            : ''
                        }
                    </div>
                </transition>);
        }

        const firstElement = this.getFirstElement();
        if (!firstElement) return null;

        const data = firstElement.data = firstElement.data || {};
        data.staticClass = this.addTooltipClass(data.staticClass);

        return firstElement;
    },

};
