import path from 'path';

import vue from '@vitejs/plugin-vue2';
import vueJsx from '@vitejs/plugin-vue2-jsx';
import fs from 'fs-extra';
import AutoImport from 'unplugin-auto-import/vite';
import VueComponents from 'unplugin-vue-components/vite';
import { defineConfig, normalizePath } from 'vite';

import vueMdPlugin from './libs/vite-plugin-markdown';
import { IconResolver } from './unplugin-resolver';

import VueMacros from 'unplugin-vue-macros/vite'

import { VueVersionConditionPlugin } from './libs/vite-plugin-vue-version-condition';

const packagesDir = path.resolve(process.cwd(), '../');
const packagesVersatileDir = path.resolve(process.cwd(), '../../packages-versatile/');

const version = JSON.parse(fs.readFileSync(`${packagesDir}/web/package.json`, 'utf-8').toString()).version;

const versatilePackages = fs.readdirSync(packagesVersatileDir)
const legacyPackages = fs.readdirSync(packagesDir).filter(i => !versatilePackages.includes(i));

// https://vitejs.dev/config/
const config = (isDev: boolean) => defineConfig({
    define: {
        __VERSION__: JSON.stringify(version),
    },
    resolve: {
        alias: [
            ...(isDev
                ? [
                    {
                        find: new RegExp(`^@mitra-design\/(${legacyPackages.join('|')})$`),
                        replacement: path.resolve(packagesDir, '$1/src'),

                        // '@mitra-design/web' -> `path-to-packages/web/src`
                    },
                    {
                        find: new RegExp(`^@mitra-design\/(${versatilePackages.join('|')})$`),
                        replacement: path.resolve(packagesVersatileDir, '$1/src'),

                        // '@mitra-design/shared' -> `path-to-packages-versatile/web/src`
                    }
                ]
                : []
            ),
            {
                find: new RegExp(`^@mitra-design\/(${legacyPackages.join('|')})/(?!es|src)(.*)`),
                replacement: path.resolve(packagesDir, '$1/src/$2'),

                // '@mitra-design/web/xx(不是es|src)/xxxxx.ts' -> `path-to-packages/web/src/xx/xxxxx.ts`
            },
            {
                find: new RegExp(`^@mitra-design\/(${versatilePackages.join('|')})/(?!es|src)(.*)`),
                replacement: path.resolve(packagesVersatileDir, '$1/src/$2'),

                // '@mitra-design/shared/xx(不是es|src)/xxxxx.ts' -> `path-to-packages-versatile/shared/src/xx/xxxxx.ts`
            },
            {
                find: /^@no-external\/([a-z-]*?)\/(.*)/,
                replacement: path.resolve(packagesDir, '$1/src/$2'),

                // '@no-external/shared/xxxx' -> `path-to-packages/shared/src/xxxx`
                //
                // 需要此路径改写的原因是：
                //
                // 在生产打包时，若有如下代码：
                //
                // import type { X } from '@mitra-design/shared/xxxxx/types'
                // interface SomeProps extends X {};
                //
                // （SomeProps被.vue引入，传递给vue-marcos/better-define修饰的defineProps）
                //
                // **由于@mitra-design/*均被external**，
                // VueMacros无法解析到X的类型，导致SomeProps无法正确解析
                //
                // 故约定此种情况下使用@no-external/*取代@mitra-design/*，避免此处导入被external
            },
            {
                find: /^(@\/.*)$/,
                replacement: '$1',
                customResolver(source, importer, options) {
                    if (!importer) return source;

                    const importerPackage = normalizePath(importer)
                        .replace(normalizePath(path.resolve(packagesDir)), '')
                        .split('/')[1];

                    const importerExt = path.extname(importer);

                    const result = normalizePath(source.replace(/^@/, path.resolve(packagesDir, importerPackage, 'src')));

                    const resultExt = path.extname(result);

                    if (!resultExt) {
                        if (importer.endsWith('\*')) {
                            // less在导入时，importer会以\*结尾
                            return `${result}.less`;
                        }

                        if (importerExt === '.scss' && !result.endsWith('.scss')) {
                            return `${result}.scss`;
                        }

                        if (['.vue', '.ts', '.tsx', '.js', '.jsx', '.md'].includes(importerExt)) {
                            const supportList = [
                                '.ts',
                                '.tsx',
                                '/index.ts',
                                '/index.tsx',
                                '.js',
                                '.jsx',
                                '/index.js',
                                '/index.jsx',
                            ];

                            for (const support of supportList) {
                                if (fs.existsSync(`${result}${support}`)) {
                                    return `${result}${support}`;
                                }
                            }
                        }
                    }
                    return result;
                },
            },
        ],
    },
    plugins: [
        VueMacros({
            plugins: {
                vue: vue(),
                vueJsx: vueJsx({
                    babelPlugins: [
                        ['@babel/plugin-proposal-decorators', { version: 'legacy' }],
                        ['@babel/plugin-proposal-class-properties'],
                    ],
                }),
            },
        }),
        vueMdPlugin(),
        VueComponents({
            dts: false,
            exclude: [
                /[\\/]node_modules[\\/]/,
                /[\\/]\.git[\\/]/,
            ],
            resolvers: [IconResolver()],
        }),
        AutoImport({
            dts: true,
            imports: [
                'vue',
                {
                    from: 'vue-router',
                    imports: ['Route', 'RouteConfig'],
                    type: true,
                },
                {
                    'vue-property-decorator': [
                        'Component',
                        'Mixins',
                        'Emit',
                        'Inject',
                        'InjectReactive',
                        'Model',
                        'ModelSync',
                        'Prop',
                        'PropSync',
                        'Provide',
                        'ProvideReactive',
                        'Ref',
                        'VModel',
                        'Watch',
                    ],
                },
                {
                    lodash: [['*', '_']],
                },
                {
                    '@mitra-design/helpers/two-way': ['twowayFactory'],
                },
                {
                    '@mitra-design/shared/vue-decorators': [
                        'Bem',
                    ],
                },
            ],
        }),
        VueVersionConditionPlugin('2'),
    ],
    server: {
        port: 6006,
    },
    build: {
        target: 'esnext',
        reportCompressedSize: false,
        commonjsOptions: {
            include: [
                /node_modules/,
                // color@4.2.3 子包-> https://node-modules.dev/graph#install=color@4.2.3
                /packages\/color/,
                /is-arrayish/,
            ],
            transformMixedEsModules: true,
        },
        rollupOptions: {
            output: {
                manualChunks: {
                    // 将 UI 库拆包
                    'mt-common': [
                        '@mitra-design/icons',
                        '@mitra-design/locale',
                        '@mitra-design/theme',
                        '@mitra-design/color',
                    ],
                    'mt-element': [
                        'element-ui',
                        '@mitra-design/element-ui',
                    ],
                    'mt-web': [
                        '@mitra-design/web',
                        '@mitra-design/pro-components',
                        '@mitra-design/e-sign',
                    ],
                    'mt-mobile': [
                        '@mitra-design/vant',
                        '@mitra-design/mobile',
                        '@mitra-design/e-sign-mobile',
                    ],
                }
            }
        },
    },
    css: {
        preprocessorOptions: {
            less: {
                javascriptEnabled: true,
            },
        },
        devSourcemap: true
    },
    optimizeDeps: {
        exclude: [
            'element-ui/types/tree',
            'element-ui/types/button',
            'vue/types/jsx',
        ],
        esbuildOptions: {
            tsconfigRaw: {
                compilerOptions: {
                    experimentalDecorators: true,
                },
            },
        },
    },
    esbuild: {
        tsconfigRaw: {
            compilerOptions: {
                target: 'esnext',
                experimentalDecorators: true,
            }
        },
    },
});


export default defineConfig(({ command }) => {
    const isDev = command === 'serve';
    return config(isDev);
});
