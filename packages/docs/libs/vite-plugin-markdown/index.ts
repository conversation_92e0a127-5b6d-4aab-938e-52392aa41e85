import fs from 'fs-extra';
import type { Plugin } from 'vite';

import { parseDocMeta } from '../doc-meta';

import { replaceApiPlaceholder } from './api';
import { getCache } from './cache';
import { transformMarkdown } from './parse';
import { replaceTokenPlaceholder } from './token';
import { getVirtualPath, isDemoMarkdown } from './utils';


/**
 *
 */
export default function vueMdPlugin(): Plugin {
    let vuePlugin: Plugin | undefined;

    return {
        name: 'vite:mitra-design-docs',
        enforce: 'pre',
        configResolved(resolvedConfig) {
            // 获取 vue 插件，在 hotUpload 中使用
            vuePlugin = resolvedConfig.plugins.find((p) => p.name === 'vite:vue2');
        },
        async load(id) {
            if (id.startsWith('virtual:')) {
                return getCache(id);
            }
            if (id.endsWith('.demo.vue')) {
                const res = await fs.readFile(id, 'utf-8');
                return res;
            }
            return null;
        },
        resolveId(id, importer) {
            // 遇到虚拟 md 模块，直接返回 id
            if (id.includes('virtual:')) {
                return id;
            }
            return null;
        },
        async transform(code: string, id: string) {
            if (!id.endsWith('.md')) {
                return null;
            }

            // 虚拟模块直接编译，不需要做其他处理
            if (id.includes('virtual:')) {
                // @ts-ignore
                return vuePlugin.transform?.call(this, code, id.replace('md', 'vue'));
            }

            const docMeta = parseDocMeta(code, id);

            if (isDemoMarkdown(id) && code.includes('## API')) {
                code = await replaceApiPlaceholder(code, id);
            }

            if (isDemoMarkdown(id) && code.includes('## Design Token')) {
                code = await replaceTokenPlaceholder(code, id);
            }

            const vueCode = await transformMarkdown(code, id, docMeta);

            // @ts-ignore
            return vuePlugin.transform?.call(this, vueCode, id.replace('md', 'vue'));
        },
        async handleHotUpdate(ctx) {
            // 处理 md 文件的热更新
            if (!ctx.file.endsWith('.md') || !vuePlugin) {
                return undefined;
            }

            const { file, read, timestamp, server, modules } = ctx;
            const { moduleGraph } = server;

            const updated = new Array();

            const content = await read();

            const vueCode = await transformMarkdown(content, file);

            if (isDemoMarkdown(file)) {
                const virtualPath = getVirtualPath(file);
                const mods = moduleGraph.getModulesByFile(virtualPath);

                if (mods) {
                    // @ts-ignore
                    const ret = await vuePlugin.handleHotUpdate?.({
                        file: virtualPath.replace('md', 'vue'),
                        timestamp,
                        modules: [...mods],
                        server,
                        read: () => getCache(virtualPath),
                    });

                    updated.push(...(ret || []));
                }
            }

            // @ts-ignore
            const ret = await vuePlugin.handleHotUpdate?.({
                file: file.replace('md', 'vue'),
                timestamp,
                modules,
                server,
                read: () => vueCode,
            });

            return [...updated, ...(ret || [])];
        },
    };
}
