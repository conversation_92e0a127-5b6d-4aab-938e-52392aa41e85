import { EOL } from 'node:os';
import path from 'node:path';

import dedent from 'dedent';
import fs from 'fs-extra';

export const replaceTokenPlaceholder = async (code: string, file: string) => {
    const componentDir = path.resolve(file, '../../').replace(/\\/g, '/');
    const componentName = path.basename(componentDir);

    // 支持两种 token.less 文件
    // 1. 组件目录下的 token.less
    // 2. 组件目录下的 styles 目录下的 token.less
    const tokenLessFile = path.resolve(componentDir, `${componentName}.token.less`);
    const tokenLessFileStyle = path.resolve(componentDir, `styles/${componentName}.token.less`);

    let finalFilePath = tokenLessFile;

    if (!fs.existsSync(finalFilePath)) {
        finalFilePath = tokenLessFileStyle;
    }

    const tokenContent = await fs.readFile(finalFilePath, 'utf-8');

    const tokenMap = handleTokenContent(tokenContent);

    const tokenTable = transformTokenMapToTable(tokenMap);

    code = code.replace(/## Design Token/g, `## Design Token${EOL}${EOL}${tokenTable}${EOL}`);

    return code;
};


const handleTokenContent = (tokenContent: string) => {
    const lines = tokenContent.replaceAll(/\/\/(\s)?(.)+/g, '').replaceAll(`${EOL}`, '').split(';').filter(Boolean);
    const tokenMap = new Map<string, string>();

    for (const line of lines) {
        const [key, value] = line.split(':');
        tokenMap.set(key, value.trim());
    }

    return tokenMap;
};

const transformTokenMapToTable = (tokenMap: Map<string, string>) => {
    const body = [];

    for (const [key, value] of tokenMap.entries()) {
        const row = `|${key}|\`${value}\`|`;
        body.push(row);
    }

    const header = '|Token Name|Default Value|';

    return dedent(`
        ${header}
        |---|---|
        ${body.join(EOL)}
    `);
};
