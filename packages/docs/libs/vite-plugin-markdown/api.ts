import path from 'node:path';

import fs from 'fs-extra';
import glob from 'glob';
import _ from 'lodash';
import { Project } from 'ts-morph';
import upper from 'uppercamelcase';
import { parse, parseSource, type ComponentDoc } from 'vue-docgen-api';

const packagesDir = path.resolve(process.cwd(), '../');

let project: Project | null = null;

import { resolveFilepath } from '../_utils';

import { getEventsTemplate, getPropsTemplate, getSlotsTemplate, getInterfacesTemplate } from './api-template';
import { toUpperCamelCase } from './utils';
import { parseVisualSlots } from './visual-slots';

const componentDocMap = new Map<string, ComponentDoc>();

const getComponentDocKey = (componentDir: string, componentName: string) => {
    return `${componentDir}-${componentName}`;
};

/**
 * 获取当前子包下的 locale 语言包，返回组件对应的语言对象
 * @param pkg 子包名
 * @param name 组件名
 */
const getLocale = (pkg: string, name: string) => {
    const localePath = path.resolve(packagesDir, pkg, './src/locale/lang/zh_CN.json');
    if (!fs.existsSync(localePath)) return {};

    const zhJSON = JSON.parse(fs.readFileSync(localePath, 'utf-8'));
    return zhJSON[upper(name)] ?? {};
};

const parseApi = async (globFilePath: string, ignorePath: string, locale: Record<string, string>, componentDir: string, componentName: string) => {
    const files = glob.sync(globFilePath, {
        ignore: [ignorePath],
    });

    const content = [];

    for (const file of files) {
        let componentDoc: ComponentDoc;
        const key = getComponentDocKey(componentDir, componentName);

        if (componentDocMap.has(key)) {
            componentDoc = componentDocMap.get(key)!;
        } else {
            componentDoc = await parse(file);
            componentDocMap.set(key, componentDoc);
        }

        const { slots } = componentDoc;
        let { props, events } = componentDoc;
        // 如果 props 为 undefined，或者全是默认值声明，可能 defineProps 泛型参数是从模块导入的，尝试合并；
        if (!props?.length || props.every(i => i.defaultValue?.value) || !events?.length) {
            const docs = await parseImportedPropsType(file, componentDir, componentName) as ComponentDoc;
            props = mergeProps(props, docs.props);
            events = mergeProps(events, docs.events);
        }

        if (props?.length) {
            const propsTemp = getPropsTemplate(props, locale);
            propsTemp && content.push(propsTemp);
        }

        if (events?.length) {
            const eventsTemp = getEventsTemplate(events);
            eventsTemp && content.push(eventsTemp);
        }

        if (slots?.length) {
            const slotsTemp = getSlotsTemplate(slots);
            slotsTemp && content.push(slotsTemp);
        }
    }

    return content;
};


// 只解析组件文件夹同名的 .vue 文件
const parseEntryApi = async (pkgName: string, componentDir: string, componentName: string, source: string) => {
    const globFilePath = `${componentDir}/**/${componentName}.vue`;
    const ignorePath = `${componentDir}/**/__demos__/**/*.vue`;

    const locale = getLocale(pkgName, componentName);

    const content = await parseApi(globFilePath, ignorePath, locale, componentDir, componentName);

    const placeholder = '## API';

    const apiIndex = source.indexOf(placeholder);

    return source.slice(0, apiIndex + placeholder.length + 1) + content.join('\n') + source.slice(apiIndex + placeholder.length + 1);
};


// 可以通过 %API(xxx) 指定解析当前组件文件夹下的 .vue 文件
const parseExtraApi = async (pkgName: string, componentDir: string, source: string) => {
    const matched = source.matchAll(/%API\(([a-zA-Z-_,\s]+)\)/g);

    for (const match of matched) {
        if (match[1]) {
            let componentName = match[1];
            let displayName = '';
            if (componentName.includes(',')) {
                [componentName, displayName] = componentName.split(', ');
            }
            const globFilePath = `${componentDir}/**/${componentName}.vue`;
            const ignorePath = `${componentDir}/**/__demos__/**/*.vue`;

            const locale = getLocale(pkgName, componentName);

            const content = await parseApi(globFilePath, ignorePath, locale, componentDir, componentName);

            source = source.replace(match[0], `#### \`<${displayName || componentName}>\`\n${content.join('\n')}`);
        }
    }

    return source;
};


// 可通过 %INTERFACE(xxx) 指定解析当前组件文件夹下的.ts 文件中某一个 interface
const parseInterfaceApi = async (pkgName: string, componentDir: string, source: string) => {
    const matched = source.matchAll(/%INTERFACE\(([A-Za-z0-9,\s./-_]+)\)/g);
    const matchedArray = Array.from(matched);

    if (matchedArray.length === 0) {
        return source;
    }

    if (!project) {
        project = new Project();
    }

    for (const match of matchedArray) {
        if (match[1]) {
            let temp = '';

            const [typeName, typePath, aliasTypeName] = match[1].split(',').map(i => i.trim());
            const typePathResolved = path.resolve(componentDir, typePath);
            const extname = path.extname(typePathResolved);
            const filepath = extname ? typePathResolved : `${typePathResolved}.ts`;

            project.addSourceFileAtPath(filepath);

            const sourceFile = project.getSourceFile(filepath);

            if (sourceFile) {
                const interfaceDecl = sourceFile.getInterfaceOrThrow(typeName);
                const properties = interfaceDecl!.getProperties();

                const parsedTypes = properties.map(property => {
                    const name = property.getName();
                    const type = property.getTypeNode()?.getText();
                    const description = property.getJsDocs()[0]?.getCommentText();
                    // 因为解析库不同，这里的逻辑还得再处理一遍～
                    // 总有一天，他们会统一的...
                    const deprecated = property.getJsDocs()[0]?.getTags().find(tag => tag.getTagName() === 'deprecated');
                    const version = property.getJsDocs()[0]?.getTags().find(tag => tag.getTagName() === 'version');
                    return {
                        name,
                        type,
                        description,
                        deprecated: deprecated ? { description: deprecated.getCommentText() } : undefined,
                        version: version ? { description: version.getCommentText()! } : undefined,
                    };
                });

                temp += getInterfacesTemplate(aliasTypeName || typeName, parsedTypes);
            }

            source = source.replace(match[0], temp);
        }
    }

    return source;
};


export const replaceApiPlaceholder = async (source: string, id: string) => {
    const componentDir = path.resolve(id, '../../');

    // web | pro-components | ...
    // eslint-disable-next-line prefer-const
    let { pkgName, componentName } = resolveFilepath(id);

    const normalizeComponentDir = componentDir.replace(/\\/g, '/');

    // 兼容一下 element-ui 组件位置
    if (normalizeComponentDir.includes('element-ui/components')) {
        componentName = normalizeComponentDir.split('/').pop()!;
    }

    // 入口组件的 api 解析
    source = await parseEntryApi(pkgName, normalizeComponentDir, componentName, source);

    // 额外组件的 api 解析
    source = await parseExtraApi(pkgName, normalizeComponentDir, source);

    // 指定的 interface api 解析
    source = await parseInterfaceApi(pkgName, normalizeComponentDir, source);

    // 解析 visual-slots
    if (source.includes('<doc-visual-slots')) {
        const key = getComponentDocKey(normalizeComponentDir, componentName);
        source = parseVisualSlots(source, componentName, componentDocMap.get(key)?.slots);
    }

    return source;
};

const parseImportedPropsType = async (source: string, componentDir: string, componentName: string) => {
    const upperComponentName = toUpperCamelCase(componentName);

    const genSFC = async () => {
        const sfc = await fs.readFile(source, { encoding: 'utf-8' });
        // import type { COMPONENTProps, COMPONENTEvents } from '../types';
        const reg = new RegExp('import\\stype\\s\\{\\s([a-zA-Z0-9,\\s]+)\\s\\}\\sfrom\\s\'\\.', 'g');

        const matched = sfc.matchAll(reg);

        let propsType;
        let eventsType;

        for (const match of matched) {
            const matches = match[1].split(',').map(i => i.trim());
            propsType = matches.find(item => item.includes(`${upperComponentName}Props`));
            eventsType = matches.find(item => item.includes(`${upperComponentName}Events`));

            if (propsType && eventsType) break;
        }

        if (!propsType && !eventsType) return '';

        // 目前支持四种种类型文件定义，依次寻找
        const typeComponentPath = path.resolve(componentDir, `types/${componentName}.ts`);
        const typePropsPath = path.resolve(componentDir, 'types/props.ts');
        const typeIndexPath = path.resolve(componentDir, 'types/index.ts');
        const typesPath = path.resolve(componentDir, 'types.ts');
        let finalPath = typeComponentPath;

        if (fs.existsSync(typeComponentPath)) {
            finalPath = typeComponentPath;
        }

        else if (fs.existsSync(typePropsPath)) {
            finalPath = typePropsPath;
        }

        else if (fs.existsSync(typeIndexPath)) {
            finalPath = typeIndexPath;
        }

        else if (fs.existsSync(typesPath)) {
            finalPath = typesPath;
        }

        const tsFileContent = await fs.readFile(finalPath, { encoding: 'utf-8' });
        const propsRE = new RegExp(`interface ${propsType}.*({[\\s\\S]*?[\r\n]})`);
        const eventsRE = new RegExp(`interface ${eventsType}.*({[\\s\\S]*?[\r\n]})`);
        const props = tsFileContent.match(propsRE)?.[1];
        const events = tsFileContent.match(eventsRE)?.[1];
        const tempSFC = `
            <script setup lang="ts">
                defineProps<${props}>();
                ${eventsType && `defineEmits<${events}>();`}
            </script>
        `;
        return tempSFC;
    };

    const res = await genSFC();
    if (!res) return {};

    const doc = await parseSource(res, source);
    return doc;
};


const mergeProps = (oldVal: any[] = [], newVal: any[] = []) => {
    const res = [...oldVal, ...newVal].reduce((pre, cur) => {
        if (!pre.some((i: any) => i.name === cur.name)) {
            pre.push(cur);
        }
        const index = pre.findIndex((i: any) => i.name === cur.name);
        pre[index] = Object.assign(pre[index], cur);

        return pre;
    }, []);

    return res;
};
