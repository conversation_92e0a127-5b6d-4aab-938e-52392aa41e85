import _ from 'lodash';

export const isDemoMarkdown = (id: string) => {
    // eslint-disable-next-line @typescript-eslint/prefer-includes
    return /\/__demos__\//.test(id);
};

export const getVirtualPath = (id: string) => {
    return `virtual:${id}`;
};

export const getRealPath = (id: string) => {
    return id.replace('virtual:', '');
};

/**
 * 转为 kebab-case
 * @param str 大/小驼峰
 */
export const toKebabCase = (str: string): string => {
    return str.replace(/[A-Z]/g, (match, offset) => {
        return `${offset > 0 ? '-' : ''}${match.toLocaleLowerCase()}`;
    });
};

export const toUpperCamelCase = (str: string) => {
    const camelCaseStr = _.camelCase(str);
    return camelCaseStr.charAt(0).toUpperCase() + camelCaseStr.slice(1);
};
