import type { ReleaseNotes } from '@/../libs/gen-release-notes/_types';

const notes: ReleaseNotes = {
    '2.9.5': {
        date: '2025-07-08',
        logs: {
            web: {
                drawer: [
                    {
                        pkg: 'web',
                        name: 'drawer',
                        time: '2025-07-08',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;移除底部默认的取消、确定按钮&lt;/li&gt;&lt;li&gt;移除 &lt;code&gt;cancel&lt;/code&gt; 和 &lt;code&gt;confirm&lt;/code&gt; 事件，关闭事件改为 &lt;code&gt;@close&lt;/code&gt;&lt;/li&gt;&lt;li&gt;&lt;code&gt;footer-content&lt;/code&gt; 插槽改为 &lt;code&gt;footer&lt;/code&gt; 插槽，原 &lt;code&gt;footer&lt;/code&gt; 插槽移除&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
                'resize-box': [
                    {
                        pkg: 'web',
                        name: 'resize-box',
                        time: '2025-07-07',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复鼠标按下没有移动直接松开时，触发的 &lt;code&gt;resize-end&lt;/code&gt; 事件中参数 size 为 0 的问题&lt;/p&gt;',
                    },
                ],
            },
        },
    },
    '2.9.4': {
        date: '2025-07-04',
        logs: {
            helpers: {
                'manually-mount': [
                    {
                        pkg: 'helpers',
                        name: 'manually-mount',
                        time: '2025-07-04',
                        type: 'fix',
                        marks: [],
                        author: '黄宇',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复在使用useManuallyMount时，在afterActive周期前尝试读取isActive后，isActive后续不会响应变化的问题&lt;/p&gt;',
                    },
                ],
            },
        },
    },
    '2.9.3': {
        date: '2025-07-03',
        logs: {
            'e-sign': {
                'input-otp': [
                    {
                        pkg: 'e-sign',
                        name: 'input-otp',
                        time: '2025-06-24',
                        type: 'feat',
                        marks: [],
                        author: '李镇宇',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;callback-text&lt;/code&gt; 属性和插槽，可自定义倒计时回调文本&lt;/p&gt;',
                    },
                ],
            },
            helpers: {
                'sync-with-query': [
                    {
                        pkg: 'helpers',
                        name: 'sync-with-query',
                        time: '2025-07-03',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;优化 &lt;code&gt;useSyncWithQuery&lt;/code&gt; 函数&lt;/p&gt;',
                    },
                ],
            },
            web: {
                'infinite-scroll-list': [
                    {
                        pkg: 'web',
                        name: 'infinite-scroll-list',
                        time: '2025-06-24',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;添加&lt;code&gt;finishText&lt;/code&gt;配置，用于控制加载完成时展示的文本。&lt;/p&gt;',
                    },
                ],
                menu: [
                    {
                        pkg: 'web',
                        name: 'menu',
                        time: '2025-06-30',
                        type: 'fix',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;添加 &lt;code&gt;title&lt;/code&gt; &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIVATE-110058&apos;&gt; 🔗 PRIVATE-110058&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
            },
            mobile: {
                'fullscreen-popup': [
                    {
                        pkg: 'mobile',
                        name: 'fullscreen-popup',
                        time: '2025-06-24',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复一些机型顶部会有 1px 偏差问题 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIVPAT-3592&apos;&gt; 🔗 PRIVPAT-3592&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
            },
            vant: {
                calendar: [
                    {
                        pkg: 'vant',
                        name: 'calendar',
                        time: '2025-06-25',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;语言包值的类型统一为字符串 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIVPAT-3641&apos;&gt; 🔗 PRIVPAT-3641&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
            },
        },
    },
    '2.9.2': {
        date: '2025-06-19',
        logs: {
            'e-sign': {
                'tag-manager': [
                    {
                        pkg: 'e-sign',
                        name: 'tag-manager',
                        time: '2025-06-16',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;选中选项后自动聚焦输入框 &lt;a href=&apos;PRIVATE-111653&apos;&gt;PRIVATE-111653&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
            },
            web: {
                'draggable-layout': [
                    {
                        pkg: 'web',
                        name: 'draggable-layout',
                        time: '2025-06-17',
                        type: 'fix',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复左右两侧分割线缺失的问题。 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIVATE-115199&apos;&gt; 🔗 PRIVATE-115199&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
                drawer: [
                    {
                        pkg: 'web',
                        name: 'drawer',
                        time: '2025-06-18',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复 &lt;code&gt;Drawer&lt;/code&gt; 组件中 &lt;code&gt;v-scroll&lt;/code&gt; 指令未注册导致无法滚动的问题&lt;/p&gt;',
                    },
                ],
                form: [
                    {
                        pkg: 'web',
                        name: 'form',
                        time: '2025-06-11',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;表单中的 Checkbox 和 Radio 行高设置为与 FormItem 高度一致，保证换行时与 label 对齐&lt;/p&gt;',
                    },
                ],
                menu: [
                    {
                        pkg: 'web',
                        name: 'menu',
                        time: '2025-06-17',
                        type: 'fix',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复菜单文案过长时图标被压缩的问题 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIVPAT-3600&apos;&gt; 🔗 PRIVPAT-3600&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
                modal: [
                    {
                        pkg: 'web',
                        name: 'modal',
                        time: '2025-06-18',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复 &lt;code&gt;Modal&lt;/code&gt; 组件中 &lt;code&gt;v-scroll&lt;/code&gt; 指令未注册导致无法滚动的问题&lt;/p&gt;',
                    },
                ],
                'resize-box': [
                    {
                        pkg: 'web',
                        name: 'resize-box',
                        time: '2025-06-18',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复部分情况下触发 &lt;code&gt;resize-end&lt;/code&gt; 事件时，size 可能会是 0 的问题、&lt;/p&gt;',
                    },
                ],
                'select-input': [
                    {
                        pkg: 'web',
                        name: 'select-input',
                        time: '2025-06-09',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复 &lt;code&gt;placeholder&lt;/code&gt; 不生效的问题&lt;/p&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'select-input',
                        time: '2025-06-18',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复 &lt;code&gt;SelectInput&lt;/code&gt; 组件中 &lt;code&gt;v-scroll&lt;/code&gt; 指令未注册导致无法滚动的问题&lt;/p&gt;',
                    },
                ],
                'select-panel': [
                    {
                        pkg: 'web',
                        name: 'select-panel',
                        time: '2025-06-10',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;优化组件性能，优化 &lt;code&gt;selection-change&lt;/code&gt; 触发时机&lt;/p&gt;',
                    },
                ],
                tooltip: [
                    {
                        pkg: 'web',
                        name: 'tooltip',
                        time: '2025-06-09',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复列表标记被压缩的问题&lt;/p&gt;',
                    },
                ],
                'virtual-list': [
                    {
                        pkg: 'web',
                        name: 'virtual-list',
                        time: '2025-06-11',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复打开弹出层时滚动条位置不对的问题&lt;/p&gt;',
                    },
                ],
            },
            mobile: {
                avatar: [
                    {
                        pkg: 'mobile',
                        name: 'avatar',
                        time: '2025-06-17',
                        type: 'feat',
                        marks: [],
                        author: '李镇宇',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;增加 &lt;code&gt;light&lt;/code&gt; 属性，用于显示浅色背景&lt;/p&gt;',
                    },
                ],
            },
        },
    },
    '2.9.1': {
        date: '2025-06-05',
        logs: {
            'e-sign': {
                'selector-org': [
                    {
                        pkg: 'e-sign',
                        name: 'selector-org',
                        time: '2025-06-03',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;新增 &lt;code&gt;subListBlankText&lt;/code&gt; 和 &lt;code&gt;subSearchListBlankText&lt;/code&gt; 配置，支持自定义子列表检索空状态文字&lt;/p&gt;',
                    },
                ],
            },
            web: {
                modal: [
                    {
                        pkg: 'web',
                        name: 'modal',
                        time: '2025-06-04',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;bodyHeight&lt;/code&gt; 属性，用于设置弹窗内容区高度&lt;/p&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'modal',
                        time: '2025-06-05',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;&lt;code&gt;close-icon&lt;/code&gt; 插槽改成 &lt;code&gt;operation&lt;/code&gt;，独立处关闭按钮，不受插槽控制&lt;/p&gt;',
                    },
                ],
                switch: [
                    {
                        pkg: 'web',
                        name: 'switch',
                        time: '2025-06-03',
                        type: 'fix',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复 IE 样式问题&lt;/p&gt;',
                    },
                ],
            },
            'element-ui': {
                'date-picker': [
                    {
                        pkg: 'element-ui',
                        name: 'date-picker',
                        time: '2025-06-03',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;优化日期时间选择器弹窗面板底部按钮的样式&lt;/p&gt;',
                    },
                ],
            },
            mobile: {
                badge: [
                    {
                        pkg: 'mobile',
                        name: 'badge',
                        time: '2025-06-03',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: true,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;&lt;code&gt;small&lt;/code&gt; 尺寸下的字体大小由 &lt;code&gt;12px&lt;/code&gt; 改为 &lt;code&gt;10px&lt;/code&gt;&lt;/p&gt;',
                    },
                ],
                'list-item': [
                    {
                        pkg: 'mobile',
                        name: 'list-item',
                        time: '2025-06-03',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;添加文字溢出时的查看全部功能。&lt;/p&gt;',
                    },
                ],
                tabs: [
                    {
                        pkg: 'mobile',
                        name: 'tabs',
                        time: '2025-06-04',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: true,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;no-side-padding&lt;/code&gt; prop 用于取消两侧 padding&lt;/p&gt;',
                    },
                ],
            },
        },
    },
    '2.9.0': {
        date: '2025-05-30',
        logs: {
            theme: {
                '': [
                    {
                        pkg: 'theme',
                        name: '',
                        time: '2025-03-27',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;新增 &lt;code&gt;shadow2Right&lt;/code&gt; token&lt;/p&gt;&lt;p&gt;其他 shadow 相关值变更：&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;code&gt;shadow2Down&lt;/code&gt; 从 &lt;code&gt;0 2px 16px rgba(0, 19, 48, 0.12)&lt;/code&gt; 变更为 &lt;code&gt;0 2px 12px rgba(0, 19, 48, 0.08)&lt;/code&gt;&lt;/li&gt;&lt;li&gt;&lt;code&gt;shadow2Up&lt;/code&gt; 从 &lt;code&gt;0 -2px 16px rgba(0, 19, 48, 0.12)&lt;/code&gt; 变更为 &lt;code&gt;0 -2px 12px rgba(0, 19, 48, 0.08)&lt;/code&gt;&lt;/li&gt;&lt;li&gt;&lt;code&gt;shadow3Down&lt;/code&gt; 从 &lt;code&gt;0 4px 20px rgba(0, 19, 48, 0.12)&lt;/code&gt; 变更为 &lt;code&gt;0 2px 16px rgba(0, 19, 48, 0.12)&lt;/code&gt;&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
            },
            'e-sign': {
                'file-img-viewer': [
                    {
                        pkg: 'e-sign',
                        name: 'file-img-viewer',
                        time: '2025-05-09',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;优化弹窗样式 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-5472&apos;&gt; 🔗 PRIV-5472&lt;/a&gt;&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
            },
            'pro-components': {
                'pro-filters': [
                    {
                        pkg: 'pro-components',
                        name: 'pro-filters',
                        time: '2025-04-25',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;预设选择器默认开启 &lt;code&gt;clearable&lt;/code&gt; &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-8311&apos;&gt; 🔗 PRIV-8311&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
                'pro-table': [
                    {
                        pkg: 'pro-components',
                        name: 'pro-table',
                        time: '2025-05-29',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;去除单行高度 &lt;code&gt;56px&lt;/code&gt; 的默认配置&lt;/li&gt;&lt;li&gt;操作按钮默认改成 link 模式&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
            },
            web: {
                alert: [
                    {
                        pkg: 'web',
                        name: 'alert',
                        time: '2025-04-29',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复所有尺寸中高度与设计稿不一致的问题&lt;/p&gt;',
                    },
                ],
                'async-group-select': [
                    {
                        pkg: 'web',
                        name: 'async-group-select',
                        time: '2025-04-25',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;新增 &lt;code&gt;clearable&lt;/code&gt; &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-8311&apos;&gt; 🔗 PRIV-8311&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
                button: [
                    {
                        pkg: 'web',
                        name: 'button',
                        time: '2025-04-28',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;Link 模式拓展到纯图标按钮中&lt;/li&gt;&lt;li&gt;新增 &lt;code&gt;iconSize&lt;/code&gt; 属性，用于控制图标的大小&lt;/li&gt;&lt;/ul&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'button',
                        time: '2025-05-09',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;新增 &lt;code&gt;right-icon&lt;/code&gt; 属性，用于控制按钮的图标位置&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
                card: [
                    {
                        pkg: 'web',
                        name: 'card',
                        time: '2025-05-07',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Card&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'draggable-layout': [
                    {
                        pkg: 'web',
                        name: 'draggable-layout',
                        time: '2025-05-14',
                        type: 'style',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;移除拖拽时的骨架屏&lt;/p&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'draggable-layout',
                        time: '2025-05-27',
                        type: 'fix',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复拖拽区域过窄导致难以交互的问题。修正左右两区域间距比设计图略宽的问题。&lt;/p&gt;',
                    },
                ],
                drawer: [
                    {
                        pkg: 'web',
                        name: 'drawer',
                        time: '2025-03-24',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Drawer&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                dropdown: [
                    {
                        pkg: 'web',
                        name: 'dropdown',
                        time: '2025-03-26',
                        type: 'new',
                        marks: [],
                        author: '李镇宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Dropdown&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                form: [
                    {
                        pkg: 'web',
                        name: 'form',
                        time: '2025-05-21',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复当存在 &lt;code&gt;extra&lt;/code&gt; 时，设置了 &lt;code&gt;noBottomSpace&lt;/code&gt; 后还有间距的问题&lt;/p&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'form',
                        time: '2025-05-28',
                        type: 'style',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;表单项之间的间距由 &lt;code&gt;20px&lt;/code&gt; 改为 &lt;code&gt;16px&lt;/code&gt;&lt;/p&gt;',
                    },
                ],
                input: [
                    {
                        pkg: 'web',
                        name: 'input',
                        time: '2025-03-13',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;修复表单校验的 &lt;code&gt;change&lt;/code&gt; 与原生 &lt;code&gt;change&lt;/code&gt; 事件触发时机不一致的问题&lt;/p&gt;',
                    },
                ],
                'input-number': [
                    {
                        pkg: 'web',
                        name: 'input-number',
                        time: '2025-04-29',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;修复设置了精度时，无法在输入过程中去除小数的问题&lt;/li&gt;&lt;li&gt;修复按钮模式下左右控制按钮宽度被挤压的问题&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
                menu: [
                    {
                        pkg: 'web',
                        name: 'menu',
                        time: '2025-04-30',
                        type: 'style',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;菜单项间距由 &lt;code&gt;4px&lt;/code&gt; 调整为 &lt;code&gt;2px&lt;/code&gt;&lt;/p&gt;',
                    },
                ],
                modal: [
                    {
                        pkg: 'web',
                        name: 'modal',
                        time: '2025-02-25',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Modal&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                popconfirm: [
                    {
                        pkg: 'web',
                        name: 'popconfirm',
                        time: '2025-03-18',
                        type: 'new',
                        marks: [],
                        author: '李镇宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Popconfirm&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                popover: [
                    {
                        pkg: 'web',
                        name: 'popover',
                        time: '2025-03-07',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Popover&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'resize-box': [
                    {
                        pkg: 'web',
                        name: 'resize-box',
                        time: '2025-03-25',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;ResizeBox&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'resize-box',
                        time: '2025-05-27',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;拖拽指示线现在使用中性色，且宽度减少&lt;/p&gt;',
                    },
                ],
                select: [
                    {
                        pkg: 'web',
                        name: 'select',
                        time: '2025-04-25',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;未选中选项时，触发器默认文本展示为灰色 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-8311&apos;&gt; 🔗 PRIV-8311&lt;/a&gt;&lt;/li&gt;&lt;li&gt;文本风格选择器支持 &lt;code&gt;clearable&lt;/code&gt; &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-8311&apos;&gt; 🔗 PRIV-8311&lt;/a&gt;&lt;/li&gt;&lt;/ul&gt;',
                    },
                    {
                        pkg: 'web',
                        name: 'select',
                        time: '2025-05-22',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复点击已选中的值也会触发 &lt;code&gt;change&lt;/code&gt; 事件的问题&lt;/p&gt;',
                    },
                ],
                tooltip: [
                    {
                        pkg: 'web',
                        name: 'tooltip',
                        time: '2025-03-03',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Tooltip&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                trigger: [
                    {
                        pkg: 'web',
                        name: 'trigger',
                        time: '2025-03-03',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Trigger&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
            },
            mobile: {
                button: [
                    {
                        pkg: 'mobile',
                        name: 'button',
                        time: '2025-04-15',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;p&gt;&lt;code&gt;outline&lt;/code&gt; 风格的按钮背景色由透明变更为纯白色 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-7727&apos;&gt; 🔗 PRIV-7727&lt;/a&gt;&lt;/p&gt;',
                    },
                ],
                filters: [
                    {
                        pkg: 'mobile',
                        name: 'filters',
                        time: '2025-04-08',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Filters&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'input-number': [
                    {
                        pkg: 'mobile',
                        name: 'input-number',
                        time: '2025-04-29',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复设置了精度时，无法在输入过程中去除小数的问题&lt;/p&gt;',
                    },
                ],
                'input-search': [
                    {
                        pkg: 'mobile',
                        name: 'input-search',
                        time: '2025-03-06',
                        type: 'new',
                        marks: [],
                        author: '李镇宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;InputSearch&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'list-item': [
                    {
                        pkg: 'mobile',
                        name: 'list-item',
                        time: '2025-05-29',
                        type: 'new',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;ListItem&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'list-select': [
                    {
                        pkg: 'mobile',
                        name: 'list-select',
                        time: '2025-05-29',
                        type: 'new',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;ListSelect&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'pull-refresh': [
                    {
                        pkg: 'mobile',
                        name: 'pull-refresh',
                        time: '2025-04-30',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;PullRefresh&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                radio: [
                    {
                        pkg: 'mobile',
                        name: 'radio',
                        time: '2025-05-22',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复文本过长时 radio 图标会被挤压的问题&lt;/p&gt;',
                    },
                ],
                'search-list': [
                    {
                        pkg: 'mobile',
                        name: 'search-list',
                        time: '2025-04-17',
                        type: 'new',
                        marks: [],
                        author: '李镇宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;SearchList&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                'selected-panel': [
                    {
                        pkg: 'mobile',
                        name: 'selected-panel',
                        time: '2025-05-29',
                        type: 'new',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;SelectedPanel&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                selector: [
                    {
                        pkg: 'mobile',
                        name: 'selector',
                        time: '2025-04-09',
                        type: 'new',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;新增 &lt;code&gt;Selector&lt;/code&gt; 组件&lt;/p&gt;',
                    },
                ],
                switch: [
                    {
                        pkg: 'mobile',
                        name: 'switch',
                        time: '2025-05-29',
                        type: 'fix',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复 &lt;code&gt;Switch&lt;/code&gt; 组件可能被挤压的问题&lt;/p&gt;',
                    },
                ],
                tabs: [
                    {
                        pkg: 'mobile',
                        name: 'tabs',
                        time: '2025-04-17',
                        type: 'style',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;&lt;code&gt;line&lt;/code&gt; 类型新增 &lt;code&gt;huge&lt;/code&gt; 尺寸&lt;/p&gt;',
                    },
                    {
                        pkg: 'mobile',
                        name: 'tabs',
                        time: '2025-04-17',
                        type: 'feat',
                        marks: [],
                        author: '王炎',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;新增 &lt;code&gt;segmentBackground&lt;/code&gt; 用于扩展 &lt;code&gt;segment&lt;/code&gt; 类型的背景色 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-7986&apos;&gt; 🔗 PRIV-7986&lt;/a&gt;&lt;/li&gt;&lt;li&gt;新增 &lt;code&gt;plain&lt;/code&gt; 类型 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-7986&apos;&gt; 🔗 PRIV-7986&lt;/a&gt;&lt;/li&gt;&lt;li&gt;新增 &lt;code&gt;divider&lt;/code&gt; 属性，用于控制是否展示分隔线 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-7986&apos;&gt; 🔗 PRIV-7986&lt;/a&gt;&lt;/li&gt;&lt;li&gt;&lt;code&gt;line&lt;/code&gt; 类型仅有一个 tab 时，不展示下划线 &lt;a target=&apos;_blank&apos; href=&apos;https://jira.qiyuesuo.me/browse/PRIV-7986&apos;&gt; 🔗 PRIV-7986&lt;/a&gt;&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
                'virtual-list': [
                    {
                        pkg: 'mobile',
                        name: 'virtual-list',
                        time: '2025-04-16',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;现在支持在数据项中传入&lt;code&gt;divider&lt;/code&gt;属性，来控制特定项的分割线。&lt;/p&gt;',
                    },
                    {
                        pkg: 'mobile',
                        name: 'virtual-list',
                        time: '2025-04-16',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;支持通过&lt;code&gt;keyField&lt;/code&gt;属性指定数据项的唯一标识。&lt;/p&gt;',
                    },
                    {
                        pkg: 'mobile',
                        name: 'virtual-list',
                        time: '2025-04-16',
                        type: 'feat',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;支持通过&lt;code&gt;getData&lt;/code&gt;属性传入异步数据获取函数，实现无尽滚动效果。&lt;/p&gt;',
                    },
                ],
            },
            vant: {
                'index-bar': [
                    {
                        pkg: 'vant',
                        name: 'index-bar',
                        time: '2025-05-30',
                        type: 'fix',
                        marks: [],
                        author: '黄宇',
                        patch: false,
                        releaseHidden: false,
                        content: '&lt;p&gt;修复&lt;code&gt;stickyOffsetTop&lt;/code&gt;未适配 px2rem 缩放的问题&lt;/p&gt;',
                    },
                ],
            },
        },
    },
};

export default notes;
