import type { ReleaseNotes } from '@/../libs/gen-release-notes/_types';

const notes: ReleaseNotes = {
    unreleased: {
        date: '',
        logs: {
            web: {
                tooltip: [
                    {
                        pkg: 'web',
                        name: 'tooltip',
                        time: '2025-07-14',
                        type: 'feat',
                        marks: [],
                        author: '刘青',
                        patch: false,
                        releaseHidden: false,
                        content:
                            '&lt;ul&gt;&lt;li&gt;将内容文字颜色从 &lt;code&gt;colorText5&lt;/code&gt; 改为 &lt;code&gt;colorTextWhite&lt;/code&gt;&lt;/li&gt;&lt;li&gt;将背景色从 &lt;code&gt;colorMaskSpotlight&lt;/code&gt; 改为 &lt;code&gt;colorMaskBubble&lt;/code&gt;&lt;/li&gt;&lt;/ul&gt;',
                    },
                ],
            },
        },
    },
};

export default notes;
