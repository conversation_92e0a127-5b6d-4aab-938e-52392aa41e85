<template>
    <div class="mto-menu">
        <div class="mto-menu-search-wrapper">
            <mto-menu-settings
                v-if="categoryCtx?.substation === 'web'"
                class="settings-icon"
            />
            <mto-menu-search
                v-model="searchKeywords"
                :highlight-area="refContainer"
            />
        </div>
        <div
            ref="refContainer"
            class="mto-menu-container"
        >
            <nav>
                <mto-menu-group
                    v-for="group of filteredMenus"
                    :key="group.groupName"
                    :group="group"
                    :search-keywords="searchKeywords"
                    :unreleased-notes="unreleasedNotes"
                    :latest-notes="latestNotes"
                />
            </nav>
        </div>
    </div>
</template>
<script setup lang="ts">
import { merge } from 'lodash';
import { computed, inject, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router/composables';

import MtoMenuGroup from './mto-menu-group.vue';
import MtoMenuSearch from './mto-menu-search.vue';
import MtoMenuSettings from './mto-menu-settings.vue';

import { useDocMenu } from '@/composables/menu';
import { OverlayScrollbars } from '@/packages/overlayscrollbars';
import { routes } from '@/router';
import { useDevModeStore } from '@/store/dev-mode';
import { useSettingsStore } from '@/store/settings';
import { type SubstationContext, substationInjectionKey } from '@/utils/context';
import unreleasedNotes from '@/views/release-notes/data/unreleased';

const devModeStore = useDevModeStore();

const refContainer = ref<HTMLDivElement>();

const searchKeywords = ref('');

const categoryCtx = inject<SubstationContext>(substationInjectionKey);

const currentRoute = useRoute();

const settingsStore = useSettingsStore();

const menus = computed(() => {
    if (categoryCtx?.substation) {
        const substationRoute = routes.find(item => item.path === `/${categoryCtx.substation}`);

        const childrenRoutes = substationRoute?.children?.filter(item => {
            if (!settingsStore.showElementUI) {
                return !item.path.startsWith('el-');
            }

            return true;
        });

        if (!substationRoute) {
            console.error(`Can't find substation route: ${categoryCtx.substation}`);
            return [];
        }

        const needSort = !!substationRoute.meta?.sort;

        return useDocMenu(currentRoute, childrenRoutes, needSort, devModeStore.active).value;
    }
    return [];
});

const filterMenuName = (item: RouteConfig) => {
    return item.meta?.menuName.toLowerCase().includes(searchKeywords.value.toLowerCase());
};

const filteredMenus = computed(() => {
    if (!searchKeywords.value) return menus.value;
    return menus.value.map(group => {
        const children = group.children.filter(item => filterMenuName(item) || item.children?.some(filterMenuName));
        return {
            ...group,
            children,
        };
    }).filter(group => group.children.length);
});

const latestNotes = ref<any>(null);

onMounted(async () => {
    const files = import.meta.glob('../../release-notes/data/*');

    // 获取最新版本号日志
    const filesKeys = Object.keys(files).filter(f => !f.includes('unreleased')).sort((a, b) => {
        const reg = /v(\d)\.(\d+)/;
        const [, aMajor, aMinor] = a.match(reg) ?? [];
        const [, bMajor, bMinor] = b.match(reg) ?? [];

        return (+aMajor > +bMajor || +aMinor > +bMinor) ? -1 : 1;
    });

    const latest = files[filesKeys[0]];

    const res: any = await latest();
    let allLogs = {};

    for (const key in res.default) {
        if (Object.prototype.hasOwnProperty.call(res.default, key)) {
            const value = res.default[key];

            merge(allLogs, value.logs);
        }
    }

    latestNotes.value = allLogs;

    if (!refContainer.value) return;

    OverlayScrollbars(refContainer.value, {
        overflow: {
            x: 'hidden',
        },
        scrollbars: {
            autoHide: 'leave',
            autoHideDelay: 280,
        },
    });
});
</script>
<style lang="less" scoped>
.mto-menu {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    z-index: var(--mto-z-index-sidebar);
    padding: 32px 32px 96px;
    width: calc(100vw - 64px);
    max-width: 320px;
    background-color: var(--mto-sidebar-bg-color);
    opacity: 0;
    box-shadow: var(--mto-c-shadow-3);
    overflow: hidden;
    transform: translateX(-100%);
    transition: opacity 0.5s, transform 0.25s ease;
    overscroll-behavior: contain;

    .mto-menu-container {
        flex-grow: 1;
    }
}

@media (min-width: 960px) {
    .mto-menu {
        z-index: 1;
        padding: var(--mto-nav-height) 0 0;
        width: var(--mto-sidebar-width);
        max-width: 100%;
        background-color: var(--mto-sidebar-bg-color);
        opacity: 1;
        visibility: visible;
        box-shadow: none;
        transform: translateX(0);

        .mto-menu-search-wrapper {
            display: flex;
            justify-content: end;
            align-items: center;
            padding-left: ~'max(32px, calc((100vw - (var(--mto-layout-max-width) - 64px)) / 2))';
            padding-right: 32px;

            &:has(.settings-icon) {
                justify-content: space-between;
            }
        }

        .mto-menu-container {
            padding-left: ~'max(32px, calc((100vw - (var(--mto-layout-max-width) - 64px)) / 2))';
            padding-right: 32px;
        }
    }
}

@media (min-width: 1680px) {
    .mto-menu {
        width: calc((100vw - (var(--mto-layout-max-width) - 64px)) / 2 + var(--mto-sidebar-width) - 32px);
    }
}
</style>
