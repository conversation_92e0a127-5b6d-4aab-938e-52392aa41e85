<template>
    <div class="mto-article-wrapper">
        <div class="mto-article-container">
            <div class="aside-wrapper">
                <div class="aside-container">
                    <div class="aside-content">
                        <mto-article-aside />
                    </div>
                </div>
            </div>

            <div class="content-wrapper content">
                <div
                    ref="refContainer"
                    class="content-container"
                >
                    <main class="mto-article">
                        <router-view />
                    </main>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUpdated, ref } from 'vue';

import MtoArticleAside from './mto-article-aside.vue';

import { OverlayScrollbars } from '@/packages/overlayscrollbars';
import { runCbs } from '@/utils/content-update';


const refContainer = ref<HTMLDivElement>();

/**
 *
 */
function initScrollbar() {
    refContainer.value?.querySelectorAll<HTMLPreElement>('[class*=language-]')
        .forEach(async (item) => {
            OverlayScrollbars(item, {
                scrollbars: {
                    autoHide: 'never',
                },
            });
        });
}


onMounted(() => {
    runCbs();
    setTimeout(() => {
        initScrollbar();
    }, 60);
});

onUpdated(() => {
    runCbs();
    initScrollbar();
});
</script>

<style lang="less" scoped>
.mto-article-wrapper {
    padding: 32px 24px 96px;
    width: 100%;
}

@media (min-width: 768px) {
    .mto-article-wrapper {
        padding: 48px 32px 128px;
    }
}

@media (min-width: 960px) {
    .mto-article-wrapper {
        padding: 32px 32px 0;
    }
}

@media (min-width: 1280px) {
    .mto-article-wrapper .mto-article-container {
        display: flex;
        justify-content: center;
    }

    .mto-article-wrapper .aside-wrapper {
        display: block;
    }
}

.mto-article-container {
    margin: 0 auto;
    width: 100%;
}

.content-wrapper {
    position: relative;
    margin: 0 auto;
    width: 100%;
}

@media (min-width: 960px) {
    .content-wrapper {
        padding: 0 32px 128px;
    }
}

@media (min-width: 1280px) {
    .content-wrapper {
        order: 1;
        margin: 0;
        min-width: 640px;
    }
}


.content-container {
    margin: 0 auto;
}

// ----------------------

.aside-wrapper {
    position: relative;
    display: none;
    order: 2;
    flex-grow: 1;
    padding-left: 32px;
    width: 100%;
    max-width: 256px;

    .aside-container {
        position: fixed;
        top: 0;
        padding-top: calc(var(--mto-nav-height) + var(--mto-layout-top-height, 0px) + var(--mto-doc-top-height, 0px) + 32px);
        width: 224px;
        height: 100vh;
        overflow-x: hidden;
        overflow-y: auto;
        scrollbar-width: none;
    }

    .aside-container::-webkit-scrollbar {
        display: none;
    }

    .aside-content {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - (var(--mto-nav-height) + var(--mto-layout-top-height, 0px) + 32px));
        padding-bottom: 32px;
        padding-left: 8px;
    }

}
</style>
