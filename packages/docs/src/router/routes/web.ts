// Do not modify this file!!!
// it was auto generated by libs/gen-routes

import type { RouteConfig } from 'vue-router';

export const webRoutes: RouteConfig[] = [
    {
        path: '/web',
        name: 'Web',
        redirect: '/web/overview',
        component: { render: (h) => h('router-view') },
        meta: {
            nav: true,
            name: 'Web',
            sort: true,
            group: 'desktop',
        },
        children: [
            {
                path: 'overview',
                name: 'WebOverview',
                component: { render: (h) => h('components-overview', { props: { subStation: 'web' } }) },
                meta: {
                    group: 'x',
                    menuName: '组件总览',
                },
            },
            {
                path: 'icon',
                name: 'WebIcon',
                component: () => import('@mitra-design/icons/__demos__/icon.md'),
                meta: {
                    group: '其他',
                    menuName: 'Icon 图标',
                },
            },
            {
                path: 'alert',
                name: 'WebAlert',
                component: () => import('@mitra-design/web/alert/__demos__/alert.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Alert 警告提示',
                    componentDir: '@mitra-design/web/alert/',
                    category: 'component',
                    coverFile: 'alert.png',
                    author: '王炎',
                    group: '反馈',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=5679-44103&mode=design&t=CObvEIGkjq8OeiLX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'alert',
                    enName: 'Alert',
                    zhName: '警告提示',
                },
            },
            {
                path: 'anchor',
                name: 'WebAnchor',
                component: () => import('@mitra-design/web/anchor/__demos__/anchor.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Anchor 锚点',
                    componentDir: '@mitra-design/web/anchor/',
                    category: 'component',
                    coverFile: '',
                    author: '李镇宇',
                    group: '导航',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=28132-76446&t=CYRQheZAiqtZcekW-4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'anchor',
                    enName: 'Anchor',
                    zhName: '锚点',
                },
            },
            {
                path: 'async-group-select',
                name: 'WebAsyncGroupSelect',
                component: () => import('@mitra-design/web/async-group-select/__demos__/async-group-select.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'AsyncGroupSelect 异步分组下拉选择器',
                    componentDir: '@mitra-design/web/async-group-select/',
                    category: 'component',
                    coverFile: '',
                    author: '杨甜',
                    group: '数据录入',
                    projectVersion: 'private-sign@4.3.6',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=3932-23213&mode=design&t=CObvEIGkjq8OeiLX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'async-group-select',
                    enName: 'AsyncGroupSelect',
                    zhName: '异步分组下拉选择器',
                },
            },
            {
                path: 'auto-ellipsis',
                name: 'WebAutoEllipsis',
                component: () => import('@mitra-design/web/auto-ellipsis/__demos__/auto-ellipsis.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'AutoEllipsis 自动省略',
                    componentDir: '@mitra-design/web/auto-ellipsis/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'auto-ellipsis',
                    enName: 'AutoEllipsis',
                    zhName: '自动省略',
                },
            },
            {
                path: 'breadcrumb',
                name: 'WebBreadcrumb',
                component: () => import('@mitra-design/web/breadcrumb/__demos__/breadcrumb.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Breadcrumb 面包屑',
                    componentDir: '@mitra-design/web/breadcrumb/',
                    category: 'component',
                    coverFile: '',
                    author: '李镇宇',
                    group: '导航',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=21390-58190&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'breadcrumb',
                    enName: 'Breadcrumb',
                    zhName: '面包屑',
                },
            },
            {
                path: 'button',
                name: 'WebButton',
                component: () => import('@mitra-design/web/button/__demos__/button.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Button 按钮',
                    componentDir: '@mitra-design/web/button/',
                    category: 'component',
                    coverFile: 'button.svg',
                    author: '刘青',
                    group: '通用',
                    projectVersion: 'private-sign@4.3.7',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'button',
                    enName: 'Button',
                    zhName: '按钮',
                },
            },
            {
                path: 'button-group',
                name: 'WebButtonGroup',
                component: () => import('@mitra-design/web/button-group/__demos__/button-group.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'ButtonGroup 按钮组合',
                    componentDir: '@mitra-design/web/button-group/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '通用',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=11463%3A36907&mode=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'button-group',
                    enName: 'ButtonGroup',
                    zhName: '按钮组合',
                },
            },
            {
                path: 'card',
                name: 'WebCard',
                component: () => import('@mitra-design/web/card/__demos__/card.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Card ',
                    componentDir: '@mitra-design/web/card/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'card',
                    enName: 'Card',
                    zhName: '',
                },
            },
            {
                path: 'cascader',
                name: 'WebCascader',
                component: () => import('@mitra-design/web/cascader/__demos__/cascader.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Cascader 级联选择器',
                    componentDir: '@mitra-design/web/cascader/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=4229-33559&node-type=rounded_rectangle&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'cascader',
                    enName: 'Cascader',
                    zhName: '级联选择器',
                },
            },
            {
                path: 'checkbox',
                name: 'WebCheckbox',
                component: () => import('@mitra-design/web/checkbox/__demos__/checkbox.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Checkbox 复选框',
                    componentDir: '@mitra-design/web/checkbox/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=1685-10404&node-type=rounded_rectangle&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'checkbox',
                    enName: 'Checkbox',
                    zhName: '复选框',
                },
            },
            {
                path: 'color-picker',
                name: 'WebColorPicker',
                component: () => import('@mitra-design/web/color-picker/__demos__/color-picker.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'ColorPicker 颜色选择器',
                    componentDir: '@mitra-design/web/color-picker/',
                    category: 'component',
                    coverFile: '',
                    author: '丁波',
                    group: '数据录入',
                    projectVersion: 'private-sign@4.3.10',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=20650-47907&mode=design&t=bpv1LES8YB3MaRiL-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'color-picker',
                    enName: 'ColorPicker',
                    zhName: '颜色选择器',
                },
            },
            {
                path: 'date-select',
                name: 'WebDateSelect',
                component: () => import('@mitra-design/web/date-select/__demos__/date-select.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'DateSelect 日期选择器',
                    componentDir: '@mitra-design/web/date-select/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@4.3.5',
                    deprecated:
                        '此组件只在 private-sign@4.3.5 中使用。在 private-sign@4.3.6 中我们对 `el-date-picker`[文档]() 进行了改造，已完全覆盖，完全取代了此组件。',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'date-select',
                    enName: 'DateSelect',
                    zhName: '日期选择器',
                },
            },
            {
                path: 'descriptions',
                name: 'WebDescriptions',
                component: () => import('@mitra-design/web/descriptions/__demos__/descriptions.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Descriptions 描述列表',
                    componentDir: '@mitra-design/web/descriptions/',
                    category: 'component',
                    coverFile: '',
                    author: '黄宇',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.7',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'descriptions',
                    enName: 'Descriptions',
                    zhName: '描述列表',
                },
            },
            {
                path: 'draggable-layout',
                name: 'WebDraggableLayout',
                component: () => import('@mitra-design/web/draggable-layout/__demos__/draggable-layout.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'DraggableLayout 可拖拽布局',
                    componentDir: '@mitra-design/web/draggable-layout/',
                    category: 'component',
                    coverFile: '',
                    author: '杨旺旺',
                    group: '布局',
                    projectVersion: 'private-sign@4.3.7',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'draggable-layout',
                    enName: 'DraggableLayout',
                    zhName: '可拖拽布局',
                },
            },
            {
                path: 'drawer',
                name: 'WebDrawer',
                component: () => import('@mitra-design/web/drawer/__demos__/drawer.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Drawer 抽屉',
                    componentDir: '@mitra-design/web/drawer/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '反馈',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'drawer',
                    enName: 'Drawer',
                    zhName: '抽屉',
                },
            },
            {
                path: 'dropdown',
                name: 'WebDropdown',
                component: () => import('@mitra-design/web/dropdown/__demos__/dropdown.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Dropdown 下拉菜单',
                    componentDir: '@mitra-design/web/dropdown/',
                    category: 'component',
                    coverFile: '',
                    author: '李镇宇',
                    group: '导航',
                    projectVersion: 'private-sign@5.3',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=3932-23213&t=eQjjGX3R4JScTJgQ-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'dropdown',
                    enName: 'Dropdown',
                    zhName: '下拉菜单',
                },
            },
            {
                path: 'form',
                name: 'WebForm',
                component: () => import('@mitra-design/web/form/__demos__/form.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Form 表单',
                    componentDir: '@mitra-design/web/form/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=2469-31911&node-type=frame&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'form',
                    enName: 'Form',
                    zhName: '表单',
                },
            },
            {
                path: 'grid',
                name: 'WebGrid',
                component: () => import('@mitra-design/web/grid/__demos__/grid.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Grid 栅格布局',
                    componentDir: '@mitra-design/web/grid/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '布局',
                    projectVersion: 'private-sign@5.2',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'grid',
                    enName: 'Grid',
                    zhName: '栅格布局',
                },
            },
            {
                path: 'group-tree',
                name: 'WebGroupTree',
                component: () => import('@mitra-design/web/group-tree/__demos__/group-tree.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'GroupTree 分组树',
                    componentDir: '@mitra-design/web/group-tree/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '业务组件',
                    projectVersion: 'private-sign@4.3.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'group-tree',
                    enName: 'GroupTree',
                    zhName: '分组树',
                },
            },
            {
                path: 'image-cropper',
                name: 'WebImageCropper',
                component: () => import('@mitra-design/web/image-cropper/__demos__/image-cropper.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'ImageCropper 图片剪裁',
                    componentDir: '@mitra-design/web/image-cropper/',
                    category: 'component',
                    coverFile: '',
                    author: '丁波',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.0',
                    figma: 'https://www.figma.com/design/DHeX2y1irabrhcfqm52Bbq/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E4%B8%9A%E5%8A%A1%E7%BB%84%E4%BB%B6?node-id=10848-35975&t=yGn39ebRtDx6RQGO-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'image-cropper',
                    enName: 'ImageCropper',
                    zhName: '图片剪裁',
                },
            },
            {
                path: 'infinite-scroll-list',
                name: 'WebInfiniteScrollList',
                component: () => import('@mitra-design/web/infinite-scroll-list/__demos__/infinite-scroll-list.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'InfiniteScrollList 无限滚动列表',
                    componentDir: '@mitra-design/web/infinite-scroll-list/',
                    category: 'component',
                    coverFile: '',
                    author: '黄宇',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'infinite-scroll-list',
                    enName: 'InfiniteScrollList',
                    zhName: '无限滚动列表',
                },
            },
            {
                path: 'input',
                name: 'WebInput',
                component: () => import('@mitra-design/web/input/__demos__/input.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Input 输入框',
                    componentDir: '@mitra-design/web/input/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'input',
                    enName: 'Input',
                    zhName: '输入框',
                },
            },
            {
                path: 'input-combined',
                name: 'WebInputCombined',
                component: () => import('@mitra-design/web/input-combined/__demos__/input-combined.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'InputCombined 聚合输入框',
                    componentDir: '@mitra-design/web/input-combined/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'input-combined',
                    enName: 'InputCombined',
                    zhName: '聚合输入框',
                },
            },
            {
                path: 'input-number',
                name: 'WebInputNumber',
                component: () => import('@mitra-design/web/input-number/__demos__/input-number.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'InputNumber 数字输入框',
                    componentDir: '@mitra-design/web/input-number/',
                    category: 'component',
                    coverFile: 'input-number.svg',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=4369-36515&mode=design&t=j10ucqGj9CkL12rL-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'input-number',
                    enName: 'InputNumber',
                    zhName: '数字输入框',
                },
            },
            {
                path: 'input-password',
                name: 'WebInputPassword',
                component: () => import('@mitra-design/web/input-password/__demos__/input-password.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'InputPassword 密码框',
                    componentDir: '@mitra-design/web/input-password/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=5380-41676&mode=design&t=CObvEIGkjq8OeiLX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'input-password',
                    enName: 'InputPassword',
                    zhName: '密码框',
                },
            },
            {
                path: 'input-search',
                name: 'WebInputSearch',
                component: () => import('@mitra-design/web/input-search/__demos__/input-search.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'InputSearch 搜索框',
                    componentDir: '@mitra-design/web/input-search/',
                    category: 'component',
                    coverFile: 'input-search.svg',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=5380-41676&mode=design&t=CObvEIGkjq8OeiLX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'input-search',
                    enName: 'InputSearch',
                    zhName: '搜索框',
                },
            },
            {
                path: 'loading',
                name: 'WebLoading',
                component: () => import('@mitra-design/web/loading/__demos__/loading.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Loading 加载中',
                    componentDir: '@mitra-design/web/loading/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '反馈',
                    projectVersion: 'private-sign@4.3.9',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'loading',
                    enName: 'Loading',
                    zhName: '加载中',
                },
            },
            {
                path: 'menu',
                name: 'WebMenu',
                component: () => import('@mitra-design/web/menu/__demos__/menu.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Menu 系统菜单',
                    componentDir: '@mitra-design/web/menu/',
                    category: 'component',
                    coverFile: '',
                    author: '杨甜',
                    group: '导航',
                    projectVersion: 'private-sign@4.3.9',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'menu',
                    enName: 'Menu',
                    zhName: '系统菜单',
                },
            },
            {
                path: 'message-box',
                name: 'WebMessageBox',
                component: () => import('@mitra-design/web/message-box/__demos__/message-box.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'MessageBox 弹框',
                    componentDir: '@mitra-design/web/message-box/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '反馈',
                    projectVersion: 'private-sign@4.3.3',
                    deprecated: '已自研<mt-confirm-modal>组件（[文档](/web/modal/confirm-modal)）, `<mt-message-box>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'message-box',
                    enName: 'MessageBox',
                    zhName: '弹框',
                },
            },
            {
                path: 'modal',
                name: 'WebModal',
                component: { render: (h) => h('router-view') },
                meta: {
                    pkg: 'web',
                    menuName: 'Modal 弹窗',
                    componentDir: '@mitra-design/web/modal/',
                    category: 'component-group',
                    coverFile: 'modal.svg',
                    author: '刘青',
                    group: '反馈',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'modal',
                    enName: 'Modal',
                    zhName: '弹窗',
                },
                redirect: 'modal/main',
                children: [
                    {
                        path: 'main',
                        name: 'WebModalMain',
                        component: () => import('@mitra-design/web/modal/__demos__/modal.md'),
                        meta: {
                            pkg: 'web',
                            menuName: 'Modal',
                            category: 'component',
                            author: '刘青',
                            group: '反馈',
                            projectVersion: 'private-sign@5.3',
                            docType: 'component',
                            pkgName: 'web',
                            componentName: 'modal',
                            enName: 'Modal',
                            zhName: '弹窗',
                            index: 1000,
                        },
                    },
                    {
                        path: 'confirm-modal',
                        name: 'WebModalConfirmModal',
                        component: () => import('@mitra-design/web/modal/__demos__/confirm-modal.md'),
                        meta: {
                            pkg: 'web',
                            menuName: 'ConfirmModal',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'web',
                            componentName: 'modal',
                            enName: 'Modal',
                            zhName: '弹窗',
                            index: 1099,
                        },
                    },
                ],
            },
            {
                path: 'popconfirm',
                name: 'WebPopconfirm',
                component: () => import('@mitra-design/web/popconfirm/__demos__/popconfirm.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Popconfirm 气泡确认框',
                    componentDir: '@mitra-design/web/popconfirm/',
                    category: 'component',
                    coverFile: '',
                    author: '李镇宇',
                    group: '反馈',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'popconfirm',
                    enName: 'Popconfirm',
                    zhName: '气泡确认框',
                },
            },
            {
                path: 'popover',
                name: 'WebPopover',
                component: () => import('@mitra-design/web/popover/__demos__/popover.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Popover 气泡卡片',
                    componentDir: '@mitra-design/web/popover/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'popover',
                    enName: 'Popover',
                    zhName: '气泡卡片',
                },
            },
            {
                path: 'popover-menu',
                name: 'WebPopoverMenu',
                component: () => import('@mitra-design/web/popover-menu/__demos__/popover-menu.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'PopoverMenu 弹出菜单',
                    componentDir: '@mitra-design/web/popover-menu/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '导航',
                    projectVersion: 'private-sign@4.3.4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'popover-menu',
                    enName: 'PopoverMenu',
                    zhName: '弹出菜单',
                },
            },
            {
                path: 'radio',
                name: 'WebRadio',
                component: () => import('@mitra-design/web/radio/__demos__/radio.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Radio 单选框',
                    componentDir: '@mitra-design/web/radio/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=1643-6707&node-type=frame&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'radio',
                    enName: 'Radio',
                    zhName: '单选框',
                },
            },
            {
                path: 'resize-box',
                name: 'WebResizeBox',
                component: () => import('@mitra-design/web/resize-box/__demos__/resize-box.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'ResizeBox 伸缩框',
                    componentDir: '@mitra-design/web/resize-box/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '通用',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'resize-box',
                    enName: 'ResizeBox',
                    zhName: '伸缩框',
                },
            },
            {
                path: 'result',
                name: 'WebResult',
                component: () => import('@mitra-design/web/result/__demos__/result.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Result 结果页',
                    componentDir: '@mitra-design/web/result/',
                    category: 'component',
                    coverFile: 'result.png',
                    author: '王炎',
                    group: '反馈',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=4186-39487&mode=design&t=qFfZFCYmd8TwiPtX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'result',
                    enName: 'Result',
                    zhName: '结果页',
                },
            },
            {
                path: 'scened-empty',
                name: 'WebScenedEmpty',
                component: () => import('@mitra-design/web/scened-empty/__demos__/scened-empty.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'ScenedEmpty 场景化空状态',
                    componentDir: '@mitra-design/web/scened-empty/',
                    category: 'component',
                    coverFile: '',
                    group: '反馈',
                    author: '黄宇',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'scened-empty',
                    enName: 'ScenedEmpty',
                    zhName: '场景化空状态',
                },
            },
            {
                path: 'scroll',
                name: 'WebScroll',
                component: () => import('@mitra-design/web/scroll/__demos__/scroll.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Scroll 滚动条',
                    componentDir: '@mitra-design/web/scroll/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '通用',
                    projectVersion: 'private-sign@5.1',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=18645-42105&t=mw5gFlsAAWVqVyfy-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'scroll',
                    enName: 'Scroll',
                    zhName: '滚动条',
                },
            },
            {
                path: 'second-sidebar',
                name: 'WebSecondSidebar',
                component: () => import('@mitra-design/web/second-sidebar/__demos__/second-sidebar.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SecondSidebar 二级页侧边导航栏',
                    componentDir: '@mitra-design/web/second-sidebar/',
                    category: 'component',
                    coverFile: '',
                    author: '杨甜',
                    group: '布局',
                    projectVersion: 'private-sign@4.3.4',
                    figma: 'https://www.figma.com/design/DHeX2y1irabrhcfqm52Bbq/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E4%B8%9A%E5%8A%A1%E7%BB%84%E4%BB%B6?node-id=8936-64333&t=zqCxkUVQIjllxMiP-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'second-sidebar',
                    enName: 'SecondSidebar',
                    zhName: '二级页侧边导航栏',
                },
            },
            {
                path: 'segmented',
                name: 'WebSegmented',
                component: () => import('@mitra-design/web/segmented/__demos__/segmented.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Segmented 分段控制器',
                    componentDir: '@mitra-design/web/segmented/',
                    category: 'component',
                    coverFile: 'segmented.svg',
                    author: '杨甜',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=10409-33508&mode=design&t=iTliiu6csAHRUdBn-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'segmented',
                    enName: 'Segmented',
                    zhName: '分段控制器',
                },
            },
            {
                path: 'select',
                name: 'WebSelect',
                component: () => import('@mitra-design/web/select/__demos__/select.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Select 下拉选择器',
                    componentDir: '@mitra-design/web/select/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@4.3.4',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=3932-23213&mode=design&t=CObvEIGkjq8OeiLX-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'select',
                    enName: 'Select',
                    zhName: '下拉选择器',
                },
            },
            {
                path: 'select-input',
                name: 'WebSelectInput',
                component: () => import('@mitra-design/web/select-input/__demos__/select-input.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SelectInput 选择器输入框',
                    componentDir: '@mitra-design/web/select-input/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'select-input',
                    enName: 'SelectInput',
                    zhName: '选择器输入框',
                },
            },
            {
                path: 'select-panel',
                name: 'WebSelectPanel',
                component: () => import('@mitra-design/web/select-panel/__demos__/select-panel.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SelectPanel 选择器面板',
                    componentDir: '@mitra-design/web/select-panel/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '底层组件',
                    projectVersion: 'private-sign@4.3.4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'select-panel',
                    enName: 'SelectPanel',
                    zhName: '选择器面板',
                },
            },
            {
                path: 'select-popover',
                name: 'WebSelectPopover',
                component: () => import('@mitra-design/web/select-popover/__demos__/select-popover.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SelectPopover 下拉选择气泡',
                    componentDir: '@mitra-design/web/select-popover/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '底层组件',
                    projectVersion: 'private-sign@4.3.4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'select-popover',
                    enName: 'SelectPopover',
                    zhName: '下拉选择气泡',
                },
            },
            {
                path: 'skeleton',
                name: 'WebSkeleton',
                component: () => import('@mitra-design/web/skeleton/__demos__/skeleton.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Skeleton 骨架屏',
                    componentDir: '@mitra-design/web/skeleton/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '反馈',
                    projectVersion: 'private-sign@4.3.8',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=14581%3A60673&mode=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'skeleton',
                    enName: 'Skeleton',
                    zhName: '骨架屏',
                },
            },
            {
                path: 'smooth-scrollbar',
                name: 'WebSmoothScrollbar',
                component: () => import('@mitra-design/web/smooth-scrollbar/__demos__/smooth-scrollbar.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SmoothScrollbar 平滑滚动条',
                    componentDir: '@mitra-design/web/smooth-scrollbar/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '通用',
                    projectVersion: 'private-sign@4.3.4',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'smooth-scrollbar',
                    enName: 'SmoothScrollbar',
                    zhName: '平滑滚动条',
                },
            },
            {
                path: 'steps',
                name: 'WebSteps',
                component: () => import('@mitra-design/web/steps/__demos__/steps.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Steps 步骤条',
                    componentDir: '@mitra-design/web/steps/',
                    category: 'component',
                    coverFile: 'steps.png',
                    author: '王炎',
                    group: '导航',
                    projectVersion: 'private-sign@4.3.7',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'steps',
                    enName: 'Steps',
                    zhName: '步骤条',
                },
            },
            {
                path: 'svg-icon',
                name: 'WebSvgIcon',
                component: () => import('@mitra-design/web/svg-icon/__demos__/svg-icon.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'SvgIcon 图标',
                    componentDir: '@mitra-design/web/svg-icon/',
                    category: 'component',
                    coverFile: '',
                    author: '黄宇',
                    group: '通用',
                    projectVersion: 'private-sign@4.3.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'svg-icon',
                    enName: 'SvgIcon',
                    zhName: '图标',
                },
            },
            {
                path: 'switch',
                name: 'WebSwitch',
                component: () => import('@mitra-design/web/switch/__demos__/switch.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Switch 开关',
                    componentDir: '@mitra-design/web/switch/',
                    category: 'component',
                    coverFile: '',
                    author: '王炎',
                    group: '数据录入',
                    projectVersion: 'private-sign@5.2',
                    figma: 'https://www.figma.com/design/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=28028-54652&node-type=rounded_rectangle&m=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'switch',
                    enName: 'Switch',
                    zhName: '开关',
                },
            },
            {
                path: 'tag',
                name: 'WebTag',
                component: () => import('@mitra-design/web/tag/__demos__/tag.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Tag 标签',
                    componentDir: '@mitra-design/web/tag/',
                    category: 'component',
                    coverFile: 'tag.svg',
                    author: '王炎',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=2606-14946&mode=design&t=laCfo66jig8dJOoP-0',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'tag',
                    enName: 'Tag',
                    zhName: '标签',
                },
            },
            {
                path: 'tag-group',
                name: 'WebTagGroup',
                component: () => import('@mitra-design/web/tag-group/__demos__/tag-group.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'TagGroup 标签组',
                    componentDir: '@mitra-design/web/tag-group/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.2',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'tag-group',
                    enName: 'TagGroup',
                    zhName: '标签组',
                },
            },
            {
                path: 'tips',
                name: 'WebTips',
                component: () => import('@mitra-design/web/tips/__demos__/tips.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Tips 提示',
                    componentDir: '@mitra-design/web/tips/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.1 | private-oss@5.1',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'tips',
                    enName: 'Tips',
                    zhName: '提示',
                },
            },
            {
                path: 'tooltip',
                name: 'WebTooltip',
                component: () => import('@mitra-design/web/tooltip/__demos__/tooltip.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Tooltip 气泡提示',
                    componentDir: '@mitra-design/web/tooltip/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.3',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'tooltip',
                    enName: 'Tooltip',
                    zhName: '气泡提示',
                },
            },
            {
                path: 'trigger',
                name: 'WebTrigger',
                component: () => import('@mitra-design/web/trigger/__demos__/trigger.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Trigger 弹出层触发器',
                    componentDir: '@mitra-design/web/trigger/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@5.3',
                    dev: 'true',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'trigger',
                    enName: 'Trigger',
                    zhName: '弹出层触发器',
                },
            },
            {
                path: 'typography',
                name: 'WebTypography',
                component: () => import('@mitra-design/web/typography/__demos__/typography.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'Typography 排版',
                    componentDir: '@mitra-design/web/typography/',
                    category: 'component',
                    coverFile: 'typography.png',
                    author: '刘青',
                    group: '通用',
                    projectVersion: 'private-sign@4.3.7',
                    figma: 'https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?node-id=4598%3A35643&mode=dev',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'typography',
                    enName: 'Typography',
                    zhName: '排版',
                },
            },
            {
                path: 'virtual-list',
                name: 'WebVirtualList',
                component: () => import('@mitra-design/web/virtual-list/__demos__/virtual-list.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'VirtualList 虚拟列表',
                    componentDir: '@mitra-design/web/virtual-list/',
                    category: 'component',
                    coverFile: '',
                    author: '刘青',
                    group: '数据展示',
                    projectVersion: 'private-sign@4.3.5',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'virtual-list',
                    enName: 'VirtualList',
                    zhName: '虚拟列表',
                },
            },
            {
                path: 'virtual-tree',
                name: 'WebVirtualTree',
                component: () => import('@mitra-design/web/virtual-tree/__demos__/virtual-tree.md'),
                meta: {
                    pkg: 'web',
                    menuName: 'VirtualTree 虚拟树',
                    componentDir: '@mitra-design/web/virtual-tree/',
                    category: 'component',
                    coverFile: '',
                    author: '王一帅',
                    group: '数据展示',
                    projectVersion: '暂未确定',
                    docType: 'component',
                    pkgName: 'web',
                    componentName: 'virtual-tree',
                    enName: 'VirtualTree',
                    zhName: '虚拟树',
                },
            },
            {
                path: 'el-autocomplete',
                name: 'ElAutocomplete',
                component: () => import('@mitra-design/element-ui/components/autocomplete/__demos__/autocomplete.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Autocomplete 自动补全输入框',
                    componentDir: '@mitra-design/element-ui/components/autocomplete/',
                    category: 'component',
                    coverFile: '',
                    group: '数据录入',
                    elementUi: 'original',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'autocomplete',
                    enName: 'Autocomplete',
                    zhName: '自动补全输入框',
                },
            },
            {
                path: 'el-avatar',
                name: 'ElAvatar',
                component: () => import('@mitra-design/element-ui/components/avatar/__demos__/avatar.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Avatar 头像',
                    componentDir: '@mitra-design/element-ui/components/avatar/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'avatar',
                    enName: 'Avatar',
                    zhName: '头像',
                },
            },
            {
                path: 'el-badge',
                name: 'ElBadge',
                component: () => import('@mitra-design/element-ui/components/badge/__demos__/badge.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Badge 标记',
                    componentDir: '@mitra-design/element-ui/components/badge/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'badge',
                    enName: 'Badge',
                    zhName: '标记',
                },
            },
            {
                path: 'el-breadcrumb',
                name: 'ElBreadcrumb',
                component: () => import('@mitra-design/element-ui/components/breadcrumb/__demos__/breadcrumb.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Breadcrumb 面包屑',
                    componentDir: '@mitra-design/element-ui/components/breadcrumb/',
                    category: 'component',
                    coverFile: '',
                    group: '导航',
                    elementUi: 'original',
                    deprecated: '已自研<mt-breadcrumb>组件（[文档](/web/breadcrumb)）, `<el-breadcrumb>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'breadcrumb',
                    enName: 'Breadcrumb',
                    zhName: '面包屑',
                },
            },
            {
                path: 'el-button',
                name: 'ElButton',
                component: { render: (h) => h('router-view') },
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Button 按钮',
                    componentDir: '@mitra-design/element-ui/components/button/',
                    category: 'component-group',
                    coverFile: 'button.png',
                    elementUi: 'both-modified',
                    group: '通用',
                    deprecated: '已自研<mt-button>组件（[文档](/web/button)）, `<el-button>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'button',
                    enName: 'Button',
                    zhName: '按钮',
                },
                redirect: 'el-button/main',
                children: [
                    {
                        path: 'main',
                        name: 'ElButtonMain',
                        component: () => import('@mitra-design/element-ui/components/button/__demos__/button.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'Button',
                            category: 'component',
                            elementUi: 'both-modified',
                            group: '通用',
                            deprecated: '已自研<mt-button>组件（[文档](/web/button)）, `<el-button>`后续不再使用。',
                            docType: 'component',
                            pkgName: 'element-ui',
                            componentName: 'button',
                            enName: 'Button',
                            zhName: '按钮',
                            index: 1000,
                        },
                    },
                    {
                        path: 'group',
                        name: 'ElButtonGroup',
                        component: () => import('@mitra-design/element-ui/components/button/__demos__/button-group.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'ButtonGroup',
                            category: 'component',
                            deprecated: '已自研`<mt-button-group>`组件（[文档](/web/button-group)）, `<el-button-group>`后续不再使用。',
                            docType: 'component-sub',
                            pkgName: 'element-ui',
                            componentName: 'button',
                            enName: 'Button',
                            zhName: '按钮',
                            index: 1099,
                        },
                    },
                    {
                        path: 'text',
                        name: 'ElButtonText',
                        component: () => import('@mitra-design/element-ui/components/button/__demos__/button-text.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'ButtonText',
                            category: 'component',
                            deprecated: '已自研`<mt-button>`组件（[文档](/web/button)）, `<el-button-text>`后续不再使用。',
                            docType: 'component-sub',
                            pkgName: 'element-ui',
                            componentName: 'button',
                            enName: 'Button',
                            zhName: '按钮',
                            index: 1099,
                        },
                    },
                ],
            },
            {
                path: 'el-card',
                name: 'ElCard',
                component: () => import('@mitra-design/element-ui/components/card/__demos__/card.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Card 卡片',
                    componentDir: '@mitra-design/element-ui/components/card/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'card',
                    enName: 'Card',
                    zhName: '卡片',
                },
            },
            {
                path: 'el-carousel',
                name: 'ElCarousel',
                component: () => import('@mitra-design/element-ui/components/carousel/__demos__/carousel.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Carousel 走马灯',
                    componentDir: '@mitra-design/element-ui/components/carousel/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'carousel',
                    enName: 'Carousel',
                    zhName: '走马灯',
                },
            },
            {
                path: 'el-cascader',
                name: 'ElCascader',
                component: { render: (h) => h('router-view') },
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Cascader 级联选择器',
                    componentDir: '@mitra-design/element-ui/components/cascader/',
                    category: 'component-group',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-cascader>组件（[文档](/web/cascader)）, `<el-cascader>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'cascader',
                    enName: 'Cascader',
                    zhName: '级联选择器',
                },
                redirect: 'el-cascader/main',
                children: [
                    {
                        path: 'main',
                        name: 'ElCascaderMain',
                        component: () => import('@mitra-design/element-ui/components/cascader/__demos__/cascader.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'Cascader',
                            category: 'component',
                            elementUi: 'original',
                            group: '数据录入',
                            deprecated: '已自研<mt-cascader>组件（[文档](/web/cascader)）, `<el-cascader>`后续不再使用。',
                            docType: 'component',
                            pkgName: 'element-ui',
                            componentName: 'cascader',
                            enName: 'Cascader',
                            zhName: '级联选择器',
                            index: 1000,
                        },
                    },
                    {
                        path: 'panel',
                        name: 'ElCascaderPanel',
                        component: () => import('@mitra-design/element-ui/components/cascader/__demos__/cascader-panel.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'CascaderPanel',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'element-ui',
                            componentName: 'cascader',
                            enName: 'Cascader',
                            zhName: '级联选择器',
                            index: 1099,
                        },
                    },
                ],
            },
            {
                path: 'el-checkbox',
                name: 'ElCheckbox',
                component: () => import('@mitra-design/element-ui/components/checkbox/__demos__/checkbox.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Checkbox 多选框',
                    componentDir: '@mitra-design/element-ui/components/checkbox/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-checkbox>组件（[文档](/web/checkbox)）, `<el-checkbox>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'checkbox',
                    enName: 'Checkbox',
                    zhName: '多选框',
                },
            },
            {
                path: 'el-collapse',
                name: 'ElCollapse',
                component: () => import('@mitra-design/element-ui/components/collapse/__demos__/collapse.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Collapse 折叠面板',
                    componentDir: '@mitra-design/element-ui/components/collapse/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '导航',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'collapse',
                    enName: 'Collapse',
                    zhName: '折叠面板',
                },
            },
            {
                path: 'el-color-picker',
                name: 'ElColorPicker',
                component: () => import('@mitra-design/element-ui/components/color-picker/__demos__/color-picker.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'ColorPicker 颜色选择器',
                    componentDir: '@mitra-design/element-ui/components/color-picker/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-color-picker>组件（[文档](/web/color-picker)）, `<el-color-picker>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'color-picker',
                    enName: 'ColorPicker',
                    zhName: '颜色选择器',
                },
            },
            {
                path: 'el-container',
                name: 'ElContainer',
                component: () => import('@mitra-design/element-ui/components/container/__demos__/container.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Container 布局容器',
                    componentDir: '@mitra-design/element-ui/components/container/',
                    category: 'component',
                    coverFile: 'container.png',
                    elementUi: 'original',
                    group: '布局',
                    deprecated: '已废弃，新组件暂未规划',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'container',
                    enName: 'Container',
                    zhName: '布局容器',
                },
            },
            {
                path: 'el-date-picker',
                name: 'ElDatePicker',
                component: { render: (h) => h('router-view') },
                meta: {
                    pkg: 'element-ui',
                    menuName: 'DatePicker 日期选择器',
                    componentDir: '@mitra-design/element-ui/components/date-picker/',
                    category: 'component-group',
                    coverFile: '',
                    elementUi: 'both-modified',
                    group: '数据录入',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'date-picker',
                    enName: 'DatePicker',
                    zhName: '日期选择器',
                },
                redirect: 'el-date-picker/main',
                children: [
                    {
                        path: 'main',
                        name: 'ElDatePickerMain',
                        component: () => import('@mitra-design/element-ui/components/date-picker/__demos__/date-picker.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'DatePicker',
                            category: 'component',
                            elementUi: 'both-modified',
                            group: '数据录入',
                            docType: 'component',
                            pkgName: 'element-ui',
                            componentName: 'date-picker',
                            enName: 'DatePicker',
                            zhName: '日期选择器',
                            index: 1000,
                        },
                    },
                    {
                        path: 'datetime-picker',
                        name: 'ElDatePickerDatetimePicker',
                        component: () => import('@mitra-design/element-ui/components/date-picker/__demos__/datetime-picker.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'DateTimePicker 日期时间选择器',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'element-ui',
                            componentName: 'date-picker',
                            enName: 'DatePicker',
                            zhName: '日期选择器',
                            index: 1099,
                        },
                    },
                    {
                        path: 'time-picker',
                        name: 'ElDatePickerTimePicker',
                        component: () => import('@mitra-design/element-ui/components/date-picker/__demos__/time-picker.md'),
                        meta: {
                            pkg: 'element-ui',
                            menuName: 'TimePicker 时间选择器',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'element-ui',
                            componentName: 'date-picker',
                            enName: 'DatePicker',
                            zhName: '日期选择器',
                            index: 1099,
                        },
                    },
                ],
            },
            {
                path: 'el-dialog',
                name: 'ElDialog',
                component: () => import('@mitra-design/element-ui/components/dialog/__demos__/dialog.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Dialog 对话框',
                    componentDir: '@mitra-design/element-ui/components/dialog/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'both-modified',
                    group: '反馈',
                    deprecated: '已自研<mt-modal>组件（[文档](/web/modal)）, `<el-dialog>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'dialog',
                    enName: 'Dialog',
                    zhName: '对话框',
                },
            },
            {
                path: 'el-divider',
                name: 'ElDivider',
                component: () => import('@mitra-design/element-ui/components/divider/__demos__/divider.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Divider 分割线',
                    componentDir: '@mitra-design/element-ui/components/divider/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'style-modified',
                    group: '布局',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'divider',
                    enName: 'Divider',
                    zhName: '分割线',
                },
            },
            {
                path: 'el-drawer',
                name: 'ElDrawer',
                component: () => import('@mitra-design/element-ui/components/drawer/__demos__/drawer.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Drawer 抽屉',
                    componentDir: '@mitra-design/element-ui/components/drawer/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    deprecated: '已自研<mt-drawer>组件（[文档](/web/drawer)）, `<el-drawer>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'drawer',
                    enName: 'Drawer',
                    zhName: '抽屉',
                },
            },
            {
                path: 'el-dropdown',
                name: 'ElDropdown',
                component: () => import('@mitra-design/element-ui/components/dropdown/__demos__/dropdown.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Dropdown 下拉菜单',
                    componentDir: '@mitra-design/element-ui/components/dropdown/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '导航',
                    deprecated: '已自研<mt-dropdown>组件（[文档](/web/dropdown)）, `<el-dropdown>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'dropdown',
                    enName: 'Dropdown',
                    zhName: '下拉菜单',
                },
            },
            {
                path: 'el-form',
                name: 'ElForm',
                component: () => import('@mitra-design/element-ui/components/form/__demos__/form.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Form 表单',
                    componentDir: '@mitra-design/element-ui/components/form/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-form>组件（[文档](/web/form)）, `<el-form>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'form',
                    enName: 'Form',
                    zhName: '表单',
                },
            },
            {
                path: 'el-image',
                name: 'ElImage',
                component: () => import('@mitra-design/element-ui/components/image/__demos__/image.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Image 图片',
                    componentDir: '@mitra-design/element-ui/components/image/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'image',
                    enName: 'Image',
                    zhName: '图片',
                },
            },
            {
                path: 'el-input',
                name: 'ElInput',
                component: () => import('@mitra-design/element-ui/components/input/__demos__/input.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Input 输入框',
                    componentDir: '@mitra-design/element-ui/components/input/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-input>组件（[文档](/web/input)）, `<el-input>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'input',
                    enName: 'Input',
                    zhName: '输入框',
                },
            },
            {
                path: 'el-input-number',
                name: 'ElInputNumber',
                component: () => import('@mitra-design/element-ui/components/input-number/__demos__/input-number.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'InputNumber 计数器',
                    componentDir: '@mitra-design/element-ui/components/input-number/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-input-number>组件（[文档](/web/input-number)）, `<el-input-number>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'input-number',
                    enName: 'InputNumber',
                    zhName: '计数器',
                },
            },
            {
                path: 'el-layout',
                name: 'ElLayout',
                component: () => import('@mitra-design/element-ui/components/layout/__demos__/layout.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Layout 布局',
                    componentDir: '@mitra-design/element-ui/components/layout/',
                    category: 'component',
                    coverFile: 'layout.png',
                    elementUi: 'original',
                    group: '布局',
                    deprecated: '已自研<mt-grid>组件（[文档](/web/grid)）, `<el-row>` 和 `<el-col>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'layout',
                    enName: 'Layout',
                    zhName: '布局',
                },
            },
            {
                path: 'el-link',
                name: 'ElLink',
                component: () => import('@mitra-design/element-ui/components/link/__demos__/link.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Link 文字链接',
                    componentDir: '@mitra-design/element-ui/components/link/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '通用',
                    deprecated: '使用 `<mt-button>`、`<mt-text>`组件（[文档](/web/typography)）代替，`el-link` 已经不推荐使用',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'link',
                    enName: 'Link',
                    zhName: '文字链接',
                },
            },
            {
                path: 'el-loading',
                name: 'ElLoading',
                component: () => import('@mitra-design/element-ui/components/loading/__demos__/loading.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Loading 加载',
                    componentDir: '@mitra-design/element-ui/components/loading/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    deprecated: '已自研<mt-loading>组件（[文档](/web/loading)）, `<el-loading>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'loading',
                    enName: 'Loading',
                    zhName: '加载',
                },
            },
            {
                path: 'el-menu',
                name: 'ElMenu',
                component: () => import('@mitra-design/element-ui/components/menu/__demos__/menu.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Menu 导航菜单',
                    componentDir: '@mitra-design/element-ui/components/menu/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '导航',
                    deprecated: '已自研<mt-menu>组件（[文档](/web/menu)）, `<el-menu>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'menu',
                    enName: 'Menu',
                    zhName: '导航菜单',
                },
            },
            {
                path: 'el-message',
                name: 'ElMessage',
                component: () => import('@mitra-design/element-ui/components/message/__demos__/message.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Message 消息提示',
                    componentDir: '@mitra-design/element-ui/components/message/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'message',
                    enName: 'Message',
                    zhName: '消息提示',
                },
            },
            {
                path: 'el-message-box',
                name: 'ElMessageBox',
                component: () => import('@mitra-design/element-ui/components/message-box/__demos__/message-box.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'MessageBox 弹框',
                    componentDir: '@mitra-design/element-ui/components/message-box/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    deprecated: '已自研<mt-confirm-modal>组件（[文档](/web/modal/confirm-modal)）, `<el-message-box>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'message-box',
                    enName: 'MessageBox',
                    zhName: '弹框',
                },
            },
            {
                path: 'el-notification',
                name: 'ElNotification',
                component: () => import('@mitra-design/element-ui/components/notification/__demos__/notification.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Notification 通知',
                    componentDir: '@mitra-design/element-ui/components/notification/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'notification',
                    enName: 'Notification',
                    zhName: '通知',
                },
            },
            {
                path: 'el-pagination',
                name: 'ElPagination',
                component: () => import('@mitra-design/element-ui/components/pagination/__demos__/pagination.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Pagination 分页',
                    componentDir: '@mitra-design/element-ui/components/pagination/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'both-modified',
                    group: '导航',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'pagination',
                    enName: 'Pagination',
                    zhName: '分页',
                },
            },
            {
                path: 'el-popconfirm',
                name: 'ElPopconfirm',
                component: () => import('@mitra-design/element-ui/components/popconfirm/__demos__/popconfirm.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Popconfirm 气泡确认框',
                    componentDir: '@mitra-design/element-ui/components/popconfirm/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '反馈',
                    deprecated: '已自研<mt-popconfirm>组件（[文档](/web/popconfirm)）, `<el-popconfirm>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'popconfirm',
                    enName: 'Popconfirm',
                    zhName: '气泡确认框',
                },
            },
            {
                path: 'el-popover',
                name: 'ElPopover',
                component: () => import('@mitra-design/element-ui/components/popover/__demos__/popover.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Popover 弹出框',
                    componentDir: '@mitra-design/element-ui/components/popover/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'both-modified',
                    group: '反馈',
                    deprecated: '已自研<mt-popover>组件（[文档](/web/popover)）, `<el-popover>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'popover',
                    enName: 'Popover',
                    zhName: '弹出框',
                },
            },
            {
                path: 'el-progress',
                name: 'ElProgress',
                component: () => import('@mitra-design/element-ui/components/progress/__demos__/progress.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Progress 进度条',
                    componentDir: '@mitra-design/element-ui/components/progress/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'progress',
                    enName: 'Progress',
                    zhName: '进度条',
                },
            },
            {
                path: 'el-radio',
                name: 'ElRadio',
                component: () => import('@mitra-design/element-ui/components/radio/__demos__/radio.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Radio 单选框',
                    componentDir: '@mitra-design/element-ui/components/radio/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-radio>组件（[文档](/web/radio)）, `<el-radio>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'radio',
                    enName: 'Radio',
                    zhName: '单选框',
                },
            },
            {
                path: 'el-select',
                name: 'ElSelect',
                component: () => import('@mitra-design/element-ui/components/select/__demos__/select.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Select 选择器',
                    componentDir: '@mitra-design/element-ui/components/select/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-select>组件（[文档](/web/select)）, `<el-select>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'select',
                    enName: 'Select',
                    zhName: '选择器',
                },
            },
            {
                path: 'el-slider',
                name: 'ElSlider',
                component: () => import('@mitra-design/element-ui/components/slider/__demos__/slider.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Slider 滑块',
                    componentDir: '@mitra-design/element-ui/components/slider/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'slider',
                    enName: 'Slider',
                    zhName: '滑块',
                },
            },
            {
                path: 'el-steps',
                name: 'ElSteps',
                component: () => import('@mitra-design/element-ui/components/steps/__demos__/steps.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Steps 步骤条',
                    componentDir: '@mitra-design/element-ui/components/steps/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '导航',
                    deprecated: '已自研<mt-steps>组件（[文档](/web/steps)）, `<el-steps>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'steps',
                    enName: 'Steps',
                    zhName: '步骤条',
                },
            },
            {
                path: 'el-switch',
                name: 'ElSwitch',
                component: () => import('@mitra-design/element-ui/components/switch/__demos__/switch.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Switch 开关',
                    componentDir: '@mitra-design/element-ui/components/switch/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    deprecated: '已自研<mt-switch>组件（[文档](/web/switch)）, `<el-switch>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'switch',
                    enName: 'Switch',
                    zhName: '开关',
                },
            },
            {
                path: 'el-table',
                name: 'ElTable',
                component: () => import('@mitra-design/element-ui/components/table/__demos__/table.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Table 表格',
                    componentDir: '@mitra-design/element-ui/components/table/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'table',
                    enName: 'Table',
                    zhName: '表格',
                },
            },
            {
                path: 'el-tabs',
                name: 'ElTabs',
                component: () => import('@mitra-design/element-ui/components/tabs/__demos__/tabs.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Tabs 标签页',
                    componentDir: '@mitra-design/element-ui/components/tabs/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '导航',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'tabs',
                    enName: 'Tabs',
                    zhName: '标签页',
                },
            },
            {
                path: 'el-timeline',
                name: 'ElTimeline',
                component: () => import('@mitra-design/element-ui/components/timeline/__demos__/timeline.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Timeline 时间线',
                    componentDir: '@mitra-design/element-ui/components/timeline/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'timeline',
                    enName: 'Timeline',
                    zhName: '时间线',
                },
            },
            {
                path: 'el-tooltip',
                name: 'ElTooltip',
                component: () => import('@mitra-design/element-ui/components/tooltip/__demos__/tooltip.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Tooltip 文字提示',
                    componentDir: '@mitra-design/element-ui/components/tooltip/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'both-modified',
                    group: '数据展示',
                    deprecated: '已自研<mt-tooltip>组件（[文档](/web/tooltip)）, `<el-tooltip>`后续不再使用。',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'tooltip',
                    enName: 'Tooltip',
                    zhName: '文字提示',
                },
            },
            {
                path: 'el-tree',
                name: 'ElTree',
                component: () => import('@mitra-design/element-ui/components/tree/__demos__/tree.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Tree 树形控件',
                    componentDir: '@mitra-design/element-ui/components/tree/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据展示',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'tree',
                    enName: 'Tree',
                    zhName: '树形控件',
                },
            },
            {
                path: 'el-upload',
                name: 'ElUpload',
                component: () => import('@mitra-design/element-ui/components/upload/__demos__/upload.md'),
                meta: {
                    pkg: 'element-ui',
                    menuName: 'Upload 上传',
                    componentDir: '@mitra-design/element-ui/components/upload/',
                    category: 'component',
                    coverFile: '',
                    elementUi: 'original',
                    group: '数据录入',
                    docType: 'component',
                    pkgName: 'element-ui',
                    componentName: 'upload',
                    enName: 'Upload',
                    zhName: '上传',
                },
            },
        ],
    },
];
