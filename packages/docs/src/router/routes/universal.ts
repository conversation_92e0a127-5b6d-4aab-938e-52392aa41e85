// Do not modify this file!!!
// it was auto generated by libs/gen-routes

import type { RouteConfig } from 'vue-router';

export const universalRoutes: RouteConfig[] = [
    {
        path: '/universal',
        name: 'Universal',
        redirect: '/universal/overview',
        component: { render: (h) => h('router-view') },
        meta: {
            nav: true,
            name: 'Universal',
            sort: true,
            group: 'universal',
        },
        children: [
            {
                path: 'overview',
                name: 'UniversalOverview',
                component: { render: (h) => h('components-overview', { props: { subStation: 'universal' } }) },
                meta: {
                    group: 'x',
                    menuName: '组件总览',
                },
            },
            {
                path: 'empty',
                name: 'UniversalEmpty',
                component: () => import('@mitra-design/universal/components/empty/__demos__/empty.md'),
                meta: {
                    pkg: 'universal',
                    menuName: 'Empty 空状态',
                    componentDir: '@mitra-design/universal/components/empty/',
                    category: 'component',
                    coverFile: '',
                    docType: 'component',
                    pkgName: 'universal',
                    componentName: 'empty',
                    enName: 'Empty',
                    zhName: '空状态',
                },
            },
            {
                path: 'icon-loading',
                name: 'UniversalIconLoading',
                component: () => import('@mitra-design/universal/components/icon-loading/__demos__/loading.md'),
                meta: {
                    pkg: 'universal',
                    menuName: 'IconLoading ',
                    componentDir: '@mitra-design/universal/components/icon-loading/',
                    category: 'component',
                    coverFile: '',
                    docType: 'component-sub',
                    pkgName: 'universal',
                    componentName: 'icon-loading',
                    enName: 'IconLoading',
                },
            },
            {
                path: 'overlayscrollbars',
                name: 'UniversalOverlayscrollbars',
                component: () => import('@mitra-design/universal/components/overlayscrollbars/__demos__/overlayscrollbars.md'),
                meta: {
                    pkg: 'universal',
                    menuName: 'Overlayscrollbars 滚动条',
                    componentDir: '@mitra-design/universal/components/overlayscrollbars/',
                    category: 'component',
                    coverFile: '',
                    docType: 'component',
                    pkgName: 'universal',
                    componentName: 'overlayscrollbars',
                    enName: 'Overlayscrollbars',
                    zhName: '滚动条',
                },
            },
            {
                path: 'svg-icon',
                name: 'UniversalSvgIcon',
                component: () => import('@mitra-design/universal/components/svg-icon/__demos__/svg-icon.md'),
                meta: {
                    pkg: 'universal',
                    menuName: 'SvgIcon ',
                    componentDir: '@mitra-design/universal/components/svg-icon/',
                    category: 'component',
                    coverFile: '',
                    author: 'null',
                    group: 'null',
                    projectVersion: 'null',
                    docType: 'component',
                    pkgName: 'universal',
                    componentName: 'svg-icon',
                    enName: 'SvgIcon',
                    zhName: '',
                },
            },
            {
                path: 'virtual-scroller',
                name: 'UniversalVirtualScroller',
                component: { render: (h) => h('router-view') },
                meta: {
                    pkg: 'universal',
                    menuName: 'VirtualScroller 虚拟滚动',
                    componentDir: '@mitra-design/universal/components/virtual-scroller/',
                    category: 'component-group',
                    coverFile: '',
                    docType: 'component',
                    pkgName: 'universal',
                    componentName: 'virtual-scroller',
                    enName: 'VirtualScroller',
                    zhName: '虚拟滚动',
                },
                redirect: 'virtual-scroller/main',
                children: [
                    {
                        path: 'main',
                        name: 'UniversalVirtualScrollerMain',
                        component: () => import('@mitra-design/universal/components/virtual-scroller/__demos__/virtual-scroller.md'),
                        meta: {
                            pkg: 'universal',
                            menuName: 'VirtualScroller',
                            category: 'component',
                            docType: 'component',
                            pkgName: 'universal',
                            componentName: 'virtual-scroller',
                            enName: 'VirtualScroller',
                            zhName: '虚拟滚动',
                            index: 1000,
                        },
                    },
                    {
                        path: 'event',
                        name: 'UniversalVirtualScrollerEvent',
                        component: () => import('@mitra-design/universal/components/virtual-scroller/__demos__/event.md'),
                        meta: {
                            pkg: 'universal',
                            menuName: '事件',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'universal',
                            componentName: 'virtual-scroller',
                            enName: 'VirtualScroller',
                            zhName: '虚拟滚动',
                            index: 1099,
                        },
                    },
                    {
                        path: 'method',
                        name: 'UniversalVirtualScrollerMethod',
                        component: () => import('@mitra-design/universal/components/virtual-scroller/__demos__/method.md'),
                        meta: {
                            pkg: 'universal',
                            menuName: '方法',
                            category: 'component',
                            docType: 'component-sub',
                            pkgName: 'universal',
                            componentName: 'virtual-scroller',
                            enName: 'VirtualScroller',
                            zhName: '虚拟滚动',
                            index: 1099,
                        },
                    },
                ],
            },
        ],
    },
];
