import { onUnmounted } from 'vue';

const contentUpdatedCallbacks = new Array<() => any>();

/**
 * 注册回调函数，在页面发生更新时执行这些回调
 * @param fn callback
 */
export const onContentUpdated = (fn: () => any) => {
    contentUpdatedCallbacks.push(fn);

    onUnmounted(() => {
        contentUpdatedCallbacks.filter((f) => f !== fn);
    });
};

/** 执行所有回调 */
export const runCbs = () => contentUpdatedCallbacks.forEach((cb) => cb());
