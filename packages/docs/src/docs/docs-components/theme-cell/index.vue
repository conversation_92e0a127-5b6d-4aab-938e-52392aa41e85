<template>
    <div>
        <table>
            <tr>
                <th>Token / Less 变量</th>
                <th>含义</th>
                <th>描述</th>
                <th>默认值</th>
                <th>预览</th>
            </tr>
            <tr
                v-for="item of tokenList"
                :key="item.token"
                :class="{ active: currentHash === item.token }"
            >
                <td>
                    <template v-if="item.deprecated">
                        <div class="token-name">
                            <mt-text
                                type="error"
                                deleted
                            >
                                {{ item.token }}
                            </mt-text>
                            <br>
                            <mt-text type="info">
                                {{ item.deprecated }}
                            </mt-text>
                        </div>
                    </template>

                    <template v-else>
                        <div

                            class="token-name"
                        >
                            {{ item.token }}
                        </div>
                        <div class="text-nowrap">
                            <mt-text copyable>@{{ toKebabCase(item.token) }}</mt-text>
                        </div>
                    </template>
                </td>
                <td class="text-nowrap">{{ item.name }}</td>
                <td>{{ item.desc }}</td>
                <td>
                    <code>{{ item.default }}</code>
                </td>
                <td>
                    <div class="value">
                        <div
                            v-if="String(item.default).startsWith('#') || String(item.default).startsWith('rgba')"
                            class="value-block color-block"
                            :class="{
                                'has-border': isWhite(item.default),
                            }"
                            :style="`background-color: ${item.default};`"
                        />
                        <div
                            v-else-if="isRadius(item.token)"
                            class="value-block radius-block"
                            :style="`border-radius: ${item.default}px`"
                        />
                        <div
                            v-else-if="isSize(item.token)"
                            class="size-block"
                            :style="`width: ${item.default}px; height: ${item.default}px`"
                        />
                        <div
                            v-else-if="isShadow(item.token)"
                            class="shadow-block"
                            :style="`box-shadow: ${item.default}`"
                        />
                        <div
                            v-else-if="isFontSize(item.token)"
                            :style="`font-size: ${item.default}px`"
                        >
                            Mitra Design
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</template>

<script lang="ts" setup>
import { toKebabCase } from '@mitra-design/theme/utils/toKebabCase';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router/composables';


interface Props {
    tokens: any;
}

const FilterColorItems = [
    'blue',
    'green',
    'orange',
    'red',
    'neutral',
    'cyan',
    'purple',
    'colorNeutral',
    'colorWhite',
];

const props = defineProps<Props>();

const route = useRoute();

const currentHash = ref('');

const tokenList = computed(() => {
    const obj = {};

    for (const key in props.tokens) {
        if (Object.prototype.hasOwnProperty.call(props.tokens, key)) {
            const token = props.tokens[key];
            if (!FilterColorItems.some(item => key.startsWith(item))) {
                obj[key] = token;
            }
        }
    }

    return obj as any;
});

const isRadius = (value: string) => {
    return value.toLowerCase().includes('radius');
};

const isWhite = (value: string) => {
    return /#(fff){1,2}/i.test(value);
};

const isFontSize = (value: string) => {
    return value.includes('fontSize');
};

const isSize = (value: string) => {
    return value.startsWith('size');
};

const isShadow = (value: string) => {
    return value.startsWith('shadow');
};

onMounted(() => {
    currentHash.value = route?.hash?.slice(1) ?? '';
    setTimeout(() => {
        document.querySelector('tr.active')?.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
        });
    }, 500);
});

</script>

<style lang="less" scoped>
@keyframes breath-border {
    0% {
        box-shadow: 0 0 10px rgba(255, 152, 0, 0.6);
    }

    50% {
        box-shadow: 0 0 0 rgba(255, 152, 0, 0.6);
    }

    100% {
        box-shadow: 0 0 10px rgba(255, 152, 0, 0.6);
    }
}

table {
    padding: 8px;
}

td {
    font-size: 13px !important;
}

tr {
    position: relative;
    &.active::after {
        position: absolute;
        top: -1px;
        left: 0;
        display: table;
        content: '';
        width: 100%;
        height: calc(100% + 1px);
        border: 2px solid var(--mto-c-brand);
        box-shadow: 0 0 10px rgba(255, 152, 0, 0.6);
        animation: breath-border 2s ease-in-out infinite;
    }
}

.value {
    display: flex;
    align-items: center;
    white-space: nowrap;
}
.value-block {
    width: 20px;
    height: 20px;
    margin-right: 8px;
}
.color-block {
    border-radius: 2px;

    &.has-border {
        border: 1px solid var(--mto-c-divider);
    }
}

.radius-block {
    border: 1px solid var(--mto-c-divider);
}

.size-block {
    border: 1px solid var(--mto-c-divider);
    border-radius: 2px;
}

.shadow-block {
    width: 80px;
    height: 80px;
}

.token-name {
    font-weight: bolder;
    margin-bottom: 4px;
}

.text-nowrap {
    white-space: nowrap;
}
</style>
