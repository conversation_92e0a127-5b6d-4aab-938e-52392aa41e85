<template>
    <div>
        <div class="color-palette">
            <div
                v-for="(presetColor) of allColors"
                :key="presetColor.key"
                class="color-palette-item"
            >
                <div
                    class="color-palette-primary"
                    :style="{ backgroundColor: presetColor.color, color: isLight(color) ? '#3c3c43' : '#fff' }"
                >
                    {{ presetColor.key }} / {{ presetColor.color }}
                </div>

                <div>
                    <div
                        v-for="(color, index) of presetColor.colors"
                        :key="`${color}-${index}`"
                        class="color-palette-item-color"
                        :style="{ backgroundColor: color, color: isLight(color) ? '#3c3c43' : '#fff' }"
                        @click="onCopy(color)"
                    >
                        <div>{{ presetColor.key }}-{{ index + 1 }}</div>
                        <div class="item-color-text">{{ color }}</div>
                    </div>
                </div>
            </div>
            <color-picker
                :value="customColor.color"
                disable-alpha
                class="color-palette-item"
                @input="updateValue"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { generate, presetColors, presetNeutralColors, isLight } from '@mitra-design/color';
import { copyToClipboard } from '@mitra-design/shared/utils';
import { Message } from 'element-ui';
import _ from 'lodash';
import { computed, ref } from 'vue';
import { Chrome as ColorPicker } from 'vue-color';


const customColor = ref({
    key: '#722ED1',
    color: '#722ED1',
    colors: generate('#722ED1', { list: true }),
});

const updateValue = (value) => {
    customColor.value = {
        key: value.hex,
        color: value.hex,
        colors: generate(value.hex, { list: true }),
    };
};

const presets = _.map(presetColors, (presetColor, key) => {
    return {
        key: key.charAt(0).toUpperCase() + key.slice(1),
        color: presetColor,
        colors: generate(presetColor, { list: true }),
    };
});

const allColors = computed(() => {
    return [
        ...presets,
        {
            key: 'Neutral',
            color: presetNeutralColors.neutral9,
            colors: _.values(presetNeutralColors),
        },
        {
            key: 'Custom',
            color: customColor.value.color,
            colors: customColor.value.colors,
        },
    ];
});

const onCopy = (color) => {
    copyToClipboard(color);
    Message.success(`已复制 ${color}`);

};

</script>


<style lang="less" scoped>
.color-palette {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 32px;
}

.color-palette-primary {
    padding: 24px;
    height: 100px;
    font-size: 16px;
    font-weight: 600;
}

.color-palette-item {
    min-width: 200px;
    &:hover {
        .item-color-text {
            opacity: 1;
        }
    }
}
.color-palette-item-color {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    cursor: pointer;
    transition: .3s;

    &:hover {
        border-radius: 4px;
        transform: scale(1.05)
    }

    .item-color-text {
        transition: .3s;
        opacity: 0;
    }
}

.vc-chrome {
    width: 100%;
    height: 280px;
}
</style>
