# QuickStart


跟随以下的步骤，快速上手组件库的使用。


## 安装

我们推荐使用 npm 或 pnpm 方式进行安装

```bash
npm i @mitra-design/web
```

```bash
pnpm i @mitra-design/web
```


## 使用

支持按需引入和全量引入

### 全量引入

如果你只是需要某几个组件，**不推荐全量引入**。

```js
/* mitra-design/index.ts */
import { MitraDesign } from '@mitra-design/web';
import Vue from 'vue';

// 全量引入样式
import '@mitra-design/web/style.v2.css';

// 安装使用
Vue.use(MitraDesign);
```

### 按需引入

按需引入能最大化的减少包体积，以 `Button` 为例：

```js
/* mitra-design/index.ts */

// 导入组件
import { Button } from '@mitra-design/web';
import Vue from 'vue';

// 引入组件样式
import '@mitra-design/web/button/index.css';

// 全局安装，使用时不需要再导入组件
Vue.use(Button);
```

```html
<!-- xxx.vue -->
<template>
    <mt-button>按钮</mt-button>
</template>
```

:::tip 推荐
引入组件时推荐使用**按需引入**的方式引入，如果使用的组件较多，那组件样式文件可以**一次性全量引入**
:::


## ProComponents 使用


`pro-components` 包内的组件都可以单独引入使用，完整的引入示例：

```ts
import { ProFilters, ProTable } from '@mitra-design/pro-components';
import Vue from 'vue';

import '@mitra-design/pro-components/style.v2.css';

Vue.use(ProFilters);
Vue.use(ProTable);
```


## ESign 使用


`e-sign` 包内包含一些中大型的业务组件，也支持单独引入使用，完整的引入示例：

```ts
import { SelectorOrg } from '@mitra-design/e-sign';
import Vue from 'vue';

import '@mitra-design/e-sign/style.v2.css';

Vue.use(SelectorOrg);
```


## 注意事项

### 兼容性

组件库产物不兼容 IE, 如果项目需要兼容 IE, 请在项目的相应构建配置中添加对应的转换包列表, 参考配置如下:

**vue.config.js**

```js
module.exports = {
    // ...
    transpileDependencies: [
        '@mitra-design',
        'color', // @mitra-design/color 依赖
        'color-convert', // @mitra-design/color -> color 依赖
    ],
};
```

**qys.cli.config.ts**

```js
module.exports = {
    // ...
    babel: {
        needTranspileInNodeModules: [
            '@mitra-design',
            'color', // @mitra-design/color 依赖
            'color-convert', // @mitra-design/color -> color 依赖
        ],
    },
};
```

**构建引擎要求:**

1. Webpack 5 以上版本(Webpack 4不支持 [exports 子路径导出](https://github.com/webpack/webpack/issues/9509#issuecomment-1381896299))
2. Vite 4 以上版本

### 依赖项

`@mitra-design/web` 是我们的 Web 端组件库，强依赖于以下几个第三方包，请确保在使用组件库之前先安装对应版本的包

```json
// package.json
{
    "peerDependencies": {
        "dayjs": "1.10.4",
        "lodash": "4.17.21",
        "vue": "2.7.4",
        "vue-class-component": "7.2.6",
        "vue-property-decorator": "9.1.2"
    }
}
```


### 在私有云低版本使用

组件库是从私有云 `private-sign@4.3.3` 开始接入的，`private-sign@4.3.3` 及以上的版本可以使用组件库的最新版本，如果想要在低版本接入，可以参考上述的代码示例，也可以直接看 `private-sign@4.3.3` 的接入方式。不过目前不确定低版本接入会不会出现其他问题，如果有同学在低版本接入组件库是遇到问题，可以随时联系架构组同学。
