<template>
    <div class="app">
        <doc-iconfont />
        <template v-if="pure">
            <van-popup
                v-model="popupMenuShow"
                position="left"
                style="width: 70%; height: 100%;"
            >
                <mto-menu class="popup-menu" />
            </van-popup>
            <mt-float-button
                style="top: 100px; left: 2px; bottom: unset; right: unset;"
                shape="square"
                type="primary"
                @click="popupMenuShow = true"
            >
                <template #icon>
                    <icon-menu-unfold :size="24" />
                </template>
            </mt-float-button>
            <div class="mto-article demo-page-wrapper">
                <router-view />
            </div>
        </template>
        <template v-else>
            <div
                class="mitra-design-official"
                :style="subStationAdditionalStyles"
            >
                <mto-header :full-width="!substation" />

                <mto-menu v-if="substation" />

                <div :class="['mto-content', { 'is-full-width': !substation }]">
                    <mto-article v-if="substation" />
                    <router-view v-else />
                </div>
            </div>
            <mto-loading />
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, provide, reactive, watchEffect } from 'vue';
import type { CSSProperties } from 'vue/types/jsx';
import { useRoute } from 'vue-router/composables';


import { useDynamicRegisterComponent } from './composables/use-dynamic-register';
import { routes } from './router';
import { substationInjectionKey } from './utils/context';
import MtoArticle from './views/_layout/mto-article/mto-article.vue';
import MtoHeader from './views/_layout/mto-header/mto-header.vue';
import MtoLoading from './views/_layout/mto-loading/mto-loading.vue';
import MtoMenu from './views/_layout/mto-menu/mto-menu.vue';

import { OverlayScrollbars } from '@/packages/overlayscrollbars';


const substation = ref<string | undefined>(undefined);
const substationName = ref<string | undefined>(undefined);
const substationSuffix = ref<string | undefined>(undefined);
const subStationAdditionalStyles = ref<CSSProperties | undefined>(undefined);


provide(substationInjectionKey, reactive({
    substation,
    substationName,
    substationSuffix,
}));

const route = useRoute();
useDynamicRegisterComponent();

const pure = computed(() => {
    return route.query.pure;
});

const popupMenuShow = ref(false);

watchEffect(() => {
    substation.value = route?.fullPath.split('/')[1];
    const substationRoute = routes.find(item => item.path === `/${substation.value}`);

    const metaName = substationRoute?.meta?.name;
    substationName.value = (Array.isArray(metaName) ? metaName[0] : metaName) ?? substationRoute?.name ?? '';
    substationSuffix.value = Array.isArray(metaName) ? metaName[1] : '';

    subStationAdditionalStyles.value = substationRoute?.meta?.additionalStyles;
});


watch(() => route.fullPath, () => {
    popupMenuShow.value = false;
});

onMounted(() => {
    if (pure.value) {
        setTimeout(() => {
            // @ts-ignore
            (document.querySelector(`${decodeURIComponent(route.hash)}`)).style.display = 'block';
        }, 1000);
    }
});


// onMounted(() => {
//     OverlayScrollbars(
//         {
//             target: document.body,
//         }, {
//             overflow: {
//                 x: 'hidden',
//             },
//             scrollbars: {
//                 autoHide: 'leave',
//                 autoHideDelay: 280,
//             },
//         });
// });

</script>
<style lang="less">
html {
    scroll-padding-top: calc(var(--mto-nav-height) + 12px);
}
</style>
<style lang="less" scoped>
.mitra-design-official {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}


.mto-content {
    flex-grow: 1;
    flex-shrink: 0;
    margin: var(--mto-layout-top-height, 0px) auto 0;
    width: 100%;
}


@media (min-width: 960px) {
    .mto-content {
        padding-top: var(--mto-nav-height);
        margin: var(--mto-layout-top-height, 0px) 0 0;

        &:not(.is-full-width) {
            padding-left: var(--mto-sidebar-width);
        }

    }
}

@media (min-width: 1680px) {
    .mto-content {
        &:not(.is-full-width) {
            padding-right: calc((100vw - var(--mto-layout-max-width)) / 2);
            padding-left: calc((100vw - var(--mto-layout-max-width)) / 2 + var(--mto-sidebar-width));
        }
    }
}

.demo-page-wrapper {
    :deep(.demo-page) {
        padding: 32px;

        >* {
            display: none;
        }

        div.doc-demo,
        h1,
        h2 {
            display: block;
        }

        p {
            &:first-of-type {
                display: block;
            }
        }

        h2:last-of-type {
            display: none;
        }
    }
}

.popup-menu {
    transform: translate(0) !important;
    padding: 16px;
    width: 100%;
    max-width: unset;
    opacity: 1;
    visibility: visible;
    box-shadow: none;
}
</style>
