<template>
    <div
        :class="[
            'doc-demo',
            {
                'mobile-mode': isMobile,
                'no-padding': isNoPadding,
                'grey-background': isGreyBackground,
                'full-height': isFullHeight,
                'pad-mode': isPad,
            },
        ]"
    >
        <div
            ref="refDemoWrapper"
            class="demo-wrapper"
            :class="[pure ? 'is-pure' : '']"
        >
            <div ref="refControlsPlaceholder" />
            <div
                v-if="isMobile"
                class="fringe"
            />
            <img
                v-if="isMobile && !isPad"
                src="../assets/imgs/phone-banner.svg"
                alt="phone-banner"
                class="phone-banner"
            >
            <div class="demo light">
                <!-- <iframe
                    width="100%"
                    height="100%"
                    frameborder="0"
                    :src="`${route.fullPath}?pure=true#${componentId}`"
                /> -->
                <component
                    :is="isNoShadowDom ? 'div' : 'shadow-root'"
                    ref="shadowRef"
                    style="flex: 1"
                >
                    <doc-iconfont />
                    <slot
                        v-if="isInited"
                        name="demo"
                    />
                </component>
            </div>
        </div>
        <div
            v-if="!pure"
            class="code-wrapper"
        >
            <div class="operators">
                <mt-tooltip
                    v-if="!isMobile"
                    :content="collapsed ? '展开代码' : '收起代码'"
                >
                    <div
                        class="icon-item"
                        @click="onCollapse"
                    >
                        <iconify-icon :icon="collapsed ? 'mdi:code-json' : 'mdi:code-braces'" />
                    </div>
                </mt-tooltip>
                <mt-tooltip content="复制代码">
                    <div
                        class="icon-item"
                        @click="onCopyCode"
                    >
                        <iconify-icon icon="mdi:content-copy" />
                    </div>
                </mt-tooltip>
            </div>
            <div
                v-show="!collapsed || isMobile"
                ref="refCode"
                class="code"
            >
                <slot name="code" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
/* eslint-disable import/order */
import { ElMessage } from '@mitra-design/element-ui';
import { copyToClipboard } from '@mitra-design/shared/utils';

import { onMounted, ref, nextTick } from 'vue';

import type { AdditionStyleInfo } from '@/../libs/vite-plugin-markdown/additional-style';

// ===== stylesheets =====
import MtESignStyleText from '@mitra-design/e-sign/components.less?inline';
import MtElStyleText from '@mitra-design/element-ui/components.less?inline';
import MtProStyleText from '@mitra-design/pro-components/components.less?inline';
import MtWebStyleText from '@mitra-design/web/components.less?inline';
import MtMobileStyleText from '@mitra-design/mobile/components.less?inline';
import MtVantStyleText from '@mitra-design/vant/components.less?inline';
import MtUniversalStyleText from '@mitra-design/universal/components.less?inline';
import DocDemoStyleText from '@/styles/demo.less?inline';
import DocDemoMobileStyleText from '@/styles/demo-mobile.less?inline';
import MtESignMobileStyleText from '@mitra-design/e-sign-mobile/components.less?inline';
import { useRoute } from 'vue-router/composables';
// =======================


const props = defineProps<{
    demoStyle: string;
    additionStylePathInfo: string;
    isMobile?: boolean;
    isNoPadding?: boolean;
    isGreyBackground?: boolean;
    isFullHeight?: boolean;
    isPad?: boolean;
    componentId: string;
    isNoShadowDom?: boolean;
}>();

// 默认折叠代码
const collapsed = ref(true);
const refCode = ref<HTMLDivElement>();
const shadowRef = ref();
const refDemoWrapper = ref<HTMLDivElement>();
const refControlsPlaceholder = ref<HTMLDivElement>();

const hasControl = ref(false);

const isInited = ref(false);

const route = useRoute();
const pure = computed(() => {
    return route.query.pure;
});

const onCollapse = () => {
    collapsed.value = !collapsed.value;
};

const onCopyCode = () => {
    const text = refCode.value!.querySelector('.shiki')?.textContent!.trim();
    text && copyToClipboard(text);
    ElMessage({
        message: '复制成功！',
        type: 'success',
        offset: 40,
    });
};

onMounted(async () => {
    const shadow = shadowRef.value?.$el?.shadowRoot ?? shadowRef.value;
    if (!shadow) return;


    // 样式 =================================================
    let additionalCss = '';
    const additionalStyleInfo = JSON.parse(props.additionStylePathInfo) as (AdditionStyleInfo | null);
    if (additionalStyleInfo) {
        if (import.meta.env.DEV) {
            // 由于vite动态import的限制
            // 下述代码看起来有些地方比较冗余，
            // 对于部分代码解释如下：
            // - insideComponentsDir 的判定：
            //   动态import中每一个/只能代表一个文件夹层级，无法通过js判定（如三目运算符等）抹除一个层级
            // - ext的判定：
            //   对于文件的扩展名，若使用动态参数，则需要保证此文件中所有的文件均可以被vite识别处理，且需要较大的开销
            try {
                const { pkg, dir, filename, ext, insideComponentsDir } = additionalStyleInfo;
                if (!insideComponentsDir) {
                    if (ext === 'scss') {
                        additionalCss = (await import(`../../../${pkg}/src/${dir}/__demos__/${filename}.scss?inline`)).default;
                    }
                    else if (ext === 'less') {
                        additionalCss = (await import(`../../../${pkg}/src/${dir}/__demos__/${filename}.less?inline`)).default;
                    }

                }
                else {
                    if (ext === 'scss') {
                        additionalCss = (await import(`../../../${pkg}/src/components/${dir}/__demos__/${filename}.scss?inline`)).default;
                    }
                    else if (ext === 'less') {
                        additionalCss = (await import(`../../../${pkg}/src/components/${dir}/__demos__/${filename}.less?inline`)).default;
                    }

                }
            }
            catch (e) {
                console.error('doc-demo addition-styles-loading', e);
            }
        }
        else {
            additionalCss = additionalStyleInfo.content;
        }
    }

    /**
     *
     */
    function createStyleSheet(cssText: string) {
        const stylesheet = new CSSStyleSheet();
        stylesheet.replaceSync(cssText);
        return stylesheet;
    }

    /** 对于不同 demo 修饰符，如 mobile，限定的特定的 shadow 样式 */
    function getDemoStyleSheet() {
        return props.isMobile
            ? [createStyleSheet(DocDemoMobileStyleText)]
            : [];
    }

    shadow.adoptedStyleSheets = [
        createStyleSheet(DocDemoStyleText),
        createStyleSheet(MtElStyleText),
        createStyleSheet(MtWebStyleText),
        createStyleSheet(MtProStyleText),
        createStyleSheet(MtESignStyleText),
        createStyleSheet(MtVantStyleText),
        createStyleSheet(MtMobileStyleText),
        createStyleSheet(MtUniversalStyleText),
        createStyleSheet(MtESignMobileStyleText),
        ...(
            props.demoStyle
                ? [createStyleSheet(props.demoStyle)]
                : []
        ),
        ...(getDemoStyleSheet()),
    ];


    // =============================================================

    setTimeout(() => {
        shadow.adoptedStyleSheets = [...shadow.adoptedStyleSheets, createStyleSheet(additionalCss)];

        isInited.value = true;
    }, 100);

    // =============================================================

    await nextTick();
    const elControl = shadow.querySelector('.doc-demo-controls');
    if (elControl) {
        hasControl.value = true;

        await nextTick();
        refControlsPlaceholder.value
            && refDemoWrapper.value?.replaceChild(elControl, refControlsPlaceholder.value);
    }
});


</script>

<style lang="less">
.doc-demo {
    --doc-demo-border-width: 1px;

    display: flex;
    flex-direction: column;
    margin: 1em 0;
    color-scheme: light;

    .demo-wrapper {
        display: flex;
        border: var(--doc-demo-border-width) solid var(--mto-c-divider);
        border-radius: 8px;
        overflow: hidden;
        flex-wrap: wrap;

        .demo {
            width: 100%;
            flex: 1 0 0;
            position: relative;
            padding: 32px;
            color: var(--mto-c-text-1);
            order: 1;
            background: #fff;
        }

        :deep(.doc-demo-controls) {
            flex-shrink: 0;
            border-style: solid;
            border-color: var(--mto-c-divider);
            border-width: 0 0 0 var(--doc-demo-border-width);
            width: 324px;
            padding: 8px 16px;
            order: 2;

            &.position-bottom {
                border-width: var(--doc-demo-border-width) 0 0;
                width: 100%;
                padding: 12px 32px;

                &+.demo {
                    flex: 1 0 auto;
                }
            }
        }
    }

    .code-wrapper {
        .operators {
            display: flex;
            justify-content: flex-end;
            position: relative;
            padding: 4px 8px;

            .icon-item {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                margin-right: 8px;
                border-radius: 4px;
                transition: .3s;
                cursor: pointer;
                color: var(--mto-c-text-2);
                font-size: 16px;

                &:hover {
                    background: var(--mto-c-brand-dimm-2);
                    color: var(--mto-c-brand);
                }
            }

        }

        .code {
            position: relative;
            overflow-x: auto;

            div[class*='language-'] {
                margin: 0;
            }
        }

    }

    &.grey-background {
        .demo-wrapper {
            .demo {
                background: var(--mt-color-background) !important;
            }
        }

    }

    &.mobile-mode {
        flex-direction: row;
        width: 100%;


        .code-wrapper {
            order: 1;
            flex-grow: 1;
            border-radius: 8px;
            overflow: hidden;
            border: var(--doc-demo-border-width) solid var(--mto-c-divider);

            .operators {
                padding-right: 12px;
            }

            .code {
                padding-right: 12px;
                background: var(--mto-c-bg-soft-mute)
            }

            div[class*='language-'] {
                border-radius: 0;
                min-height: 260px;
                max-height: 508px;
            }
        }

        @phone-color: #231206;

        .demo-wrapper {
            position: relative;
            order: 2;
            width: 387px;
            border-radius: 24px 24px 0 0;
            border: 6px solid @phone-color;
            border-bottom: none;
            flex: 0 0 auto;
            margin-left: -12px;
            margin-top: 18px;
            filter: drop-shadow(0 0 12px var(--mto-c-mute-lighter));

            &.is-pure {
                width: calc(100vw - 16px);
                height: 600px;
                left: -12px;
                border-radius: 24px;
                border-bottom: 6px solid @phone-color;
            }

            .phone-banner {
                position: absolute;
                top: 0;
                left: 0;
                z-index: 10;
                width: 100%;
                height: fit-content;
            }

            .fringe {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                height: 24px;
                width: 120px;
                border-radius: 0 0 16px 16px;
                background: @phone-color;
                z-index: 20;

                &::before {
                    content: '';
                    position: absolute;
                    left: -2px;
                    top: 0;
                    width: 0;
                    height: 0;
                    border-width: 2px;
                    border-style: solid;
                    border-color: @phone-color transparent transparent transparent;
                }

                &::after {
                    content: '';
                    position: absolute;
                    right: -2px;
                    top: 0;
                    width: 0;
                    height: 0;
                    border-width: 2px;
                    border-style: solid;
                    border-color: @phone-color transparent transparent transparent;
                }
            }


            .demo {
                // 用于使得其内部的position: fixed限定在shadow-root区域
                transform: scale(1);
                padding: 8px;
                margin-top: 48px;
                max-height: 600px;
                overflow-y: auto;
                scrollbar-width: none;
                background: #fff;

                &::webkit-scrollbar {
                    display: none;
                    width: 0;
                }
            }
        }

        &.no-padding {
            .demo-wrapper {
                .demo {
                    padding: 0 0 8px;
                }
            }
        }

        &.full-height {
            .code-wrapper {

                div[class*='language-'] {
                    max-height: 580px;
                    height: 580px;
                }


            }

            .demo-wrapper {
                border-radius: 24px;
                border-bottom: 6px solid @phone-color;
            }
        }

        &.pad-mode {
            .demo-wrapper {
                width: 588px;

                .fringe {
                    display: none;
                }
            }
        }

    }
}
</style>
