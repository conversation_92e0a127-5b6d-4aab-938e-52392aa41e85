<!DOCTYPE html>
<html lang="zh-CN" translate="no" class="dark" style="font-size: 10px;">
  <head>
    <meta charset="UTF-8" />
    <title>Mitra Design</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <script type="module">
        import Vue from 'vue';
        Vue.config.warnHandler = (msg, _vm, trace) => {
            if (/type check failed for prop/.test(msg)) {
                return;
            }
            console.warn("[Vue warn]: ".concat(msg).concat(trace));
        }
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
