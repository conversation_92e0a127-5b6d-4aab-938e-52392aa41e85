{"name": "@mitra-design/docs", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "node -v && pnpm gen:routes && vite --host", "docs:build": "node -v && cross-env NODE_OPTIONS=--max-old-space-size=10240 vite build", "preview": "vite preview --host", "gen:routes": "tsx libs/gen-routes/index.ts"}, "dependencies": {"@iconify/vue2": "2.1.0", "@mitra-design/icons": "workspace:*", "@mitra-design/color": "workspace:*", "@mitra-design/e-sign": "workspace:*", "@mitra-design/e-sign-mobile": "workspace:*", "@mitra-design/element-ui": "workspace:*", "@mitra-design/helpers": "workspace:*", "@mitra-design/locale": "workspace:*", "@mitra-design/mobile": "workspace:*", "@mitra-design/pro-components": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/universal": "workspace:*", "@mitra-design/vant": "workspace:*", "@mitra-design/web": "workspace:*", "dayjs": "1.10.4", "element-ui": "2.15.8", "gsap": "3.12.2", "html-entities": "2.4.0", "lodash": "4.17.21", "pinia": "2.1.6", "qrcode": "1.5.3", "uslug": "1.0.4", "vue-class-component": "7.2.6", "vue-color": "^2.8.1", "vue-docgen-api": "4.54.2", "vue-property-decorator": "9.1.2", "vue-shadow-dom": "1.5.0"}, "devDependencies": {"@types/less": "3.0.3", "@types/markdown-it": "12.2.3", "@types/markdown-it-container": "2.0.6", "@types/uslug": "1.0.4", "@types/qrcode": "1.5.5", "@vitejs/plugin-vue2": "2.3.1", "@vitejs/plugin-vue2-jsx": "1.1.1", "cheerio": "1.0.0-rc.12", "less": "4.1.3", "less-plugin-module-resolver": "1.0.3", "markdown-it": "13.0.1", "markdown-it-anchor": "8.6.7", "markdown-it-container": "3.0.0", "sass": "1.64.2"}, "volta": {"node": "20.12.2"}}