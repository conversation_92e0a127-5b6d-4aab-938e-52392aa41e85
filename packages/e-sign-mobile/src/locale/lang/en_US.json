{"BatchOperation": {"batch": "Batch Operation", "view": "view", "batchFinish": "Batch completed", "batchInProgress": "Batch in progress", "finish": "complete", "fail": "fail", "residue": "surplus", "noBatchTask": "There are currently no batch tasks available", "common": "common", "copiesNumber": "share", "inProgress": "In progress", "batchTask": "Batch tasks", "already": "already existing", "recordUnit": "strip", "clearRecords": "Clear records", "iKnow": "Ok", "all": "whole", "batchRecords": "Batch Records", "abnormal": "There is an anomaly present"}, "BatchOperationDetail": {"confirm": "confirm", "common": "common", "copiesNumber": "share", "fail": "fail", "cancel": "cancel", "finish": "complete", "residue": "surplus", "viewAll": "View", "more": "more", "resultDisplay": "Result display", "historyTask": "Historical Tasks", "recordUnit": "strip", "loadingText": "Loading, please wait", "emptyData": "There is currently no data available", "success": "success", "back": "back", "continue": "<PERSON><PERSON><PERSON>", "taskList": "task list", "noBatchTask": "There are currently no batch tasks available", "noData": "No data available at the moment", "iKnow": "Ok", "failFile": "Failed file", "operationTime": "Operation time"}, "OperationLog": {"operatorFallback": "Operator", "oneMinuteAgo": "One minute ago", "minutesAgo": "%{num} minutes ago", "hoursAgo": "%{num} hours ago", "daysAgo": "%{num} days ago"}}