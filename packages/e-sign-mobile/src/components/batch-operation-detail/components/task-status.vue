<template>
    <div :class="[bem(), { 'progressing': status === 'in-progress' }]">
        <template v-if="status === 'in-progress'">
            <van-circle
                v-model="currentRate"
                :rate="percentage"
                :stroke-width="80"
                :text="text"
                layer-color="#f0f2f7"
                color="#015BED"
                size="66px"
            />
            <div :class="bem('count-wrapper')">
                <ul :class="bem('status-nums')">
                    <li
                        v-for="item in statusNumList"
                        :key="item.field"
                        :class="bem('item')"
                    >
                        <span
                            :class="bem('status-tag')"
                            :style="{ backgroundColor: item.color }"
                        />
                        <span :class="bem('text')">{{ item.text }}</span>
                        <span
                            :class="bem('number')"
                        >{{ count[item.field] }}</span>
                    </li>
                </ul>
                <div :class="bem('status-tips')">
                    处理时间可能较长，您可退出继续其他操作，退出后后台仍会继续进行{{ dialogTitle.replace('批量', '') }}操作
                </div>
            </div>
        </template>
        <template v-else>
            <mt-result
                v-if="finishStatus === 'success'"
                :title="`${dialogTitle}${t('finish')}`"
                status="success"
            >
                <template #subtitle>
                    <div :class="bem('overview-text')">
                        <span :class="bem('text-warpper')">
                            {{ t('common') }} {{ count.total }}
                            <span>{{ unit ? unit : t('copiesNumber') }}</span>
                            <span v-if="count.failure">，{{ t('fail') }}
                                <span :class="bem('failure-count-text')">
                                    {{ count.failure }}
                                </span>
                                <span>{{ unit ? unit : t('copiesNumber') }}</span>
                            </span>
                        </span>
                    </div>
                </template>
            </mt-result>
            <mt-result
                v-else
                :title="`${dialogTitle}${t('fail')}`"
                status="error"
            >
                <template #subtitle>
                    <div :class="bem('overview-text')">
                        <span>
                            {{ t('fail') }}
                            <span :class="bem('failure-count-text')">
                                {{ count.failure }}
                            </span>
                            <span>{{ unit ? unit : t('copiesNumber') }}</span>
                        </span>
                    </div>
                </template>
            </mt-result>
        </template>
    </div>
</template>
<script setup lang="ts">
import { Result as MtResult } from '@mitra-design/mobile';
import type { TaskBaseCountMap } from '@mitra-design/internal';

import { useBem } from '@/composables';
import { useI18n } from '@/locale';

const props = defineProps<{
    count: TaskBaseCountMap;
    dialogTitle: string;
    status: string;
    unit?: string;
}>();


const componentName = 'task-status';

const { bem } = useBem(componentName);
const { t } = useI18n('batch-operation-detail');

const currentRate = ref(0);
const finishStatus = computed(() => {
    return props.count.failure === props.count.total ? 'fail' : 'success';
});

const statusNumList = computed(() => {
    return [
        {
            text: t('success'),
            color: '#2BB353',
            field: 'success',
            finalDivider: true,
        },
        {
            text: t('fail'),
            color: '#ED521F',
            field: 'failure',
            finalDivider: true,
        },
        {
            text: t('residue'),
            color: '#9BA3B0',
            field: 'rest',
            finalDivider: false,
        },
    ];
});

const percentage = computed(() => {
    return +(((props.count.success + props.count.failure) / props.count.total) * 100).toFixed();
});

const text = computed(() => `${currentRate.value.toFixed(0)}%`);
</script>
