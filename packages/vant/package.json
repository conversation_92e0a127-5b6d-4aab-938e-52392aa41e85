{"name": "@mitra-design/vant", "version": "2.9.5", "description": "自构建 vant", "scripts": {"build": "pnpm build:component && pnpm build:style && pnpm build:tsc", "build:style": "mitra-design-cli build:style", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json"}, "main": "es/index.js", "exports": {".": "./es/index.js", "./*": "./es/*", "./es/*": "./es/*", "./locale/lang": "./es/locale/lang/index.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"locale/lang": ["es/locale/lang/index.d.ts"]}}, "files": ["es", "types"], "sideEffects": ["*.css"], "homepage": "https://mitra-design.qiyuesuo.net", "dependencies": {"@mitra-design/locale": "workspace:*", "@mitra-design/icons": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/universal": "workspace:*", "@mitra-design/helpers": "workspace:*", "@babel/runtime": "7.x", "@vant/icons": "^3.0.2", "@vant/popperjs": "^1.1.0", "vue-lazyload": "1.2.3", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0"}, "peerDependencies": {"vue": "2.7.4", "vue-class-component": "7.2.6", "vue-property-decorator": "9.1.2"}, "devDependencies": {"@mitra-design/cli": "workspace:*", "@vue/test-utils": "1.0.0-beta.29"}}