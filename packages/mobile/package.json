{"name": "@mitra-design/mobile", "version": "2.9.5", "description": "移动端组件库", "scripts": {"build": "pnpm build:component && pnpm build:style && pnpm build:tsc", "build:style": "mitra-design-cli build:style", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json"}, "main": "es/index.js", "exports": {".": "./es/index.js", "./*": "./es/*", "./es/*": "./es/*", "./locale/lang": "./es/locale/lang/index.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"locale/lang": ["es/locale/lang/index.d.ts"]}}, "files": ["es", "types"], "sideEffects": ["*.css"], "homepage": "https://mitra-design.qiyuesuo.me/mobile/overview", "dependencies": {"@mitra-design/color": "workspace:*", "@mitra-design/icons": "workspace:*", "@mitra-design/helpers": "workspace:*", "@mitra-design/locale": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/universal": "workspace:*", "@mitra-design/vant": "workspace:*", "cropperjs": "^1.6.2", "dayjs": "1.11.13", "scroll-into-view-if-needed": "2.2.25"}, "devDependencies": {"@mitra-design/cli": "workspace:*"}}