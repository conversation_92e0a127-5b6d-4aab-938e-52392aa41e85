<template>
    <div
        ref="stepsRef"
        :class="cls"
    >
        <slot>
            <steps-item
                v-for="(option, index) in options"
                ref="itemRefs"
                :key="option.value ?? index"
                v-bind="option"
            />
        </slot>
    </div>
</template>

<script setup lang="ts">

import { computed, provide, reactive, ref, toRefs, onUpdated, watch, nextTick } from 'vue';


import StepsItem from './steps-item.vue';
import { type StepsProps, stepsInjectionKey } from './types';
import { scrollTo } from './utils/dom';

import { useBem } from '@/composables';

const componentName = 'steps';
const { bem } = useBem(componentName);

const props = withDefaults(
    defineProps<StepsProps>(),
    {
        type: 'default',
        direction: 'horizontal',
        labelPlacement: 'vertical',
        changeable: false,
        size: 'medium',
    }
);

const emit = defineEmits<{
    (e: 'input', step: number | string, event?: MouseEvent): void;
}>();

const labelPlacement = computed(() => {
    if (props.type === 'dot') {
        return props.direction === 'vertical' ? 'horizontal' : 'vertical';
    }
    if (props.direction === 'vertical') {
        return 'horizontal';
    }
    return props.labelPlacement;
});

const cls = computed(() => {
    return [
        bem(),
        bem('', props.type),
        bem('', props.direction),
        bem('', `label-${labelPlacement.value}`),
        bem('', props.size),
        {
            [bem('', 'changeble')]: props.changeable,
        },
    ];
});

const options = computed(() => props.options ?? []);

/** modelvalue */
const tempVal = ref<string | number>(1);

const handleChange = (step: number | string, event?: MouseEvent) => {
    if (props.changeable) {
        tempVal.value = Object.keys(indexMap.value)[step as number - 1] ?? step;
        emit('input', tempVal.value, event);
    }
};

const activeIndex = computed(() => {
    return indexMap.value[props.value ?? 0]
            ?? (
                typeof props.value === 'number'
                    ? (props.value - 1)
                    : -1
            );
})
provide(stepsInjectionKey, reactive({
    ...toRefs(props),
    labelPlacement: computed(() => labelPlacement.value),
    value: computed(() => props.value ?? tempVal.value),
    activeIndex: computed(() => activeIndex.value),
    setCurrent: (step: number | string, e?: MouseEvent) => handleChange(step, e),
}));

const itemRefs = ref<InstanceType<typeof StepsItem>[]>([]);
onUpdated(() => {
    itemRefs.value.forEach(itemRef => {
        itemRef.getIndex();
    });
});

const linear = (value: number): number => value;

const indexMap = computed<Record<number | string, number>>(() => {
    const entries = options.value.filter(i => i.value).map((i, index) => {
        return [i.value, index];
    });

    return Object.fromEntries(entries);
});

const stepsRef = ref<HTMLElement | undefined>();

watch(
    () => props.value,
    async () => {
        await nextTick();
        if (!(stepsRef.value instanceof HTMLElement))  {
            return;
        }

        if (!props.value) {
            return;
        }

        const activeItem = itemRefs.value[activeIndex.value];

        const activeItemOffsetLeft = (activeItem.$el as HTMLElement).offsetLeft;
        const left = activeItemOffsetLeft + activeItemOffsetLeft / 2 - stepsRef.value.offsetWidth / 2;

        scrollTo(stepsRef.value, {
            left,
            animation: linear,
        });
    },
    {
        immediate: true
    }
);
</script>
