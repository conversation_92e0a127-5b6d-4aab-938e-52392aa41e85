<template>
    <div
        ref="itemRef"
        :class="cls"
        @click="handleClick"
    >
        <div
            v-if="showTail"
            :class="bem('tail')"
        />
        <div :class="bem('inner')">
            <!-- 完全自定义 icon，可能需写样式 -->
            <slot
                name="icon"
                :step="stepNumber"
                :status="finalStatus"
            >
                <div :class="bem('icon')">
                    <!-- 当只需要替换 icon-font 图标时，不常用，推荐用 icon prop，暂不暴露，需暴露时改个名， -->
                    <slot
                        v-if="stepsContext?.type !== 'dot'"
                        name="icon"
                        :step="stepNumber"
                        :status="finalStatus"
                    >
                        <svg-icon
                            v-if="icon"
                            :icon="icon"
                            :size="svgSize"
                        />
                        <icon-check
                            v-else-if="finalStatus === 'finish'"
                            :size="svgSize"
                        />
                        <icon-close
                            v-else-if="finalStatus === 'error'"
                            :size="svgSize"
                        />
                        <template v-else>{{ stepNumber }}</template>
                    </slot>
                </div>
            </slot>

            <div :class="bem('content')">
                <div :class="bem('title')">
                    <slot name="title">
                        {{ title }}
                    </slot>
                </div>
                <div
                    v-if="description || $scopedSlots.description"
                    :class="bem('description')"
                >
                    <slot name="description">
                        {{ description }}
                    </slot>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup lang="ts">
import { computed, inject, ref, useSlots, onMounted } from 'vue';

import { stepsInjectionKey, type StepItemProps, type StepSize } from './types';

import { useBem } from '@/composables';

const componentName = 'steps-item';
const { bem } = useBem(componentName);

const props = defineProps<StepItemProps>();

const stepsContext = inject(stepsInjectionKey, {});

const itemRef = ref<HTMLElement>();

const svgSize = computed(() => {
    const sizeMap: Record<StepSize, number> = {
        'medium': 16,
        'small': 14,
    };
    return sizeMap[stepsContext.size ?? 'medium'];
});
const slots = useSlots();

const cls = computed(() => {
    return [
        bem(),
        bem('', finalStatus.value),
        {
            [bem('', 'active')]: stepsContext.value === stepNumber.value,
            [bem('', 'custom-icon')]: Boolean(slots.icon),
        },
    ];
});

const showTail = computed(() => stepsContext.labelPlacement === 'vertical' || stepsContext.direction === 'vertical');

const index = ref(0);
const getIndex = () => {
    const parent = itemRef.value?.parentElement ?? null;
    if (!parent || !itemRef.value) {
        return 0;
    }
    index.value = Array.from(parent.getElementsByClassName(bem())).indexOf(itemRef.value);
};

onMounted(() => {
    getIndex();
});

defineExpose({
    getIndex,
});

const stepNumber = computed(() => index.value + 1);

const finalStatus = computed(() => props.status ?? getStatus(stepNumber.value));

const handleClick = (e: MouseEvent) => {
    stepsContext.setCurrent?.(stepNumber.value, e);
};

const getStatus = (step: number) => {
    const current = ((stepsContext.activeIndex ?? 0) + 1);
    if (step < current) {
        return 'finish';
    }
    if (step > current) {
        return 'wait';
    }
    return stepsContext.status ?? 'process';
};
</script>
