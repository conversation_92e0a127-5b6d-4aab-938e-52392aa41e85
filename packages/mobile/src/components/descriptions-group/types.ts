import type { DescriptionsProps } from '@/components/descriptions/types';

export interface DescriptionsGroupProps {
    options: Array<
        DescriptionsProps
        & { key: string | number }
    >;

    /** 折叠高度 */
    collapseHeight?: number;

    /** 折叠数量 */
    collapseCount?: number;


    /** 是否折叠 */
    collapse?: boolean;

    /** 是否默认折叠，非受控属性 */
    defaultCollapse?: boolean;

    // ⬇⬇ pass through attrs ⬇⬇
    /** 尺寸 */
    size?: DescriptionsProps['size'];

    /** 标签宽度 */
    labelWidth?: DescriptionsProps['labelWidth'];

    /** 标签后缀 */
    labelSuffix?: DescriptionsProps['labelSuffix'];

    /** 占位文本 */
    placeholder?: DescriptionsProps['placeholder'];

    /** 布局 */
    layout?: DescriptionsProps['layout'];
}
