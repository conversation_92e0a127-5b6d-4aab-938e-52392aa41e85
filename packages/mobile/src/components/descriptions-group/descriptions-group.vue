<template>
    <div
        :class="cls"
    >
        <div
            :class="{
                [bem('flexible-container')]: true,
            }"
            :style="flexibleContainerStyle"
        >
            <div
                ref="wrapperRef"
                :class="bem('wrapper')"
            >
                <mt-descriptions
                    v-for="config of options"
                    ref="descriptionsRef"
                    :key="config.key"
                    :items="config.items"
                    :title="config.title"
                    :title-style="options.length > 1 ? 'colorful' : undefined"
                    v-bind="groupAttrs"
                >
                    <template
                        v-for="item in config.items"
                        #[item.key]
                    >
                        <slot
                            :name="`${config.key}-${item.key}`"
                            :item="item"
                        />
                    </template>
                </mt-descriptions>
            </div>
        </div>

        <div
            v-if="showCollapse"
            :class="bem('folder')"
            @click="toggleCollapse"
        >
            <icon-double-down
                v-if="computedCollapse"
                :class="bem('folder-icon')"
            />
            <icon-double-up
                v-else
                :class="bem('folder-icon')"
            />
        </div>
    </div>
</template>
<script setup lang="ts">
import Vue, { computed, ref, nextTick, watch } from 'vue';
import type { CSSProperties } from 'vue/types/jsx';

import type { DescriptionsGroupProps } from './types';

import MtDescriptions from '@/components/descriptions/descriptions.vue';
import { useBem } from '@/composables';

const props = withDefaults(
    defineProps<DescriptionsGroupProps>(),
    {
        collapseCount: 3,
        collapse: undefined,
        defaultCollapse: true,
    }
);
const emit = defineEmits<{
    /** 展开/收起时触发，返回布尔值 是否收起 */
    (e: 'collapse-change', value: boolean): void;
    (e: 'update:collapse', value: boolean): void;
}>();

const componentName = 'descriptions-group';

const { bem } = useBem(componentName);

const cls = computed(() => {
    return {
        [bem()]: true,
        [bem('', 'collapse')]: computedCollapse.value && fullHeight.value, // fullHeight 有值时，代表高度计算完毕，此时再进入 collapse 状态，隐藏 dom。否则会得到不足的高度
    };
});

const groupAttrs = computed(() => {
    return {
        size: props.size,
        layout: props.layout,
        labelSuffix: props.labelSuffix,
        labelWidth: props.labelWidth,
        placeholder: props.placeholder,
    };
});

/** 获取一个元素的高度 */
const getHeight = (element: HTMLElement) => {
    const style = window.getComputedStyle(element, null);
    const height = style.getPropertyValue('height');
    // 会有浏览器得到关键字 值吗。
    return height;
};

/** 获得前 n 项 row 的高度总和 */
const getAllHeight = (n: number): number => {
    const items = wrapperRef.value?.querySelectorAll('.mtm-descriptions--row');
    if (!items) return 0;
    let sum = 0;
    Array.from(items).slice(0, props.collapseCount).map(ele => {
        sum += parseFloat(getHeight(ele as HTMLElement));
    });
    return sum;
};

const containerHeight = ref<CSSProperties['height']>('auto');

const wrapperRef = ref<HTMLElement>();

const flexibleContainerStyle = computed<CSSProperties>(() => {
    return {
        height: containerHeight.value,
    };
});

const descriptionsRef = ref<Array<Vue>>();

const _collapse = ref(props.defaultCollapse);
const computedCollapse = computed(() => props.collapse ?? _collapse.value);

const fullHeight = ref<string>();

const collapsedHeight = ref<string>();

const toggleCollapse = async () => {
    containerHeight.value = fullHeight.value;
    await nextTick();

    const item = descriptionsRef.value?.[0].$el.querySelector('.mtm-descriptions--row');

    const collapseHeight =
        item instanceof HTMLElement
            ? `${props.collapseHeight ? props.collapseHeight : getAllHeight(props.collapseCount)}px`
            : 'auto';


    _collapse.value = !_collapse.value;

    containerHeight.value = _collapse.value ? collapseHeight : fullHeight.value;
    emit('collapse-change', _collapse.value);
    emit('update:collapse', _collapse.value);
};

/**
 * 计算 原始高度和最低高度
 * 注意，理想情况下，不期望得到 auto，auto 高度过渡动画兼容性不好。
 */
const setupHeight = async () => {
    fullHeight.value = undefined;
    await nextTick();
    fullHeight.value = wrapperRef.value instanceof HTMLElement ? `${wrapperRef.value.getBoundingClientRect().height}px` : 'auto';
    const item = descriptionsRef.value?.[0].$el.querySelector('.mtm-descriptions--row');
    collapsedHeight.value = item instanceof HTMLElement
        ? `${props.collapseHeight ? props.collapseHeight : getAllHeight(props.collapseCount)}px`
        : 'auto';
};

const showCollapse = computed(() => {
    const descriptionsItemCount: number = props.options
        .flatMap(i => i.items)
        .filter(i => i.show || (i.show === undefined))
        .length;

    // NaN 时 false
    return Number(props.collapseCount) < descriptionsItemCount;
});

const setHeight = () => {
    if (!showCollapse.value) {
        return;
    }
    containerHeight.value = computedCollapse.value ? collapsedHeight.value : fullHeight.value;
};

watch(
    [() => props.collapseCount, () => props.options],
    async () => {
        await setupHeight();
        setHeight();
    },
    { immediate: true, deep: true }
);
</script>
