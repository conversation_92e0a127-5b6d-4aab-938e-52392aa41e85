<template>
    <mt-descriptions-group
        :options="options"
        :placeholder="'null'"
        @collapse-change="handleChange"
    />
</template>

<script setup lang="ts">
import { ref } from 'vue';

const options = ref([
    {
        key: '1',
        title: 'group1',
        items: [
            { label: '公司', value: '某公司' },
            { label: '管理员', value: '某人' },
            { label: '地址', placeholder: '无' },
            { label: '电话' },
        ],
    },
    {
        key: '2',
        title: 'group2',
        items: [
            { label: '公司', value: '某公司' },
            { label: '管理员', value: '某人' },
            { label: '地址', value: '中国某地' },
            { label: '电话' },
        ],
    },
]);

const handleChange = (e: boolean) => {
    console.log(e);
};
</script>
