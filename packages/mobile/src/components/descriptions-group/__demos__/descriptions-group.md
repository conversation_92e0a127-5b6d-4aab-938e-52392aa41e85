---
author: 王炎
group: 展示组件
projectVersion: private-sign@5.2
figma: https://www.figma.com/design/XUn7Ef4DdsFvtjmqyFKUas/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91H5%2FAPP-%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83?node-id=15112-26570&node-type=rounded_rectangle&m=dev
---

# DescriptionsGroup
描述列表组合

## 何时使用
需要分组使用描述列表时，此组件默认有一个展开收起功能

## 基础示例
默认收起，收起时并仅展示 3 条描述，并隐藏标题
```demo mobile
descriptions-group.demo.vue
```

## 折叠
可通过 `collapse-count` 指定折叠几项，或 `collapse-height` 指定折叠至具体高度
默认呈折叠状态，如果不需要默认折叠，可声明 `default-collapse` 为 `false`
```demo mobile
descriptions-group.collapse.demo.vue
```

## 插槽分发
插槽优先级大于 prop，对应的 `item` 对象将作为插槽 prop
```demo mobile
descriptions-group.slot.demo.vue
```

## 透传 Attrs
部分属性可以透传至所有的描述列表，优先级：DescriptionsGroup.Attrs < Descriptions.Attrs
```demo mobile
descriptions-group.through.demo.vue
```

## API
