<template>
    <div :class="[bem(), bem('', { 'collapsed': collapsible && collapsed })]">
        <div :class="bem('container')">
            <!-- input-search -->
            <div :class="bem('input-search')">
                <input-search
                    v-model="keywordTempVal"
                    v-bind="finalInputSearchProps"
                    @search="onSearch"
                    @focus="onFocus"
                >
                    <template #prefix>
                        <slot name="input-prefix" />
                    </template>
                    <template #suffix>
                        <slot
                            v-if="(collapsible && !collapsed) || (!collapsible && cancelButton)"
                            name="input-suffix"
                        >
                            <mt-button
                                size="medium"
                                variant="text"
                                theme="info"
                                link
                                @click="onCancel"
                            >
                                取消
                            </mt-button>
                        </slot>
                    </template>
                </input-search>
            </div>

            <template v-if="!collapsible || (collapsible && !collapsed)">
                <!-- history -->
                <div
                    v-if="!keyword && historyList?.length"
                    :class="bem('history-wrapper')"
                >
                    <div :class="bem('history')">
                        <div :class="bem('history-header')">
                            <div :class="bem('history-header-title')">
                                {{ t('history') }}
                            </div>
                            <icon-trash
                                :class="bem('history-header-icon')"
                                :size="18"
                                @click="onClearHistory"
                            />
                        </div>
                        <div :class="bem('history-list')">
                            <mt-tag
                                v-for="item in historyList"
                                :key="item"
                                closable
                                max-width="calc(50% - 6px)"
                                @click="onSelectHistoryTag(item)"
                                @close="onRemoveHistoryTag(item)"
                            >
                                {{ item }}
                            </mt-tag>
                        </div>
                    </div>
                </div>

                <!-- content -->
                <div
                    v-else
                    :class="bem('content', { gray })"
                >
                    <div :class="bem('content-inner')">
                        <slot name="list">
                            <virtual-list
                                :key="listKey"
                                :get-data="virtualListGetData"
                                v-bind="{
                                    divider,
                                    itemSize,
                                    empty,
                                    keyField: idField,
                                    contentField,
                                    descriptionField,
                                    itemPadding,
                                }"
                                @items-changed="items => emit('items-changed', items)"
                            >
                                <template #default="{ item }">
                                    <search-list-item
                                        :key="item[idField]"
                                        :item="item"
                                        :config="props"
                                    >
                                        <template #item="{ item }">
                                            <slot
                                                name="item"
                                                :item="item"
                                                :keyword="keyword"
                                            />
                                        </template>
                                    </search-list-item>
                                </template>
                            </virtual-list>
                        </slot>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">
import { Dialog } from '@mitra-design/vant';
import { useVModel } from '@vueuse/core';
import type { SetRequired } from 'type-fest';

import SearchListItem from './search-list-item.vue';
import type { SearchListProps, SearchListEvents, SearchListHistory, FormattedSearchListHistory } from './types';

import InputSearch from '@/components/input-search/input-search.vue';
import type { SyncDataGetter } from '@/components/virtual-list/types';
import VirtualList from '@/components/virtual-list/virtual-list.vue';
import { useBem } from '@/composables';
import { useDbStore } from '@/composables/use-db-store';
import { useI18n } from '@/locale';

const componentName = 'search-list';

const props = withDefaults(
    defineProps<SearchListProps>(),
    {
        itemSize: 64,

        idField: 'id',
        contentField: 'content',
        descriptionField: 'description',

        max: 20,
        cancelButton: true,
        empty: () => ({
            scene: 'search-empty',
            centered: true,
        }),
        history: false,
        loadingText: '检索中',
    }
);

defineOptions({
    model: {
        prop: 'modelValue',
        event: 'update:modelValue',
    },
});

const emit = defineEmits<SearchListEvents>();

const { bem } = useBem(componentName);
const { t } = useI18n(componentName);
const { readDbStore, saveDbStore, deleteDbStore } = useDbStore(componentName);

const keywordTempVal = ref('');
const keyword = ref('');

// 用于强制刷新列表
const listKey = ref(0);

/**
 * 是否折叠
 *
 * - 当props.collapsible为true时，组件初始为折叠状态，输入框聚焦后展开，点击取消按钮后重新折叠
 * - 暂不考虑暴露折叠状态给外部
 */
const collapsed = ref(true);

const finalInputSearchProps = computed(() => {
    const defaultProps = {
        gray: true,
        debouncedTime: 300,
    };
    return {
        ...defaultProps,
        ...(props.inputSearchProps || {}),
    };
});

const virtualListGetData = computed(() => {
    if (!keyword.value) return (() => Promise.resolve([new Array(), 0])) as SyncDataGetter;

    if (typeof props.getData === 'function') {
        return props.getData(keyword.value);
    }

    if (props.getData?.mode === 'local') {
        const fn = props.getData.fn || ((keyword, data) => data.filter(item => item[props.contentField].includes(keyword)));

        const data = props.getData.localData || [];

        const filteredData = fn(keyword.value, data);
        return (() => Promise.resolve([filteredData, filteredData.length])) as SyncDataGetter;
    }

    return (() => Promise.resolve([new Array(), 0])) as SyncDataGetter;
});

const onFocus = () => {
    collapsed.value = false;
};

const onSearch = (result: string) => {
    keyword.value = result;

    if (props.history) { addHistory(); }
    emit('search', result);
};

const onCancel = () => {
    collapsed.value = true;
    keyword.value = '';

    emit('cancel');
};

watch(keyword,
    () => {
        listKey.value++;
        keywordTempVal.value = keyword.value || '';
    },
    {
        immediate: true,
    }
);


// =========== 历史记录 ============
const onSelectHistoryTag = (item: string) => {
    keyword.value = item;
};

const onRemoveHistoryTag = (item: string) => {
    removeHistory(item);
};

const onClearHistory = async () => {
    await Dialog.confirm({
        message: t('clearHistory'),
    });

    clearHistory();
};

const historyList = ref<string[]>([]);

const formattedHistoryConfig = computed<FormattedSearchListHistory>(() => {
    const defaultHistoryConfig: SetRequired<SearchListHistory, 'maxCount'> = {
        maxCount: 20,
        ttl: 0,
    };

    if (!props.history) { return { enabled: false, ...defaultHistoryConfig }; }
    if (props.history === true) { return { enabled: true, ...defaultHistoryConfig }; }
    return { enabled: true, ...defaultHistoryConfig, ...props.history };
});

/**
 *
 */
async function getHistory() {
    if (!formattedHistoryConfig.value.enabled) { return []; }

    const history = await readDbStore({
        primaryIdentifier: formattedHistoryConfig.value.primaryIdentifier,
        secondaryIdentifier: formattedHistoryConfig.value.secondaryIdentifier,
    });

    if (!history) { return []; }

    return history.content as string[];
}

onMounted(async () => {
    historyList.value = await getHistory();
});

/**
 *
 */
async function addHistory() {
    const currentKeyword = keyword.value;
    if (!currentKeyword) { return; }

    const history = await getHistory();

    // 若历史记录已存在，移动到最前面
    if (history.includes(currentKeyword)) {
        history.splice(history.indexOf(currentKeyword), 1);
        history.unshift(currentKeyword);
    } else {
        history.unshift(currentKeyword);
    }

    // 截断超过最大数量的所有历史记录
    if (history.length > formattedHistoryConfig.value.maxCount) {
        history.splice(formattedHistoryConfig.value.maxCount, history.length - formattedHistoryConfig.value.maxCount);
    }

    historyList.value = history;

    // 保存历史记录
    await saveDbStore({
        primaryIdentifier: formattedHistoryConfig.value.primaryIdentifier,
        secondaryIdentifier: formattedHistoryConfig.value.secondaryIdentifier,
        content: history,
        expiresAt: formattedHistoryConfig.value.ttl ? new Date(Date.now() + formattedHistoryConfig.value.ttl * 1000) : undefined,
    });
}

/**
 *
 */
async function removeHistory(item: string) {
    const history = await getHistory();
    if (!history.includes(item)) { return; }

    history.splice(history.indexOf(item), 1);

    historyList.value = history;

    await saveDbStore({
        primaryIdentifier: formattedHistoryConfig.value.primaryIdentifier,
        secondaryIdentifier: formattedHistoryConfig.value.secondaryIdentifier,
        content: history,
        expiresAt: formattedHistoryConfig.value.ttl ? new Date(Date.now() + formattedHistoryConfig.value.ttl * 1000) : undefined,
    });
}

/**
 *
 */
async function clearHistory() {
    historyList.value = [];

    await deleteDbStore({
        primaryIdentifier: formattedHistoryConfig.value.primaryIdentifier,
        secondaryIdentifier: formattedHistoryConfig.value.secondaryIdentifier,
    });
}
// ==========================================================

// ===== 与外界同步 ============================================

const syncedKeyword = useVModel(props, 'modelValue', emit, { eventName: 'update:modelValue' });

watch(
    keyword,
    () => {
        syncedKeyword.value = keyword.value;
    }
);

watch(
    syncedKeyword,
    () => {
        keyword.value = syncedKeyword.value || '';
    },
    {
        immediate: true,
    }
);

const reset = () => {
    keyword.value = '';
};

defineExpose({
    reset,
});
</script>
