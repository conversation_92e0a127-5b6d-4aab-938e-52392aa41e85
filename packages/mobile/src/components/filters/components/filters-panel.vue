<template>
    <div :class="bem()">
        <div :class="bem('fields')">
            <filters-field
                v-for="field in filterList"
                :key="getDataKey(field.dataKey)"
                v-model="model"
                :field="field"
                :cache="cache"
            />
        </div>

        <div
            v-if="showCacheControl"
            :class="bem('cache-control')"
        >
            <mt-checkbox
                :value="cacheControl"
                block
                @change="onCacheControlChange"
            >
                {{ t('cacheControl') }}
            </mt-checkbox>
        </div>

        <!-- Footer -->
        <mt-button-group
            :class="bem('footer')"
            :options="footerOptions"
        />
    </div>
</template>
<script setup lang="ts">
import { IconClear } from '@mitra-design/icons';
import { useVModel } from '@mitra-design/shared/composables';

import type { FilterList, FiltersCache } from '../types/common';
import { getDataKey } from '../utils';
import { filtersIsDefault } from '../utils/filters-is-default';

import FiltersField from './filters-field.vue';

import MtButtonGroup from '@/components/button-group/button-group.vue';
import type { ButtonGroupProps } from '@/components/button-group/types';
import { useBem } from '@/composables';
import { useI18n } from '@/locale';

const props = defineProps<{
    modelValue?: any;
    defaultValue?: Record<string, any>;
    filterList: FilterList;
    cacheControl?: boolean;
    cache?: FiltersCache;
}>();
const emit = defineEmits<{
    (event: 'update:modelValue', value: any): void;
    (event: 'update:cacheControl', value: boolean): void;
    (event: 'confirm'): void;
    (event: 'cancel'): void;
    (event: 'reset'): void;
}>();

defineOptions({
    inheritAttrs: false,
    model: {
        prop: 'modelValue',
        event: 'update:modelValue',
    },
});

const model = useVModel(props, 'modelValue', emit, 'update:modelValue');

const componentName = 'filters-panel';

const { bem } = useBem(componentName);

const { t } = useI18n('filters');

const showCacheControl = computed(() => {
    if (props.cache?.strategy !== 'user-control') {
        return false;
    }

    return !filtersIsDefault(model.value, props.filterList, props.defaultValue);
});

const footerOptions = computed<ButtonGroupProps['options']>(() => {
    return [
        {
            key: 'confirm',
            content: '确定',
            onClick: () => {
                emit('confirm');
            },
        },
        {
            key: 'cancel',
            variant: 'outline',
            content: '取消',
            onClick: () => {
                emit('cancel');
            },
        },
        {
            key: 'reset',
            content: '重置',
            icon: IconClear,
            onClick: () => {
                emit('reset');
            },
        },
    ];
});

const onCacheControlChange = (value: boolean) => {
    emit('update:cacheControl', value);
};

</script>
