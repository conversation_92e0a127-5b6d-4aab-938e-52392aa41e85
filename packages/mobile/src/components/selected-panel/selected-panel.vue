<template>
    <div :class="bem()">
        <van-popup
            v-model="isActive"
            position="bottom"
            :class="bem('popup')"
            :overlay-class="bem('popup-overlay')"
            round
            transition="fade"
        >
            <selected-panel-content :config="props" />
        </van-popup>
        <selected-panel-bar
            :config="props"
            :is-active="isActive"
            @count-click="handleCountClick"
        />
    </div>
</template>
<script setup lang="ts">
import SelectedPanelBar from './selected-panel-bar.vue';
import SelectedPanelContent from './selected-panel-content.vue';
import type { SelectedPanelProps } from './types';

import { useBem } from '@/composables';

const isActive = ref(false);

const props = withDefaults(
    defineProps<SelectedPanelProps>(),
    {
        title: '已选',
        idField: 'id',
        contentField: 'content',
        descriptionField: 'description',
        itemSize: 64,
        countText: (count: number) => `已选择 ${count} 项`,
        divider: false,

        onConfirm: () => { },
        onClear: () => { },
        onDelete: () => { },
    }
);

const { bem } = useBem('selected-panel');

onMounted(async () => {
    await nextTick();
});

const handleCountClick = () => {
    isActive.value = !isActive.value;
};
</script>
