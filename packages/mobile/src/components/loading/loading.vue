<!-- weixin 的 $_loading 在接入侧使用 mtm-loading 实现 -->

<template>
    <Loading />
</template>
<script setup lang="tsx">
import { IconLoading } from '@mitra-design/universal';
import { useDesignToken } from '@mitra-design/theme';
import { useBem } from '@/composables';
import { px2rem } from '@/utils';
import { TransferDom } from '@mitra-design/shared/directives';
import { computed, onMounted, ref } from 'vue';
import type { CSSProperties } from 'vue/types/jsx';
import type { LoadingProps } from './types';
import { PopupContext } from '@mitra-design/vant';

const props = withDefaults(
    defineProps<LoadingProps>(),
    {
        size: 'medium',
        textPlacement: 'bottom',
        background: 'white',
        active: true,
        delay: 0,
    }
);

/** @ts-ignore */
const vTransferDom = TransferDom;

const componentName = 'loading';

const { bem } = useBem(componentName);

const { designTokens } = useDesignToken();

const useComponentClassName = () => {
    return {
        /** 组件名 */
        name: bem(),
        /** 居中 */
        centerClass: bem('', 'center'),
        /** 全屏 */
        fullscreenClass: bem('', 'fullscreen'),
        /** 锁定滚动条 */
        lockClass: bem('', 'lock'),
        /** target */
        attachClass: bem('', 'attach'),
    };
};


const classes = useComponentClassName();

// 作为全屏遮罩元素
const fullscreenClasses = [
    classes.name,
    classes.fullscreenClass,
    classes.centerClass,
];

// 作为挂载元素的
const attachClasses = computed(() => {
    return [
        ...baseClass.value,
        classes.attachClass,
    ];
});

/** 作为图标和文字容器 */
const baseClass = computed(() => {
    return [
        classes.centerClass,
        classes.name,
        bem('', `direction-${props.textPlacement === 'right' ? 'horizontal' : 'vertical'}`),
        {
            [bem('', 'fit-content')]: props.fitContent,
        }
    ];
});


const SIZE_MAP: Record<string, { iconsize: string; fontsize: string }> = {
    'large': {
        iconsize: '40px',
        fontsize: '16px',
    },
    'medium': {
        iconsize: '32px',
        fontsize: '14px',
    },
    'small': {
        iconsize: '24px',
        fontsize: '13px',
    },
    'mini': {
        iconsize: '18px',
        fontsize: '13px',
    },
};

const fontsize = computed(() => {
    if(typeof props.size !== 'number' && SIZE_ENUM.includes(props.size)) {
        return SIZE_MAP[props.size].fontsize
    }

    return '13px';
});
const styles = computed(() => {
    const styles: CSSProperties = {};

    styles['font-size'] = fontsize.value;

    styles['background'] = props.background;

    return px2rem(styles);
});

const SIZE_ENUM = ['large', 'medium', 'small', 'mini'];
const isPresetSize = computed(() => SIZE_ENUM.includes(props.size as any))
const iconClasses = computed(() => {
    const iconBem = useBem(`${componentName}-icon`).bem;
    return [
        iconBem(),
        iconBem('', `text-${props.textPlacement}`),
        {
            [iconBem('', String(props.size))]: isPresetSize.value
        }
    ];
});

const renderIcon = () => {
    const iconsize =
        typeof props.size === 'number'
            ? `${props.size}px`
            :  SIZE_ENUM.includes(props.size)
                ? SIZE_MAP[props.size].iconsize
                : props.size;


    const  LoadingCompoent = IconLoading;

    return <LoadingCompoent
                class={iconClasses.value}
                size={iconsize}
                primary-color={designTokens.colorPrimary}
            />
}

const renderTitle = () => {
    if(!props.title) return;
    return <div class={bem('title')}>{props.title}</div>
}

const renderText = () => {
    if(!props.text) return;
    return <div class={bem('text')}>{props.text}</div>
}

const renderLoadingWithAttach = () => {
    return (<div
        class={props.fullscreen ? baseClass.value : attachClasses.value}
        style={props.fullscreen ? {} : styles.value}
        v-transfer-dom={props.fullscreen ? null : (props.target ?? 'body')}
    >
        {renderIcon()}
        {renderTitle()}
        {renderText()}
    </div>)
}

const renderLoading = () => {
    return (<div
        class={baseClass.value}
        style={styles.value}
    >
        {renderIcon()}
        {renderTitle()}
        {renderText()}
    </div>)
}

const Loading = () => {
    if (!props.active || !delayOff.value) {
            return null
    };
    // 全屏时额外插入一层 dom 作为遮罩层
    if(props.fullscreen) {
        return (
            // @ts-ignore
            <div class={fullscreenClasses} style={{...styles.value, zIndex: ++PopupContext.zIndex}} v-transfer-dom={props.target}>
                {renderLoading()}
            </div>
        )
    }

    // 非全屏时将 target 作为遮罩层，默认是指令的 binding element
    if(props.target) {
        return props.active ? renderLoadingWithAttach() : null;
    }
    return props.active ? renderLoading() : null;
};

/** 延迟是否结束 */
const delayOff = ref(true);
const startDelay = () => {
    delayOff.value = false;
    const timer = setTimeout(() => {
        delayOff.value = true;
        clearTimeout(timer);
    }, props.delay);
};
onMounted(() => {
    props.delay && startDelay();
});
</script>
