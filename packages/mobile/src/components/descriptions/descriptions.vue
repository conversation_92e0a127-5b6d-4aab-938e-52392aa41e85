<template>
    <div :class="cls">
        <div
            v-if="title"
            :class="[bem('title', `style-${titleStyle}`), bem('title')]"
            @click="handleClickTitle"
        >
            <div
                v-if="titleStyle === 'colorful'"
                :class="bem('title-refer')"
            />
            <div>
                <mt-ellipsis
                    :max-line="2"
                    :ellipsis="true"
                    :native="false"
                    @change="handleEllipsisChange"
                >
                    {{ title }}
                </mt-ellipsis>
            </div>
        </div>
        <div :class="bem('body')">
            <template v-if="layout === 'horizontal'">
                <div
                    v-for="(row, index) of groupItems"
                    :key="index"
                    :class="[bem('', 'row')]"
                >
                    <descriptions-item-label
                        v-bind="{ item: row, context: props }"
                    />

                    <descriptions-item-content
                        v-bind="{ item: row, context: props }"
                    >
                        <template
                            v-for="key of Object.keys($scopedSlots) "
                            #[key]="slotProps"
                        >
                            <slot
                                :name="key"
                                v-bind="{ ...slotProps }"
                            />
                        </template>
                    </descriptions-item-content>
                </div>
            </template>
            <template v-if="layout === 'vertical'">
                <template v-for="(row, index) of groupItems">
                    <div
                        :key="`label-${index}`"
                        :class="[bem('', 'row')]"
                    >
                        <descriptions-item-label
                            v-bind="{ item: row, context: props }"
                        />
                    </div>
                    <div
                        :key="`content-${index}`"
                        :class="[bem('', 'row')]"
                    >
                        <descriptions-item-content
                            v-bind="{ item: row, context: props }"
                        >
                            <template
                                v-for="key of Object.keys($scopedSlots) "
                                #[key]="slotProps"
                            >
                                <slot
                                    :name="key"
                                    v-bind="{ ...slotProps }"
                                />
                            </template>
                        </descriptions-item-content>
                    </div>
                </template>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Ellipsis as MtEllipsis } from '@mitra-design/internal';
import { Toast, Dialog } from '@mitra-design/vant';
import { computed, provide } from 'vue';

import DescriptionsItemContent from './descriptions-item-content.vue';
import DescriptionsItemLabel from './descriptions-item-label.vue';
import type { Item } from './types';

import { useBem } from '@/composables';

const componentName = 'descriptions';
const { bem } = useBem(componentName);

const isEllipsising = ref(false);
const handleEllipsisChange = (val: boolean) => {
    isEllipsising.value = val;
};

const handleClickTitle = () => {
    if (!isEllipsising.value || !props.title) return;
    if (props.title.length > 30) {
        return Dialog({
            message: props.title,
            confirmButtonText: '我知道了',
        });
    }
    Toast(props.title);
};

const props = withDefaults(
    defineProps<{
        items: Item[];
        /** 标题样式 */
        titleStyle?: 'colorful';
        /** 布局方式 */
        layout?: 'horizontal' | 'vertical';
        /** 标题 */
        title?: string;
        /** 尺寸 */
        size?: 'medium' | 'small';
        /** 标签后缀， */
        labelSuffix?: string;
        /** 标签长度 */
        labelWidth?: number;
        /** 是否强制标签宽度 */
        forceLabelWidth?: boolean;
        /** 占位提示 */
        placeholder?: string;
    }>(),
    {
        size: 'medium',
        placeholder: '--',
        col: 1,
        layout: 'horizontal',
        labelWidth: 80,
        forceLabelWidth: true,
    }
);

const cls = computed(() => {
    return {
        [bem()]: true,
        [bem('', props.size)]: true,
        [bem('', props.layout)]: true,

    };
});

const groupItems = computed((() => {
    return props.items.filter(i => i.show || (i.show === undefined));
}));
</script>
