.mtm-descriptions {
    * {
        box-sizing: border-box;
    }

    .mtm-descriptions__body {
        display: table;
        table-layout: auto;
        font-size: 14px;
    }
    .mtm-descriptions__title {
        display: flex;
        margin-bottom: 16px;
        color: @color-text-1;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;

        word-break: break-word;
    }

    .mtm-descriptions__title--style-colorful {
        font-size: 14px;
        line-height: 22px;

        .mtm-descriptions__title-refer {
            display: inline-flex;
            margin-right: 4px;

            width: 2px;
            height: 12px;
            margin-top: 5px;
            flex-shrink: 0;
            background-color: @color-primary;
        }
    }

    .mtm-descriptions--row {
        display: table-row;
        line-height: 22px;
    }

    .mtm-descriptions-item-label, .mtm-descriptions-item-content {
        padding-bottom: 12px;
    }
}

// size
.mtm-descriptions--small {
    font-size: 12px;

    .mtm-descriptions__body {
        font-size: 12px;
    }

    .mtm-descriptions__title {
        margin-bottom: 12px;
        font-size: 14px;
        line-height: 22px;
    }

    .mtm-descriptions--row {
        line-height: 18px;
    }

    .mtm-descriptions-item-label {
        padding-right: 12px;
    }

    .mtm-descriptions-item-label, .mtm-descriptions-item-content {
        padding-bottom: 6px;
    }
}

// layout
.mtm-descriptions--vertical {
    .mtm-descriptions-item-label {
        padding-bottom: 4px;
    }
}
