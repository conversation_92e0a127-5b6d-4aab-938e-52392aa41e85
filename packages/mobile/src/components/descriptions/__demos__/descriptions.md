---
author: 王炎
group: 展示组件
projectVersion: private-sign@5.1.0
figma: https://www.figma.com/design/XUn7Ef4DdsFvtjmqyFKUas/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91H5%2FAPP-%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83?node-id=15112-26570&node-type=rounded_rectangle&m=dev
---

# Descriptions - 描述列表

::: warning 注意事项
若需要实现展开收起功能，推荐使用[描述列表组合](/mobile/descriptions-group)
:::

## 何时使用

在需要以列表形式展示多个属性值时使用，如详情页中展示某条数据的详细信息。

## 基础示例

```demo mobile
descriptions.basic.demo.vue
```

## 尺寸

尺寸有 `medium`、`small`，详情页一般使用 `medium`

```demo mobile
descriptions.size.demo.vue
```


## 条目显隐

在 items 配置中，`show`字段可以控制条目的显隐。
当值为`false`时，条目隐藏。若未配置或值为`true`时，条目显示。

```demo mobile
descriptions.show.demo.vue
```

## 提示文字

在 items 配置中，`tips`字段可以控制在条目的 label 后显示一个在悬停时展示提示文字的问号图标。
- 传入字符串，点击则弹出 `toast` 展示内容。
- 传入对象，则会将对象透传给 [MtTips](/mobile/tips)。

```demo mobile
descriptions.tips.demo.vue
```
## 占位文字

在 Descriptions 的配置中，`placeholder`字段可以控制在条目的 value 为空时显示的占位文字。默认值为`--`。
而 items 配置中，对于每个条目也可以配置`placeholder`字段，此时会覆盖`mt-descriptions`的配置。

当条目对应的值为空字符串`''`、`null`、`undefined`时，会显示占位文字。
注意：当你使用插槽定义描述内容时，组件内无法判定插槽具体内容，因此请按条件传递插槽或在插槽内自行实现 `placeholder`

```demo mobile
descriptions.placeholder.demo.vue
```

## 布局

Descriptions 提供了标签水平、标签垂直两种布局方式
```demo mobile
descriptions.layout.demo.vue
```

### 标签宽度
标签固定宽度为 `80px`。
在 items 配置中，通过传入`label-width`配置项可以控制标签的**固定宽度**，超出此宽度时将会省略截断。
请注意，`label-width`仅包括 label 文字区域的占位宽度，不包括`label-suffix`及`tips`所需空间，亦不包括右侧的 padding 间隔。

<!-- 在 items 配置中，通过开启`force-label-width`配置项则可以将`label-width`指定的宽度由固定宽度改为最大宽度。 -->


```demo mobile
descriptions.width.demo.vue
```

## 文本省略

当标题或描述内容超过两行时，文本将自动省略，点击会以 `Toast` 方式提示完整内容，大于 30 字时则以 `Dialog` 形式进行提示。
> 注意：使用插槽时，此特性无效

```demo mobile
descriptions.ellipsis.demo.vue
```
<!-- ## 多列展示

通过配置`col`属性可使条目在多列中展示，`col`属性接收一个数字，表示需要展示的列数。

可以对每个 item 配置`colspan`属性，控制该条目占据的列数，不配置时默认为 1。

```demo
descriptions.col.demo.vue
``` -->

## API
