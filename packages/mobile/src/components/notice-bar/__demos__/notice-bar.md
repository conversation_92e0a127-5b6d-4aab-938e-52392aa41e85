---
author: 李镇宇
group: 展示组件
projectVersion: private-weixin@5.0
figma: https://www.figma.com/file/XUn7Ef4DdsFvtjmqyFKUas/%E3%80%90%E6%96%B0%E7%89%88%E3%80%91H5%2FAPP-%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83?node-id=281%3A1609&mode=dev
---

# notice-bar 通知栏

## 基础示例

```demo mobile no-padding
notice-bar.demo.vue
```

## 不同状态的通知

公告栏类型有信息（info）、进行中（processing）、警示（warning）、成功（success）、错误（error）

```vue mobile no-padding
<template>
    <div class="demo-flex-col">
        <mt-notice-bar type="info" content="信息状态公告栏" />
        <mt-notice-bar type="processing" content="进行中状态公告栏" />
        <mt-notice-bar type="success" content="成功状态公告栏" />
        <mt-notice-bar type="warning" content="警告状态公告栏" />
        <mt-notice-bar type="error" content="错误状态公告栏" />
    </div>
</template>
```

## 二级提示

设置`secondary`为`true`，会添加圆角，字号为 12px，左右间距缩小，一般用于当前页面中次级提示，常用于板块中间的间隙或者板块内部，例如弹框、弹出层等

```vue mobile full-height no-padding
<template>
    <van-popup
        :value="true"
        round
        position="bottom"
        :style="{ height: '30%' }"
        :close-on-click-overlay="false"
    >
        <div :style="{ padding: '10px' }">
            <div :style="{ padding: '10px 0' }">标题</div>
            <mt-notice-bar
                type="info"
                secondary
                content="信息状态二级通知栏"
            />
        </div>
    </van-popup>
</template>
```

## 内容颜色跟随类别显示

配置`colorful-text`为`true`，内容的文字颜色会跟随类型，一般用于在板块中做状态提醒

```vue mobile no-padding
<template>
    <div class="demo-flex-col">
        <mt-notice-bar type="info" content="信息状态公告栏" colorful-text />
        <mt-notice-bar type="processing" content="进行中状态公告栏" colorful-text />
        <mt-notice-bar type="success" content="成功状态公告栏" colorful-text />
        <mt-notice-bar type="warning" content="警告状态公告栏" colorful-text />
        <mt-notice-bar type="error" content="错误状态公告栏" colorful-text />
    </div>
</template>
```


## 显示边框

配置 `bordered` 为 `true` 会显示边框
```vue mobile no-padding
<template>
    <div class="demo-flex-col">
        <mt-notice-bar type="info" content="信息状态公告栏" bordered />
        <mt-notice-bar type="processing" content="进行中状态公告栏" bordered />
        <mt-notice-bar type="success" content="成功状态公告栏" bordered />
        <mt-notice-bar type="warning" content="警告状态公告栏" bordered />
        <mt-notice-bar type="error" content="错误状态公告栏" bordered />
    </div>
</template>
```


## 带操作的通知

可传入`operation`属性，也可以使用插槽

```demo mobile no-padding
notice-bar.operation.demo.vue
```

## 整行点击

使用`@row-click`事件，可点击整行触发

:::warning
组件内部没有阻止冒泡，**如果同时使用`@operation`事件与`@row-click`事件，则需手动阻止`@operation`事件冒泡，简写为`@operation.stop`**
:::

```demo mobile no-padding
notice-bar.rowclick.demo.vue
```

## 可滚动的公告栏

传入`marquee`属性为`true`，可实现公告栏的走马灯效果，也可以传入`speed`、`loop`、`delay`

```vue mobile no-padding
<template>
    <div class="demo-flex-col">
        <mt-notice-bar visible marquee :content="content" />
        <mt-notice-bar
            :prefix-icon="false"
            :marquee="marquee"
            :content="content"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const content = ref('走马灯效果的滚动公告栏');
const marquee = {
    loop: 'infinity',
    speed: 100,
    delay: 1000,
};
</script>
```

## API
