<template>
    <div class="icons-container">
        <el-tabs v-model="currentTab">
            <el-tab-pane v-for="pane of paneList" :key="pane.name" :label="pane.name" class="pane-wrap">
                <div
                    v-for="[icon, iconComponent] of pane.icons"
                    :key="icon"
                    class="icon-wrap"
                    @click="onClickIcon(icon)"
                >
                    <component :is="iconComponent" :size="40" />
                    <div class="icon-name">{{ icon }}</div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup lang="ts">
import {
    // GENERATED BY `mitra-design-cli gen:icon`
    // WARN: DO NOT MODIFIED MANUALLY
    IconAuthenticatedFill,
    IconBatchSuccess,
    IconCaretDownFill,
    IconCheckBold,
    IconCheckCircle,
    IconCheckFill,
    IconCheck,
    IconChevronDown,
    IconChevronUp,
    IconClear,
    IconClockwise90,
    IconCloseCircleFill,
    IconCloseCircle,
    IconClose,
    IconConfiguration,
    IconCopy,
    IconCounterClockwise90,
    IconDoubleDown,
    IconDoubleUp,
    IconEdit,
    IconEllipsisCircle,
    IconEllipsis,
    IconEyeClose,
    IconEye,
    IconFileOpenTwohue,
    IconFileText,
    IconFileTwohue,
    IconFilter,
    IconFlipLeft,
    IconFlipRight,
    IconFullscreenExit,
    IconFullscreen,
    IconHolder,
    IconInfoCircleFill,
    IconLeft,
    IconLoadingCircleTwohue,
    IconLoadingFull,
    IconLoading,
    IconMailChange,
    IconMenuFold,
    IconMenuUnfold,
    IconMinusCircle,
    IconMinus,
    IconPlusCircleAlt,
    IconPlusCircle,
    IconPlus,
    IconPrevious,
    IconPushpin,
    IconQuestionCircle,
    IconQuestionFill,
    IconRank,
    IconReduceCircleFill,
    IconRefresh,
    IconRight,
    IconSearch,
    IconSet,
    IconShortcut,
    IconStar,
    IconSwap,
    IconTailor,
    IconTelephoneChange,
    IconTrash,
    IconUser,
    IconWarningCircle,
    IconWarningFill,
} from '@mitra-design/icons';
import { copyToClipboard } from '@mitra-design/shared/utils';
import { Message } from 'element-ui';
import { ref } from 'vue';

const currentTab = ref('0');

const paneList = ref([
    // GENERATED BY `mitra-design-cli gen:icon`
    // WARN: DO NOT MODIFIED MANUALLY
    {
        name: '线性图标',
        icons: [
            ['icon-batch-success', IconBatchSuccess],
            ['icon-check-bold', IconCheckBold],
            ['icon-check-circle', IconCheckCircle],
            ['icon-check', IconCheck],
            ['icon-chevron-down', IconChevronDown],
            ['icon-chevron-up', IconChevronUp],
            ['icon-clear', IconClear],
            ['icon-clockwise-90', IconClockwise90],
            ['icon-close-circle', IconCloseCircle],
            ['icon-close', IconClose],
            ['icon-configuration', IconConfiguration],
            ['icon-copy', IconCopy],
            ['icon-counter-clockwise-90', IconCounterClockwise90],
            ['icon-double-down', IconDoubleDown],
            ['icon-double-up', IconDoubleUp],
            ['icon-edit', IconEdit],
            ['icon-ellipsis-circle', IconEllipsisCircle],
            ['icon-ellipsis', IconEllipsis],
            ['icon-eye-close', IconEyeClose],
            ['icon-eye', IconEye],
            ['icon-file-text', IconFileText],
            ['icon-filter', IconFilter],
            ['icon-flip-left', IconFlipLeft],
            ['icon-flip-right', IconFlipRight],
            ['icon-fullscreen-exit', IconFullscreenExit],
            ['icon-fullscreen', IconFullscreen],
            ['icon-holder', IconHolder],
            ['icon-left', IconLeft],
            ['icon-loading-full', IconLoadingFull],
            ['icon-loading', IconLoading],
            ['icon-mail-change', IconMailChange],
            ['icon-menu-fold', IconMenuFold],
            ['icon-menu-unfold', IconMenuUnfold],
            ['icon-minus-circle', IconMinusCircle],
            ['icon-minus', IconMinus],
            ['icon-plus-circle-alt', IconPlusCircleAlt],
            ['icon-plus-circle', IconPlusCircle],
            ['icon-plus', IconPlus],
            ['icon-previous', IconPrevious],
            ['icon-pushpin', IconPushpin],
            ['icon-question-circle', IconQuestionCircle],
            ['icon-rank', IconRank],
            ['icon-refresh', IconRefresh],
            ['icon-right', IconRight],
            ['icon-search', IconSearch],
            ['icon-set', IconSet],
            ['icon-shortcut', IconShortcut],
            ['icon-star', IconStar],
            ['icon-swap', IconSwap],
            ['icon-tailor', IconTailor],
            ['icon-telephone-change', IconTelephoneChange],
            ['icon-trash', IconTrash],
            ['icon-user', IconUser],
            ['icon-warning-circle', IconWarningCircle],
        ],
    },
    {
        name: '填充图标',
        icons: [
            ['icon-authenticated-fill', IconAuthenticatedFill],
            ['icon-caret-down-fill', IconCaretDownFill],
            ['icon-check-fill', IconCheckFill],
            ['icon-close-circle-fill', IconCloseCircleFill],
            ['icon-info-circle-fill', IconInfoCircleFill],
            ['icon-question-fill', IconQuestionFill],
            ['icon-reduce-circle-fill', IconReduceCircleFill],
            ['icon-warning-fill', IconWarningFill],
        ],
    },
    {
        name: '双色图标',
        icons: [
            ['icon-file-open-twohue', IconFileOpenTwohue],
            ['icon-file-twohue', IconFileTwohue],
            ['icon-loading-circle-twohue', IconLoadingCircleTwohue],
        ],
    },
    {
        name: '多色图标',
        icons: [],
    },
]);

const onClickIcon = async (name: string) => {
    const code = `<${name} />`;

    await copyToClipboard(code);

    Message.success(`复制成功：${code}`);
};
</script>

<style lang="less" scoped>
.icons-container {
    margin-top: 40px;
}
.pane-wrap {
    display: grid;
    grid-template-columns: repeat(6, 1fr);

    .icon-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        height: 160px;

        svg {
            transition: transform 0.3s;
            will-change: transform;
        }

        &:hover {
            cursor: pointer;
            color: #ff7d00;
            svg {
                transform: scale(1.2);
            }
        }

        .icon-name {
            margin-top: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
</style>
