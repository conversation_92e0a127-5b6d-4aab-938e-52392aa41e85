import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach } from 'vitest';
import Vue, { ref, nextTick, set, del } from 'vue';
import VueRouter from 'vue-router';

import { useSyncWithQuery } from '../use-sync-with-query';

// 安装 VueRouter
Vue.use(VueRouter);

/**
 * 通用测试工具函数
 */
function createWrapper(router: VueRouter, setup: () => any) {
    let setupContext: any;
    const TestComponent = {
        template: '<div/>',
        setup() {
            setupContext = setup();
            return setupContext;
        },
    };

    mount(TestComponent, { router });
    return { setupContext };
}

/**
 * 辅助函数：同时验证状态和 URL
 */
function expectStateAndUrl(
    state: any,
    expectedState: any,
    router: VueRouter,
    expectedQuery: any,
    expectedPath: string
) {
    expect(state.value).toEqual(expectedState);
    expect(router.currentRoute.query).toEqual(expectedQuery);
    expect(router.currentRoute.fullPath).toBe(expectedPath);
}

/**
 * 辅助函数：验证双向同步（状态 → URL → 状态）
 */
async function expectBidirectionalSync(
    state: any,
    newStateValue: any,
    router: VueRouter,
    expectedQuery: any,
    expectedPath: string,
    urlQuery: any,
    expectedStateFromUrl: any
) {
    // 1. 修改状态，验证同步到 URL
    state.value = newStateValue;
    await nextTick();
    expectStateAndUrl(state, newStateValue, router, expectedQuery, expectedPath);

    // 2. 修改 URL，验证同步到状态
    await router.replace({ query: urlQuery });
    await nextTick();
    expect(state.value).toEqual(expectedStateFromUrl);
    expect(router.currentRoute.query).toEqual(urlQuery);
}


// ============================================================================
// Vue Router 行为测试
// ============================================================================
describe('Vue Router 行为', () => {
    let router: VueRouter;

    beforeEach(() => {
        router = new VueRouter({
            mode: 'abstract',
            routes: [{ path: '/', component: { template: '<div/>' } }],
        });
        router.push('/');
    });

    it('无 params 时应该将 query 参数处理为字符串', async () => {
        await router.replace({
            query: { count: 1, active: true, never: undefined, nil: null } as any,
        });
        expect(router.currentRoute.query.count).toBe('1');
        expect(router.currentRoute.query.active).toBe('true');
        expect(router.currentRoute.query).toEqual({
            count: '1',
            active: 'true',
            never: undefined,
            nil: null,
        });
    });
});

// ============================================================================
// 基础功能测试
// ============================================================================
describe('基础功能', () => {
    let router: VueRouter;

    beforeEach(() => {
        router = new VueRouter({
            mode: 'abstract',
            routes: [{ path: '/', component: { template: '<div/>' } }],
        });
        router.push('/');
    });

    // ------------------------------------------------------------------------
    // 简单类型同步
    // ------------------------------------------------------------------------
    describe('简单类型同步', () => {
        it('应该正确同步字符串类型', async () => {
            const { setupContext: { syncedValue } } = createWrapper(router, () => {
                const value = ref<string | undefined>('initial');
                const syncedValue = useSyncWithQuery(value, 'text');
                return { syncedValue };
            });

            // 1. 验证初始状态
            await nextTick();
            expectStateAndUrl(syncedValue, 'initial', router, {}, '/');

            // 2. 状态 → URL：修改状态，验证同步到 URL
            syncedValue.value = 'hello';
            await nextTick();
            expectStateAndUrl(syncedValue, 'hello', router, { text: 'hello' }, '/?text=hello');

            // 3. URL → 状态：修改 URL，验证同步到状态
            await router.replace({ query: { text: 'world' } });
            await nextTick();
            expectStateAndUrl(syncedValue, 'world', router, { text: 'world' }, '/?text=world');

            // 4. 设置为 undefined - 应该恢复默认值
            syncedValue.value = undefined;
            await nextTick();
            await nextTick(); // 额外等待，因为恢复默认值是异步的
            
            // 修复后的行为：简单类型设置为 undefined 后恢复默认值（与对象类型行为一致）
            expectStateAndUrl(syncedValue, 'initial', router, {}, '/');
        });

        it('应该正确同步数字类型', async () => {
            const { setupContext: { syncedPage } } = createWrapper(router, () => {
                const page = ref<number | undefined>(1);
                const syncedPage = useSyncWithQuery(page, 'page', { parseNumber: true });
                return { syncedPage };
            });

            // 1. 验证初始状态
            await nextTick();
            expectStateAndUrl(syncedPage, 1, router, {}, '/');

            // 2. 状态 → URL：修改数字值
            syncedPage.value = 2;
            await nextTick();
            expectStateAndUrl(syncedPage, 2, router, { page: '2' }, '/?page=2');

            // 3. URL → 状态：从 URL 解析数字
            await router.replace({ query: { page: '42' } });
            await nextTick();
            expectStateAndUrl(syncedPage, 42, router, { page: '42' }, '/?page=42');

            // 4. 设置为 undefined 应该恢复默认值
            syncedPage.value = undefined;
            await nextTick();
            await nextTick(); // 额外等待，因为恢复默认值是异步的
            expectStateAndUrl(syncedPage, 1, router, {}, '/');
        });
    });

    // ------------------------------------------------------------------------
    // 数组类型同步
    // ------------------------------------------------------------------------
    describe('数组类型同步', () => {
        it('应该正确处理数组的默认行为（无 parser）', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    tags: ['tag1', 'tag2'],
                    categories: ['cat1'],
                });

                // 没有指定任何 parser
                const syncedData = useSyncWithQuery(data);
                return { syncedData };
            });

            // 1. 验证初始状态
            await nextTick();
            expectStateAndUrl(
                syncedData,
                { tags: ['tag1', 'tag2'], categories: ['cat1'] },
                router,
                {},
                '/'
            );

            // 2. 状态 → URL：修改数组字段
            syncedData.value.tags = ['new1', 'new2', 'new3'];
            syncedData.value.categories = ['newCat'];
            await nextTick();

            // 验证数组使用 Vue Router 的默认行为（多个同名参数）
            expectStateAndUrl(
                syncedData,
                { tags: ['new1', 'new2', 'new3'], categories: ['newCat'] },
                router,
                { tags: ['new1', 'new2', 'new3'], categories: ['newCat'] },
                '/?tags=new1&tags=new2&tags=new3&categories=newCat'
            );

            // 3. 测试数组恢复行为
            syncedData.value.tags = undefined;
            await nextTick();
            await nextTick();
            
            expectStateAndUrl(
                syncedData,
                { tags: ['tag1', 'tag2'], categories: ['newCat'] },
                router,
                { categories: ['newCat'] },
                '/?categories=newCat'
            );
        });

        it('应该正确处理 URL 中的多个同名参数', async () => {
            // 预设 URL 中包含多个同名参数
            await router.replace({
                query: {
                    tags: ['url1', 'url2', 'url3'],
                    single: 'value',
                },
            });
            await nextTick();

            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    tags: ['initial'],
                    single: 'initial',
                });

                const syncedData = useSyncWithQuery(data);
                return { syncedData };
            });

            await nextTick();

            // 验证数组解析（符合 Vue Router 标准行为）
            expect(syncedData.value.tags).toEqual(['url1', 'url2', 'url3']); // 返回完整数组
            expect(syncedData.value.single).toBe('value');
        });
    });

    // ------------------------------------------------------------------------
    // 对象类型测试
    // ------------------------------------------------------------------------
    describe('对象类型同步', () => {
        it('应该正确同步对象字段', async () => {
            const { setupContext: { syncedFilters } } = createWrapper(router, () => {
                const filters = ref({
                    page: 1,
                    pageSize: '10',
                    active: true,
                    name: 'test',
                });

                const syncedFilters = useSyncWithQuery(filters, {
                    parseNumber: ['page'],
                    parseBoolean: ['active'],
                });
                return { syncedFilters };
            });

            // 1. 验证初始状态
            await nextTick();
            expectStateAndUrl(
                syncedFilters,
                { page: 1, pageSize: '10', active: true, name: 'test' },
                router,
                {},
                '/'
            );

            // 2. 修改字段测试状态 → URL 
            syncedFilters.value.page = 2;
            syncedFilters.value.pageSize = '20';
            syncedFilters.value.active = false;
            syncedFilters.value.name = 'modified';
            await nextTick();
            
            expectStateAndUrl(
                syncedFilters,
                { page: 2, pageSize: '20', active: false, name: 'modified' },
                router,
                { page: '2', pageSize: '20', active: 'false', name: 'modified' },
                '/?page=2&pageSize=20&active=false&name=modified'
            );

            // 3. 测试 URL → 状态
            await router.replace({ query: { page: '5', active: 'true', name: 'url-modified' } });
            await nextTick();
            
            // 验证状态更新，注意 pageSize 保持原状态因为 URL 中没有
            expect(syncedFilters.value).toEqual({ 
                page: 5, 
                pageSize: '10', // 恢复初始值
                active: true, 
                name: 'url-modified' 
            });
        });

        it('应该支持动态属性操作', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({ page: 1 });
                const syncedData = useSyncWithQuery(data, {
                    parseNumber: ['page', 'size'],
                    parseBoolean: ['active'],
                    strictMode: false, // 允许动态属性同步
                });
                return { syncedData };
            });

            await nextTick();

            // Vue.set 添加属性
            set(syncedData.value, 'size', 10);
            set(syncedData.value, 'active', true);
            await nextTick();

            expectStateAndUrl(
                syncedData,
                { page: 1, size: 10, active: true },
                router,
                { size: '10', active: 'true' },
                '/?size=10&active=true'
            );

            // Vue.delete 删除属性
            del(syncedData.value, 'size');
            await nextTick();
            await nextTick();

            // 删除动态添加的属性后，该属性从状态中移除，URL 中也删除
            expect(syncedData.value).toEqual({ page: 1, active: true });
            expect(router.currentRoute.query).toEqual({ active: 'true' });
            expect(router.currentRoute.fullPath).toBe('/?active=true');
        });
    });
});

// ============================================================================
// 高级配置功能测试
// ============================================================================
describe('高级配置功能', () => {
    let router: VueRouter;

    beforeEach(() => {
        router = new VueRouter({
            mode: 'abstract',
            routes: [{ path: '/', component: { template: '<div/>' } }],
        });
        router.push('/');
    });

    describe('ignoreKeys 功能', () => {
        it('应该忽略指定字段不同步到 URL', async () => {
            const { setupContext: { syncedFilters } } = createWrapper(router, () => {
                const filters = ref({
                    page: 1,
                    ignoredField: 'should-not-sync',
                    normalField: 'should-sync',
                });

                const syncedFilters = useSyncWithQuery(filters, {
                    ignoreKeys: ['ignoredField'],
                });
                
                return { syncedFilters };
            });

            await nextTick();

            // 修改被忽略的字段
            syncedFilters.value.ignoredField = 'changed';
            await nextTick();
            
            expectStateAndUrl(
                syncedFilters,
                { page: 1, ignoredField: 'changed', normalField: 'should-sync' },
                router,
                {},
                '/'
            );
            
            // 修改正常字段
            syncedFilters.value.normalField = 'changed';
            await nextTick();
            
            expectStateAndUrl(
                syncedFilters,
                { page: 1, ignoredField: 'changed', normalField: 'changed' },
                router,
                { normalField: 'changed' },
                '/?normalField=changed'
            );
        });

        it('应该支持动态 ignoreKeys', async () => {
            const { setupContext: { syncedFilters, dynamicKeys } } = createWrapper(router, () => {
                const dynamicKeys = ref(['dynamicField']);
                
                const filters = ref({
                    staticField: 'static',
                    dynamicField: 'dynamic',
                    normalField: 'normal',
                });

                const syncedFilters = useSyncWithQuery(filters, {
                    ignoreKeys: () => ['staticField', ...dynamicKeys.value],
                });
                
                return { syncedFilters, dynamicKeys };
            });

            await nextTick();

            // 修改正常字段
            syncedFilters.value.normalField = 'changed-normal';
            await nextTick();
            
            expectStateAndUrl(
                syncedFilters,
                { staticField: 'static', dynamicField: 'dynamic', normalField: 'changed-normal' },
                router,
                { normalField: 'changed-normal' },
                '/?normalField=changed-normal'
            );
            
            // 动态添加忽略字段
            dynamicKeys.value.push('normalField');
            
            // 再次修改现在被忽略的字段
            syncedFilters.value.normalField = 'should-be-ignored-now';
            await nextTick();
            
            // URL 应该保持不变
            expect(router.currentRoute.query).toEqual({ normalField: 'changed-normal' });
        });
    });

    describe('strictMode 功能', () => {
        it('严格模式应该拒绝 URL 中的额外参数', async () => {
            // 预设 URL 中包含额外参数
            await router.replace({ 
                query: { 
                    page: '2', 
                    category: 'books', 
                    extraParam: 'should-be-ignored',
                } 
            });
            await nextTick();

            const { setupContext: { syncedFilters } } = createWrapper(router, () => {
                const filters = ref({
                    page: 1,
                    category: 'electronics',
                });
                const syncedFilters = useSyncWithQuery(filters, {
                    parseNumber: ['page'],
                    strictMode: true, // 默认值
                });
                return { syncedFilters };
            });

            await nextTick();

            // 状态应该只包含初始定义的字段
            expect(syncedFilters.value).toEqual({ page: 2, category: 'books' });
            expect('extraParam' in syncedFilters.value).toBe(false);
        });

        it('非严格模式应该接受 URL 中的额外参数', async () => {
            // 预设 URL 中包含额外参数
            await router.replace({ 
                query: { 
                    page: '2', 
                    category: 'books', 
                    extraParam: 'should-be-accepted',
                } 
            });
            await nextTick();

            const { setupContext: { syncedFilters } } = createWrapper(router, () => {
                const filters = ref({
                    page: 1,
                    category: 'electronics',
                });
                const syncedFilters = useSyncWithQuery(filters, {
                    parseNumber: ['page'],
                    strictMode: false,
                });
                return { syncedFilters };
            });

            await nextTick();

            // 状态应该包含所有 URL 参数
            expect(syncedFilters.value).toEqual({ 
                page: 2, 
                category: 'books', 
                extraParam: 'should-be-accepted',
            });
        });
    });

    describe('Parser API 功能', () => {
        it('应该支持自定义 parser', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    tags: 'tag1,tag2,tag3',
                    config: '{"theme":"dark","lang":"zh"}',
                });

                const syncedData = useSyncWithQuery(data, {
                    parser: {
                        // 标签数组解析器
                        tags: {
                            get: (value: string | string[]) => {
                                if (Array.isArray(value)) return value;
                                return value ? value.split(',') : [];
                            },
                            set: (value: string[]) => value && value.length > 0 ? value.join(',') : undefined
                        },
                        // JSON 配置解析器
                        config: {
                            get: (value: string | string[]) => {
                                const str = Array.isArray(value) ? value[0] : value;
                                try {
                                    return JSON.parse(str);
                                } catch {
                                    return {};
                                }
                            },
                            set: (value: any) => value ? JSON.stringify(value) : undefined
                        },
                    }
                });

                return { syncedData };
            });

            await nextTick();

            // 检查初始值解析
            expect(syncedData.value.tags).toEqual(['tag1', 'tag2', 'tag3']);
            expect(syncedData.value.config).toEqual({ theme: 'dark', lang: 'zh' });

            // 测试修改值并同步到 URL
            syncedData.value.tags = ['newTag1', 'newTag2'];
            syncedData.value.config = { theme: 'light', lang: 'en' };
            await nextTick();

            expect(router.currentRoute.query.tags).toBe('newTag1,newTag2');
            expect(router.currentRoute.query.config).toBe('{"theme":"light","lang":"en"}');
        });

        it('应该支持只有 getter 的 parser', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    count: '42',
                    normalField: 'test'
                });

                const syncedData = useSyncWithQuery(data, {
                    parser: {
                        // 只有 getter 的解析器
                        count: (value: string | string[]) => {
                            const str = Array.isArray(value) ? value[0] : value;
                            return parseInt(str) || 0;
                        },
                    }
                });

                return { syncedData };
            });

            await nextTick();

            // 检查初始解析
            expect(syncedData.value.count).toBe(42);

            // 修改只有 getter 的字段
            syncedData.value.count = 100;
            await nextTick();

            // 只有 getter 的字段应该使用默认的序列化
            expect(router.currentRoute.query.count).toBe('100');
        });

        it('应该处理数组 parser', async () => {
            // 预设 URL 中包含多个同名参数
            await router.replace({
                query: {
                    tags: ['tag1', 'tag2', 'tag3'],
                },
            });
            await nextTick();

            const { setupContext: { filters } } = createWrapper(router, () => {
                const defaultFilters = ref({
                    tags: 'initial',
                });

                const filters = useSyncWithQuery(defaultFilters, {
                    parser: {
                        tags: (param: string | string[]) => {
                            return Array.isArray(param) ? param : [param];
                        },
                    },
                });

                return { filters };
            });

            await nextTick();

            // 初始状态应该正确应用 parser
            expect(filters.value.tags).toEqual(['tag1', 'tag2', 'tag3']);

            // 状态 → URL：修改有 parser 的字段
            filters.value.tags = ['new1', 'new2'];
            await nextTick();
            
            // 验证同步到 URL（使用 Vue Router 默认行为）
            expect(router.currentRoute.query.tags).toEqual(['new1', 'new2']);
            expect(router.currentRoute.fullPath).toBe('/?tags=new1&tags=new2');
        });
    });
});

// ============================================================================
// 多实例和边界情况测试
// ============================================================================
describe('多实例和边界情况', () => {
    let router: VueRouter;

    beforeEach(() => {
        router = new VueRouter({
            mode: 'abstract',
            routes: [{ path: '/', component: { template: '<div/>' } }],
        });
        router.push('/');
    });

    describe('多实例管理', () => {
        it('多个实例应该独立管理各自的字段', async () => {
            // 第一个实例
            const { setupContext: { filters1 } } = createWrapper(router, () => {
                const data = ref({ page: 1, category: 'books' });
                const filters1 = useSyncWithQuery(data, {
                    parseNumber: ['page'],
                });
                return { filters1 };
            });

            // 第二个实例
            const { setupContext: { filters2 } } = createWrapper(router, () => {
                const data = ref({ size: 10, active: true });
                const filters2 = useSyncWithQuery(data, {
                    parseNumber: ['size'],
                    parseBoolean: ['active'],
                });
                return { filters2 };
            });

            await nextTick();

            // 修改第一个实例
            filters1.value.page = 2;
            filters1.value.category = 'electronics';
            await nextTick();

            // 修改第二个实例
            filters2.value.size = 20;
            filters2.value.active = false;
            await nextTick();

            // URL 应该包含两个实例的所有字段
            expect(router.currentRoute.query).toEqual({
                page: '2',
                category: 'electronics',
                size: '20',
                active: 'false',
            });

            // 各实例应该维护自己的状态
            expect(filters1.value).toEqual({ page: 2, category: 'electronics' });
            expect(filters2.value).toEqual({ size: 20, active: false });
        });

        it('多实例不同 ignoreKeys 策略', async () => {
            // 第一个实例：忽略 privateField1
            const { setupContext: { sync1 } } = createWrapper(router, () => {
                const data = ref({ 
                    shared: 'initial', 
                    privateField1: 'private1',
                    normalField: 'normal1',
                });
                const sync1 = useSyncWithQuery(data, {
                    ignoreKeys: ['privateField1'],
                });
                return { sync1 };
            });

            await nextTick();

            // 修改第一个实例的字段
            sync1.value.shared = 'modified';
            sync1.value.privateField1 = 'changed-private1'; // 应该被忽略
            sync1.value.normalField = 'changed-normal1';
            
            await nextTick();

            // URL 应该只包含第一个实例未被忽略的字段
            expect(router.currentRoute.query).toEqual({
                shared: 'modified',
                normalField: 'changed-normal1',
            });

            // 第二个实例：忽略 privateField2
            const { setupContext: { sync2 } } = createWrapper(router, () => {
                const data = ref({ 
                    shared: 'initial', 
                    privateField2: 'private2',
                    normalField: 'normal2',
                });
                const sync2 = useSyncWithQuery(data, {
                    ignoreKeys: ['privateField2'],
                });
                return { sync2 };
            });

            await nextTick();

            // 第二个实例应该从 URL 获取已有的状态
            expect(sync2.value.shared).toBe('modified');
            expect(sync2.value.normalField).toBe('changed-normal1');

            // 私有字段应该保持各自实例的默认值，不受 URL 影响
            expect(sync1.value.privateField1).toBe('changed-private1');
            expect(sync2.value.privateField2).toBe('private2');
        });
    });

    describe('边界情况', () => {
        it('应该处理特殊值', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    nullField: null,
                    emptyString: '',
                    zeroNumber: 0,
                    falseBool: false,
                });
                const syncedData = useSyncWithQuery(data, {
                    parseNumber: ['zeroNumber'],
                    parseBoolean: ['falseBool'],
                });
                return { syncedData };
            });

            await nextTick();

            // 验证初始状态处理
            expectStateAndUrl(
                syncedData,
                { 
                    nullField: null, 
                    emptyString: '', 
                    zeroNumber: 0, 
                    falseBool: false,
                },
                router,
                {},
                '/'
            );

            // 修改为非默认值
            syncedData.value.nullField = 'not-null';
            syncedData.value.emptyString = 'not-empty';
            syncedData.value.zeroNumber = 5;
            syncedData.value.falseBool = true;
            await nextTick();

            expectStateAndUrl(
                syncedData,
                { 
                    nullField: 'not-null', 
                    emptyString: 'not-empty', 
                    zeroNumber: 5, 
                    falseBool: true,
                },
                router,
                { 
                    nullField: 'not-null', 
                    emptyString: 'not-empty', 
                    zeroNumber: '5', 
                    falseBool: 'true',
                },
                '/?nullField=not-null&emptyString=not-empty&zeroNumber=5&falseBool=true'
            );
        });

        it('应该处理 parser 异常情况', async () => {
            const { setupContext: { syncedData } } = createWrapper(router, () => {
                const data = ref({
                    json: '{"valid": true}',
                    number: '42'
                });

                const syncedData = useSyncWithQuery(data, {
                    parser: {
                        json: {
                            get: (value: string | string[]) => {
                                const str = Array.isArray(value) ? value[0] : value;
                                try {
                                    return JSON.parse(str);
                                } catch {
                                    return null; // 解析失败返回 null
                                }
                            },
                            set: (value: any) => value ? JSON.stringify(value) : undefined,
                        },
                        number: {
                            get: (value: string | string[]) => {
                                const str = Array.isArray(value) ? value[0] : value;
                                const num = parseInt(str, 10);
                                return isNaN(num) ? 0 : num;
                            },
                            set: (value: number) => value.toString(),
                        },
                    },
                });

                return { syncedData };
            });

            await nextTick();

            // 测试从 URL 解析无效的 JSON
            await router.replace({ 
                query: { 
                    json: 'invalid-json',
                    number: 'not-a-number'
                } 
            });
            await nextTick();

            expect(syncedData.value.json).toBeNull();
            expect(syncedData.value.number).toBe(0);
        });
    });
});

