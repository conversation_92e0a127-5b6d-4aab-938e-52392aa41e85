import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach } from 'vitest';
import Vue, { ref, nextTick, set, del } from 'vue';
import VueRouter from 'vue-router';

import { useSyncWithQuery } from '../use-sync-with-query';

// 安装 VueRouter
Vue.use(VueRouter);

// ============================================================================
// 测试工具函数
// ============================================================================

/**
 * 创建测试环境
 */
function createTestEnvironment() {
    const router = new VueRouter({
        mode: 'abstract',
        routes: [{ path: '/', component: { template: '<div/>' } }],
    });
    router.push('/');
    
    return { router };
}

/**
 * 创建组件包装器
 */
function createWrapper<T>(router: VueRouter, setup: () => T): { result: T } {
    let setupResult: T;
    const TestComponent = {
        template: '<div/>',
        setup() {
            setupResult = setup();
            return setupResult;
        },
    };

    mount(TestComponent, { router });
    return { result: setupResult! };
}

/**
 * 验证状态和URL的一致性
 */
function expectStateAndUrl(
    state: any,
    expectedState: any,
    router: VueRouter,
    expectedQuery: Record<string, any>,
    expectedPath: string
) {
    expect(state.value, `状态应该为: ${JSON.stringify(expectedState)}`).toEqual(expectedState);
    expect(router.currentRoute.query, `URL query应该为: ${JSON.stringify(expectedQuery)}`).toEqual(expectedQuery);
    expect(router.currentRoute.fullPath, `URL路径应该为: ${expectedPath}`).toBe(expectedPath);
}

/**
 * 验证双向同步
 */
async function expectBidirectionalSync(
    state: any,
    stateValue: any,
    router: VueRouter,
    expectedQuery: Record<string, any>,
    expectedPath: string
) {
    // 状态 → URL
    state.value = stateValue;
    await nextTick();
    expectStateAndUrl(state, stateValue, router, expectedQuery, expectedPath);

    // URL → 状态（跳过如果已经在目标状态）
    if (router.currentRoute.fullPath !== expectedPath) {
        await router.replace({ query: expectedQuery });
        await nextTick();
    }
    expectStateAndUrl(state, stateValue, router, expectedQuery, expectedPath);
}

// ============================================================================
// 1. 核心概念测试 (Core Functionality)
// ============================================================================

describe('Core Functionality - 核心概念', () => {
    let router: VueRouter;

    beforeEach(() => {
        ({ router } = createTestEnvironment());
    });

    describe('双向同步机制', () => {
        it('应该实现状态到URL的同步', async () => {
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery(ref('initial'), 'text')
            }));

            syncedValue.value = 'changed';
            await nextTick();

            expect(router.currentRoute.query).toEqual({ text: 'changed' });
            expect(router.currentRoute.fullPath).toBe('/?text=changed');
        });

        it('应该实现URL到状态的同步', async () => {
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery(ref('initial'), 'text')
            }));

            await router.replace({ query: { text: 'from-url' } });
            await nextTick();

            expect(syncedValue.value).toBe('from-url');
        });

        it('应该支持完整的双向同步', async () => {
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery(ref('initial'), 'text')
            }));

            await expectBidirectionalSync(
                syncedValue,
                'baz',
                router,
                { text: 'baz' },
                '/?text=baz'
            );
        });
    });

    describe('类型系统差异', () => {
        it('简单类型：应该使用key-value模式', async () => {
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery('foo', 'a')
            }));

            syncedValue.value = 'baz';
            await nextTick();

            expect(router.currentRoute.query, '简单类型应该使用key-value模式同步到URL').toEqual({ a: 'baz' });
        });

        it('对象类型：应该将属性展开到URL', async () => {
            const { result: { syncedObject } } = createWrapper(router, () => ({
                syncedObject: useSyncWithQuery({ a: 'foo', b: 25 })
            }));

            expect(syncedObject.value).toEqual({ a: 'foo', b: 25 });
            expect(router.currentRoute.query).toEqual({});
            syncedObject.value = { a: 'baz', b: 30 };
            await nextTick();

            expect(router.currentRoute.query, '对象类型应该将属性展开到URL，数字转为字符串').toEqual({ a: 'baz', b: '30' });
        });
    });

    describe('初始化行为', () => {
        it('应该优先使用URL中的值初始化', async () => {
            await router.replace({ query: { a: 'baz', b: 'bar' } });
            
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery('foo', 'a')
            }));

            expect(syncedValue.value).toBe('baz');
            expect(router.currentRoute.query).toEqual({ a: 'baz', b: 'bar' });
            expect(router.currentRoute.fullPath).toBe('/?a=baz&b=bar');
        });

        it('URL中无值时应该使用默认值', async () => {
            const { result: { syncedValue } } = createWrapper(router, () => ({
                syncedValue: useSyncWithQuery('initial', 'a')
            }));

            expect(syncedValue.value, '无URL参数时应该使用默认值').toBe('initial');
            expect(router.currentRoute.query, 'URL query应该为空对象').toEqual({});
            expect(router.currentRoute.fullPath, 'URL路径应该为根路径').toBe('/');
        });
    });
});

// ============================================================================
// 2. API接口测试 (API Interface)
// ============================================================================

describe('API Interface - API接口', () => {
    let router: VueRouter;

    beforeEach(() => {
        ({ router } = createTestEnvironment());
    });

    describe('函数重载', () => {
        it('重载1: useSyncWithQuery(ref, key)', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery('foo', 'a')
            }));

            synced.value = 'baz';
            await nextTick();

            expect(router.currentRoute.query).toEqual({ a: 'baz' });
        });

        it('重载2: useSyncWithQuery(ref, key, options)', async () => {
            await router.replace({ query: { count: '1' } });
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(42, 'count', { parseNumber: true })
            }));

            expect(synced.value).toBe(1);
        });

        it('重载3: useSyncWithQuery(ref, options)', async () => {
            await router.replace({ query: { x: '1', y: '1' } });
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery({ x: 1, y: 1 }, { parseNumber: ['x'] })
            }));


            expect(synced.value, '对象状态应该包含count=1').toEqual({ x: 1, y: '1' });
        });

        it('重载3: useSyncWithQuery(ref, options)', async () => {
            await router.replace({ query: { x: '1', y: '1' } });
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery({ x: 1, y: 1 }, { parseNumber: true })
            }));

            expect(synced.value, '对象状态应该包含count=1').toEqual({ x: 1, y: 1 });
        });
    });

    describe('配置选项', () => {
        it('parseNumber: true - 应该解析所有数字', async () => {
            await router.replace({ query: { x: '42' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(0, 'x', { parseNumber: true })
            }));

            expect(synced.value).toBe(42);
        });

        it('parseNumber: [keys] - 应该只解析指定字段', async () => {
            await router.replace({ query: { x: '42', y: '123' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ x: 0, y: '123' }), { parseNumber: ['x'] })
            }));

            expect(synced.value).toEqual({ x: 42, y: '123' });
        });

        it('parseBoolean: true - 应该解析布尔值', async () => {
            await router.replace({ query: { flag: 'true' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref(false), 'flag', { parseBoolean: true })
            }));

            expect(synced.value, 'parseBoolean应该将字符串true解析为布尔值true').toBe(true);
        });

        it('strictMode: false - 应该接受额外参数', async () => {
            await router.replace({ query: { defined: 'value', extra: 'should-accept' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ defined: 'default' }), { strictMode: false })
            }));

            expect(synced.value, '非严格模式应该接受URL中的额外参数extra').toEqual({ defined: 'value', extra: 'should-accept' });
        });
    });
});

// ============================================================================
// 3. 数据类型测试 (Data Types)
// ============================================================================

describe('Data Types - 数据类型', () => {
    let router: VueRouter;

    beforeEach(() => {
        ({ router } = createTestEnvironment());
    });

    describe('基础类型', () => {
        const basicTypeTests = [
            { type: 'string', initial: 'test', changed: 'changed', urlValue: 'changed' },
            { type: 'number', initial: 42, changed: 100, urlValue: '100', options: { parseNumber: true } },
            { type: 'boolean', initial: true, changed: false, urlValue: 'false', options: { parseBoolean: true } },
        ] as const;

        basicTypeTests.forEach(({ type, initial, changed, urlValue, options }) => {
            it(`${type}类型 - 应该正确处理双向同步`, async () => {
                const { result: { synced } } = createWrapper(router, () => ({
                    synced: useSyncWithQuery(ref(initial), 'value', options)
                }));

                // 测试状态 → URL
                synced.value = changed;
                await nextTick();
                expect(router.currentRoute.query.value).toBe(urlValue);

                // 测试URL → 状态（只在需要时才导航）
                if (router.currentRoute.query.value !== urlValue) {
                    await router.replace({ query: { value: urlValue } });
                    await nextTick();
                }
                expect(synced.value).toBe(changed);
            });
        });

        it('undefined - 应该恢复默认值', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref('default'), 'text')
            }));

            synced.value = undefined;
            await nextTick();

            expect(synced.value).toBe('default');
            expect(router.currentRoute.query).toEqual({});
        });

        it('null、undefined', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery({ a: 'foo' })
            }));

            synced.value.a = 'baz';
            await nextTick();
            expect(synced.value.a).toEqual('baz');
            expect(router.currentRoute.fullPath).toEqual('/?a=baz');

            synced.value.a = undefined;
            await nextTick();
            expect(synced.value.a).toEqual('foo');
            expect(router.currentRoute.fullPath).toEqual('/');

            synced.value.a = null;
            await nextTick();
            expect(synced.value.a).toEqual(null);
            expect(router.currentRoute.fullPath).toEqual('/?a');
        });
    });

    describe('复合类型', () => {
        it('Array - 应该保持Vue Router的数组行为', async () => {
            await router.replace({ query: { tags: ['tag1', 'tag2', 'tag3'] } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery({ tags: ['default'] })
            }));

            expect(synced.value.tags).toEqual(['tag1', 'tag2', 'tag3']);
        });

        it('Object - 应该展开属性到URL', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    name: 'test', 
                    settings: { theme: 'dark' }
                }))
            }));

            synced.value = { 
                name: 'changed', 
                settings: { theme: 'light' }
            };
            await nextTick();

            // 对象被转换为 [object Object]，除非有自定义parser
            expect(router.currentRoute.query).toEqual({ 
                name: 'changed', 
                settings: '[object Object]'
            });
        });
    });

    describe('特殊值处理', () => {
        const specialValues = [
            { name: '空字符串', value: '', expected: '' },
            { name: '数字0', value: 0, expected: '0' },
            { name: '布尔false', value: false, expected: 'false' },
        ] as const;

        specialValues.forEach(({ name, value, expected }) => {
            it(`${name} - 应该正确处理`, async () => {
                const { result: { synced } } = createWrapper(router, () => ({
                    synced: useSyncWithQuery(ref({ special: value }))
                }));

                // 非默认值应该同步到URL
                synced.value.special = value === 0 ? 1 : (value === false ? true : 'non-empty');
                await nextTick();

                expect(router.currentRoute.query.special).toBeDefined();
            });
        });

        it('数字和布尔解析失败时应该返回原值而不是默认值', async () => {
            // 设置包含无效数字和布尔值的URL
            await router.replace({ 
                query: { 
                    invalidNumber: 'not-a-number',
                    invalidBoolean: 'not-true-or-false'
                } 
            });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    invalidNumber: 42, // 默认值是数字
                    invalidBoolean: true // 默认值是布尔
                }), {
                    parseNumber: ['invalidNumber'],
                    parseBoolean: ['invalidBoolean']
                })
            }));

            await nextTick();

            // 验证解析失败时返回原始字符串值，而不是默认值
            expect(synced.value.invalidNumber).toBe('not-a-number'); // 应该返回原值
            expect(synced.value.invalidBoolean).toBe('not-true-or-false'); // 应该返回原值
            
            // 验证这些都不等于默认值
            expect(synced.value.invalidNumber).not.toBe(42);
            expect(synced.value.invalidBoolean).not.toBe(true);
        });

        it('应该正确处理URL中的falsy值而不回退到默认值', async () => {
            // 设置包含falsy值的URL
            await router.replace({ 
                query: { 
                    emptyString: '',
                    zero: '0', 
                    falseValue: 'false',
                    nullValue: 'null'
                } 
            });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    emptyString: 'default',
                    zero: 'default', 
                    falseValue: 'default',
                    nullValue: 'default'
                }))
            }));

            await nextTick();

            // 验证falsy值被正确解析，而不是回退到默认值
            expect(synced.value.emptyString).toBe(''); // 空字符串应该被保留
            expect(synced.value.zero).toBe('0'); // 字符串'0'应该被保留  
            expect(synced.value.falseValue).toBe('false'); // 字符串'false'应该被保留
            expect(synced.value.nullValue).toBe('null'); // 字符串'null'应该被保留
            
            // 验证这些都不等于默认值
            expect(synced.value.emptyString).not.toBe('default');
            expect(synced.value.zero).not.toBe('default');
            expect(synced.value.falseValue).not.toBe('default');
            expect(synced.value.nullValue).not.toBe('default');
        });
    });
});

// ============================================================================
// 4. 高级功能测试 (Advanced Features)
// ============================================================================

describe('Advanced Features - 高级功能', () => {
    let router: VueRouter;

    beforeEach(() => {
        ({ router } = createTestEnvironment());
    });

    describe('自定义Parser', () => {
        it('应该支持get/set parser', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ tags: 'tag1,tag2' }), {
                    parser: {
                        tags: {
                            get: (value: string | string[]) => {
                                const str = Array.isArray(value) ? value[0] : value;
                                return str ? str.split(',') : [];
                            },
                            set: (value: string[]) => value.join(',')
                        }
                    }
                })
            }));

            expect(synced.value.tags).toEqual(['tag1', 'tag2']);

            synced.value.tags = ['new1', 'new2'];
            await nextTick();

            expect(router.currentRoute.query.tags).toBe('new1,new2');
        });

        it('应该支持函数式parser', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ count: '42' }), {
                    parser: {
                        count: (value: string | string[]) => {
                            const str = Array.isArray(value) ? value[0] : value;
                            return parseInt(str) || 0;
                        }
                    }
                })
            }));

            expect(synced.value.count).toBe(42);
        });
    });

    describe('忽略键功能', () => {
        it('应该支持静态ignoreKeys', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    public: 'public', 
                    private: 'private' 
                }), {
                    ignoreKeys: ['private']
                })
            }));

            synced.value.public = 'changed-public';
            synced.value.private = 'changed-private';
            await nextTick();

            expect(router.currentRoute.query, '被忽略的字段不应该同步到URL').toEqual({ public: 'changed-public' });
            expect(synced.value.private).toBe('changed-private'); // 状态中保持
        });

        it('应该支持动态ignoreKeys', async () => {
            const dynamicKeys = ref(['field1']);
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    field1: 'value1', 
                    field2: 'value2' 
                }), {
                    ignoreKeys: () => dynamicKeys.value
                })
            }));

            // 初始状态
            synced.value.field2 = 'changed';
            await nextTick();
            expect(router.currentRoute.query).toEqual({ field2: 'changed' });

            // 动态添加忽略字段
            dynamicKeys.value.push('field2');
            synced.value.field2 = 'should-be-ignored';
            await nextTick();
            
            expect(router.currentRoute.query.field2, '动态添加到ignoreKeys的字段应该保持URL中的原值').toBe('changed');
        });
    });

    describe('严格模式', () => {
        it('严格模式：应该拒绝额外参数', async () => {
            await router.replace({ query: { 
                defined: 'value', 
                extra: 'should-ignore' 
            }});
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ defined: 'default' }), { strictMode: true })
            }));

            expect(synced.value).toEqual({ defined: 'value' });
            expect('extra' in synced.value).toBe(false);
        });

        it('非严格模式：应该接受额外参数', async () => {
            await router.replace({ query: { 
                defined: 'value', 
                extra: 'should-accept' 
            }});
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ defined: 'default' }), { strictMode: false })
            }));

            expect(synced.value).toEqual({ defined: 'value', extra: 'should-accept' });
        });
    });

    describe('多实例独立性', () => {
        it('应该正确处理并发状态更新而不丢失修改', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ count: 0 }))
            }));

            // 快速连续修改状态（模拟并发更新）
            synced.value.count = 1;
            synced.value.count = 2;
            synced.value.count = 3;
            
            // 等待响应式更新完成
            await nextTick();
            
            // 最终状态应该是最后一次修改的值
            expect(synced.value.count).toBe(3);
            expect(router.currentRoute.query.count).toBe('3');
            expect(router.currentRoute.fullPath).toBe('/?count=3');
        });

        it('多实例应该独立管理各自字段', async () => {
            const { result: { instance1 } } = createWrapper(router, () => ({
                instance1: useSyncWithQuery(ref({ page: 1 }))
            }));

            const { result: { instance2 } } = createWrapper(router, () => ({
                instance2: useSyncWithQuery(ref({ size: 10 }))
            }));

            instance1.value.page = 2;
            instance2.value.size = 20;
            await nextTick();

            expect(router.currentRoute.query, '多实例应该在URL中合并各自的字段').toEqual({ page: '2', size: '20' });
            expect(instance1.value).toEqual({ page: 2 });
            expect(instance2.value).toEqual({ size: 20 });
        });

        it('应该支持响应式的defaultValue', async () => {
            const defaultValue = ref({ a: 'initial', b: 100 });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(defaultValue)
            }));

            // 初始状态
            expect(synced.value).toEqual({ a: 'initial', b: 100 });
            
            // 修改状态
            synced.value.a = 'changed';
            await nextTick();
            expect(router.currentRoute.query).toEqual({ a: 'changed' });
            
            // 修改默认值，应该增加新字段
            defaultValue.value = { a: 'initial', b: 100, c: 'new' };
            await nextTick();
            
            // 状态应该包含新字段，但保留已修改的值
            expect(synced.value).toEqual({ a: 'changed', b: 100, c: 'new' });

            synced.value.c = 'changed';
            await nextTick();
            
            // URL应该只显示非默认值
            expect(router.currentRoute.query).toEqual({ a: 'changed', c: 'changed' });
        });
    });
});

// ============================================================================
// 5. 性能和边界测试 (Performance & Edge Cases)
// ============================================================================

describe('Performance & Edge Cases - 性能和边界', () => {
    let router: VueRouter;

    beforeEach(() => {
        ({ router } = createTestEnvironment());
    });

    describe('异常处理', () => {
        it('应该处理Parser异常', async () => {
            await router.replace({ query: { json: 'invalid-json' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ json: '{}' }), {
                    parser: {
                        json: {
                            get: (value: string | string[]) => {
                                const str = Array.isArray(value) ? value[0] : value;
                                try {
                                    return JSON.parse(str);
                                } catch {
                                    return null;
                                }
                            },
                            set: (value: any) => JSON.stringify(value)
                        }
                    }
                })
            }));

            expect(synced.value.json).toBeNull();
        });

        it('应该安全处理解析器抛出异常的情况', async () => {
            await router.replace({ query: { 
                throwError: 'should-throw',
                normal: 'normal-value'
            }});
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    throwError: 'default',
                    normal: 'default'
                }), {
                    parser: {
                        throwError: {
                            get: () => {
                                throw new Error('Parser error');
                            },
                            set: (value: any) => String(value)
                        }
                    }
                })
            }));

            // 异常处理后应该返回原值或默认值
            expect(synced.value.throwError).toBe('should-throw');
            expect(synced.value.normal).toBe('normal-value');
        });

        it('应该安全处理序列化器抛出异常的情况', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    throwError: 'default',
                    normal: 'default'
                }), {
                    parser: {
                        throwError: {
                            get: (value: string | string[]) => value,
                            set: () => {
                                throw new Error('Serializer error');
                            }
                        }
                    }
                })
            }));

            // 修改会触发序列化
            synced.value.throwError = 'new-value';
            synced.value.normal = 'normal-changed';
            await nextTick();

            // 序列化异常的字段应该fallback到字符串形式
            expect(router.currentRoute.query.throwError).toBe('new-value');
            expect(router.currentRoute.query.normal).toBe('normal-changed');
        });

        it('应该处理无效的URL参数', async () => {
            await router.replace({ query: { num: 'not-a-number' } });
            
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ num: 42 }), { parseNumber: ['num'] })
            }));

            expect(synced.value.num).toBe('not-a-number'); // 保持原值，不回退到默认值
        });
    });

    describe('Vue特性兼容', () => {
        it('应该支持Vue.set动态添加属性', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ initial: 'value' }), { strictMode: false })
            }));

            set(synced.value, 'dynamic', 'added');
            await nextTick();

            expect(router.currentRoute.query, 'Vue.set动态添加的属性应该同步到URL').toEqual({ 
                dynamic: 'added' 
            });
        });

        it('应该支持Vue.delete删除属性', async () => {
            const { result: { synced } } = createWrapper(router, () => ({
                synced: useSyncWithQuery(ref({ 
                    keep: 'value', 
                    remove: 'will-be-removed' 
                }))
            }));

            // 首先确保初始状态同步到URL
            await nextTick();
            
            del(synced.value, 'remove');
            await nextTick();
            await nextTick(); // 等待恢复默认值

            expect(synced.value.remove).toBe('will-be-removed'); // 应该恢复默认值
            // URL中只保留keep字段，remove被删除后恢复为默认值，不在URL中显示
            expect(router.currentRoute.query).toEqual({});
        });
    });
});