import type { MaybeRef } from '@vueuse/core';
import { isEqual } from 'lodash';
import { unref, type Ref, ref, computed, watch, onUnmounted } from 'vue';

import { useRouter, useRoute } from './use-router';

type UrlState = Record<string, any>;

interface SimpleTypeOptions {
    parseNumber?: MaybeRef<boolean>;
    parseBoolean?: MaybeRef<boolean>;
    strictMode?: MaybeRef<boolean>;
}

type Parser<T = any> = {
    get: (value: string | string[]) => T;
    set: (value: T) => string | string[] | undefined;
} | ((value: string | string[]) => T);

interface UseSyncWithQueryOptions {
    parseNumber?: MaybeRef<string[] | true>;
    parseBoolean?: MaybeRef<string[] | true>;
    ignoreKeys?: MaybeRef<string[]> | (() => string[]);
    strictMode?: MaybeRef<boolean>;
    parser?: Record<string, Parser>;
}

// 统一的值处理器
interface ValueProcessor {
    parse: (value: string | string[] | null) => any;
    serialize: (value: any) => string | string[] | null;
    shouldSync: (key: string) => boolean;
    parseFromUrl: (query: Record<string, any>) => any;
    serializeToUrl: (state: any, currentQuery: Record<string, any>) => Record<string, any>;
}

// 创建统一的值处理器
function createValueProcessor(
    isSimpleType: boolean,
    keyOrOptions: string | MaybeRef<UseSyncWithQueryOptions> | undefined,
    options: SimpleTypeOptions | UseSyncWithQueryOptions,
    defaultValueRef: MaybeRef<any>
): ValueProcessor {
    if (isSimpleType) {
        const key = keyOrOptions as string;
        const opts = options as SimpleTypeOptions;
        
        return {
            parse: (value: string | string[] | null) => {
                return parseSimpleValue(value, opts);
            },
            serialize: (value: any) => {
                return serializeSimpleValue(value, unref(defaultValueRef));
            },
            shouldSync: () => true,
            parseFromUrl: (query: Record<string, any>) => {
                const defaultValue = unref(defaultValueRef);
                const urlValue = query[key];
                if (urlValue !== undefined) {
                    const parsed = parseSimpleValue(urlValue, opts);
                    return parsed !== undefined ? parsed : defaultValue;
                }
                return defaultValue;
            },
            serializeToUrl: (state: any, currentQuery: Record<string, any>) => {
                const query = { ...currentQuery };
                const defaultValue = unref(defaultValueRef);
                
                // 如果状态为 undefined，恢复默认值
                if (state === undefined) {
                    delete query[key];
                    return query;
                }
                
                const serialized = serializeSimpleValue(state, defaultValue);
                
                if (serialized !== null) {
                    query[key] = serialized;
                } else {
                    delete query[key];
                }
                
                return query;
            }
        };
    } else {
        const opts = unref(keyOrOptions || {}) as UseSyncWithQueryOptions;
        
        const getConfig = () => {
            const ignoreKeysOption = opts.ignoreKeys;
            let ignoreKeys: string[] = [];
            if (typeof ignoreKeysOption === 'function') {
                ignoreKeys = ignoreKeysOption();
            } else {
                ignoreKeys = unref(ignoreKeysOption) || [];
            }
            
            return {
                parseNumber: unref(opts.parseNumber),
                parseBoolean: unref(opts.parseBoolean),
                parser: opts.parser,
                ignoreKeys,
                strictMode: unref(opts.strictMode) ?? true,
            };
        };
        
        const parseValue = (key: string, value: string | string[]) => {
            return safeParseValue(key, value, getConfig(), unref(defaultValueRef));
        };
        
        const serializeValue = (key: string, value: any) => {
            return safeSerializeValue(key, value, getConfig(), unref(defaultValueRef));
        };
        
        return {
            parse: () => null, // 对象类型不使用单个值解析
            serialize: () => null, // 对象类型不使用单个值序列化
            shouldSync: (key: string) => !getConfig().ignoreKeys.includes(key),
            parseFromUrl: (query: Record<string, any>) => {
                const defaultValue = unref(defaultValueRef);
                const result = { ...defaultValue } as any;
                const config = getConfig();
                
                // 处理初始状态中的字段
                Object.keys(defaultValue as any).forEach(fieldKey => {
                    const urlValue = query[fieldKey];
                    if (urlValue !== undefined) {
                        result[fieldKey] = parseValue(fieldKey, urlValue);
                    } else {
                        // 应用自定义parser到初始值
                        const initialValue = (defaultValue as any)[fieldKey];
                        if (config.parser?.[fieldKey] && typeof initialValue === 'string') {
                            result[fieldKey] = parseValue(fieldKey, initialValue);
                        }
                    }
                });
                
                // 处理 URL 中的额外字段（非严格模式）
                if (!config.strictMode) {
                    Object.keys(query).forEach(urlKey => {
                        const urlValue = query[urlKey];
                        if (urlValue !== undefined && !(urlKey in (defaultValue as any))) {
                            result[urlKey] = parseValue(urlKey, urlValue);
                        }
                    });
                }
                
                return result;
            },
            serializeToUrl: (state: any, currentQuery: Record<string, any>) => {
                const query = { ...currentQuery };
                const config = getConfig();
                const defaultValue = unref(defaultValueRef);
                
                // 清理当前实例管理的键（但不清理被忽略的键）
                const keysToClean = new Set([
                    ...Object.keys(defaultValue as any).filter(key => !config.ignoreKeys.includes(key))
                ]);
                
                if (!config.strictMode) {
                    Object.keys(query).forEach(urlKey => {
                        if (!(urlKey in (defaultValue as any)) && !config.ignoreKeys.includes(urlKey)) {
                            keysToClean.add(urlKey);
                        }
                    });
                }
                
                keysToClean.forEach(key => {
                    delete query[key];
                });
                
                // 检查并处理被删除或设置为undefined的属性
                const defaultKeys = Object.keys(defaultValue as any);
                const missingKeys = defaultKeys.filter(key => !(key in (state as any)) || ((state as any)[key] === undefined));
                
                // 如果有属性被删除或undefined，停止序列化，由上层的watch恢复状态
                if (missingKeys.length > 0) {
                    return query; // 不更新URL，等待状态恢复
                }
                
                // 序列化新状态
                Object.keys(state as any).forEach(key => {
                    if (config.ignoreKeys.includes(key)) return;
                    
                    // 严格模式下，只同步初始状态中声明的属性
                    if (config.strictMode && !(key in (defaultValue as any))) {
                        return;
                    }
                    
                    const value = (state as any)[key];
                    const serialized = serializeValue(key, value);
                    
                    if (serialized !== null) {
                        query[key] = serialized;
                    } else if (value === null) {
                        // 真正的null值应该在URL中显示为空参数
                        query[key] = null;
                    }
                });
                
                return query;
            }
        };
    }
}

// 简单类型解析辅助函数
function parseSimpleValue(value: string | string[] | null, options: SimpleTypeOptions): any {
    if (value === null || value === undefined) return undefined;
    
    const stringValue = Array.isArray(value) ? value[0] : value;
    if (!stringValue) return undefined;
    
    if (unref(options.parseNumber)) {
        const num = Number(stringValue);
        return isNaN(num) ? undefined : num;
    }
    
    if (unref(options.parseBoolean)) {
        if (stringValue === 'true') return true;
        if (stringValue === 'false') return false;
        return stringValue;
    }
    
    return stringValue;
}

// 简单类型序列化辅助函数
function serializeSimpleValue(value: any, defaultValue: any): string | string[] | null {
    if (value === undefined) return null;
    if (isEqual(value, defaultValue)) return null;
    
    if (Array.isArray(value)) return value;
    if (value === null) return null;
    return String(value);
}

// 安全的解析函数，支持异常处理
function safeParseValue(
    key: string, 
    value: string | string[], 
    config: { parseNumber?: any; parseBoolean?: any; parser?: any }, 
    defaultValue: any
): any {
    try {
        // 自定义 parser 优先
        if (config.parser?.[key]) {
            const parser = config.parser[key];
            if (typeof parser === 'function') {
                return parser(value);
            } else if (parser?.get) {
                return parser.get(value);
            }
        }
        
        // 没有自定义 parser，使用内置解析逻辑
        
        // Vue Router 已经处理好数组、字符串等类型，我们只需要处理特殊类型转换
        
        // 处理数字解析
        const shouldParseNumber = Array.isArray(config.parseNumber) 
            ? config.parseNumber.includes(key)
            : config.parseNumber === true;
        if (shouldParseNumber) {
            const stringValue = Array.isArray(value) ? value[0] : value;
            if (stringValue) {
                const num = Number(stringValue);
                if (!isNaN(num)) {
                    return num;
                }
                // 解析失败时继续执行，返回原值
            }
        }
        
        // 处理布尔解析
        const shouldParseBoolean = Array.isArray(config.parseBoolean)
            ? config.parseBoolean.includes(key)
            : config.parseBoolean === true;
        if (shouldParseBoolean) {
            const stringValue = Array.isArray(value) ? value[0] : value;
            if (stringValue === 'true') return true;
            if (stringValue === 'false') return false;
        }
        
        // 其他情况直接返回 Vue Router 的值
        return value !== undefined ? value : (defaultValue as any)[key];
    } catch (error) {
        // 解析出错时返回原值或默认值
        console.warn(`解析值时出错 [${key}]:`, error);
        return value !== undefined ? value : (defaultValue as any)[key];
    }
}

// 安全的序列化函数，支持异常处理
function safeSerializeValue(
    key: string, 
    value: any, 
    config: { parser?: any }, 
    defaultValue: any
): string | string[] | null {
    try {
        if (value === undefined) return null;
        if (isEqual(value, (defaultValue as any)[key])) return null;
        
        // 自定义 parser 优先
        if (config.parser?.[key]) {
            const parser = config.parser[key];
            if (typeof parser === 'object' && parser?.set) {
                const result = parser.set(value);
                return result === undefined ? null : result;
            }
        }
        
        // 没有自定义 parser，直接返回值让 Vue Router 处理
        if (Array.isArray(value)) return value;
        if (value === null) return null; // Vue Router中的null值
        return String(value);
    } catch (error) {
        // 序列化出错时返回字符串形式或null
        console.warn(`序列化值时出错 [${key}]:`, error);
        try {
            return String(value);
        } catch {
            return null;
        }
    }
}


// 函数重载定义
export function useSyncWithQuery(
    data: MaybeRef<number | undefined>,
    key: string,
    options: SimpleTypeOptions & { parseNumber: MaybeRef<true> }
): Ref<number | undefined>;

export function useSyncWithQuery(
    data: MaybeRef<boolean | undefined>,
    key: string,
    options: SimpleTypeOptions & { parseBoolean: MaybeRef<true> }
): Ref<boolean | undefined>;

export function useSyncWithQuery(
    data: MaybeRef<string | undefined>,
    key: string,
    options?: SimpleTypeOptions
): Ref<string | undefined>;

export function useSyncWithQuery<S extends UrlState = UrlState>(
    data: MaybeRef<S>,
    options?: MaybeRef<UseSyncWithQueryOptions>
): Ref<S>;

/**
 * 统一的 useSyncWithQuery 实现
 */
export function useSyncWithQuery<T>(
    data: MaybeRef<T>,
    keyOrOptions?: string | MaybeRef<UseSyncWithQueryOptions>,
    options: SimpleTypeOptions | UseSyncWithQueryOptions = {}
): Ref<T> {
    const router = useRouter();
    const route = useRoute();
    
    const isSimpleType = typeof keyOrOptions === 'string';
    
    // 创建响应式状态
    const state = ref(unref(data)) as Ref<T>;
    
    // Race condition fix: Use update queue to handle concurrent updates
    const updateLock = { value: false };
    let pendingUpdate: T | null = null;
    
    // 初始化状态
    const processor = createValueProcessor(isSimpleType, keyOrOptions, options, data);
    state.value = processor.parseFromUrl(route.query);
    
    // Watchers cleanup array for memory leak prevention
    const cleanupCallbacks: (() => void)[] = [];
    
    // Process pending updates after URL update completes
    const processPendingUpdate = () => {
        if (pendingUpdate !== null && !updateLock.value) {
            const nextUpdate = pendingUpdate;
            pendingUpdate = null;
            
            const newQuery = processor.serializeToUrl(nextUpdate, route.query);
            if (!isEqual(route.query, newQuery)) {
                updateLock.value = true;
                router.replace({ query: newQuery }).finally(() => {
                    updateLock.value = false;
                    // Process any updates that came in during this operation
                    processPendingUpdate();
                });
            }
        }
    };

    // 单一watch: 状态变化时更新URL (Race condition fixed with queue)
    const stateWatchStop = watch(
        state,
        (newState) => {
            // 对于对象类型，检查并恢复缺失的属性
            if (!isSimpleType) {
                const currentDefaultValue = unref(data);
                const missingKeys = Object.keys(currentDefaultValue as any).filter(
                    key => !(key in (newState as any)) || ((newState as any)[key] === undefined)
                );
                
                if (missingKeys.length > 0) {
                    // 恢复缺失的属性
                    missingKeys.forEach(key => {
                        (state.value as any)[key] = (currentDefaultValue as any)[key];
                    });
                    return; // 让下次watch处理恢复后的值
                }
            }
            
            // 对于简单类型，如果设置为undefined，恢复默认值
            if (isSimpleType && newState === undefined) {
                state.value = unref(data);
                return;
            }
            
            if (updateLock.value) {
                // If currently updating, queue this update
                pendingUpdate = newState;
                return;
            }
            
            const newQuery = processor.serializeToUrl(newState, route.query);
            if (!isEqual(route.query, newQuery)) {
                updateLock.value = true;
                router.replace({ query: newQuery }).finally(() => {
                    updateLock.value = false;
                    // Process any updates that came in during this operation
                    processPendingUpdate();
                });
            }
        },
        { deep: true, immediate: false }
    );
    
    cleanupCallbacks.push(stateWatchStop);
    
    // 单一watch: URL变化时更新状态 (Race condition fixed)
    const routeWatchStop = watch(
        () => route.query,
        (newQuery) => {
            if (updateLock.value) return;
            
            const newState = processor.parseFromUrl(newQuery);
            if (!isEqual(state.value, newState)) {
                state.value = newState;
            }
        },
        { deep: true, immediate: false }
    );
    
    cleanupCallbacks.push(routeWatchStop);
    
    // 监听默认值变化，重新同步状态
    if (typeof data === 'object' && 'value' in data) {
        const defaultValueWatchStop = watch(
            data,
            (newDefaultValue) => {
                if (!isSimpleType) {
                    // 对于对象类型，当默认值变化时，合并新的默认值到当前状态
                    const currentState = state.value as any;
                    const mergedState = { ...newDefaultValue };
                    
                    // 保留当前状态中已存在的值
                    Object.keys(currentState).forEach(key => {
                        if (currentState[key] !== undefined) {
                            mergedState[key] = currentState[key];
                        }
                    });
                    
                    state.value = mergedState;
                } else {
                    // 对于简单类型，如果当前值等于旧的默认值，更新为新的默认值
                    // 这里需要复杂的逻辑来判断是否应该更新
                }
            },
            { deep: true }
        );
        
        cleanupCallbacks.push(defaultValueWatchStop);
    }
    
    // Memory leak prevention: Cleanup watchers
    onUnmounted(() => {
        cleanupCallbacks.forEach(cleanup => cleanup());
    });
    
    return state;
}


export type UseSyncWithQuery = typeof useSyncWithQuery;