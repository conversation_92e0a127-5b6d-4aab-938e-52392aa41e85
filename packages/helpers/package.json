{"name": "@mitra-design/helpers", "version": "2.9.5", "description": "Mitra Design helpers package", "scripts": {"build": "rimraf ./es & tsc --project tsconfig.build.json"}, "types": "", "exports": {"./manually-mount": "./es/manually-mount/index.js", "./two-way": "./es/two-way/index.js", "./sync-with-query": "./es/sync-with-query/index.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"manually-mount": ["es/manually-mount/index.d.ts"], "two-way": ["es/two-way/index.d.ts"], "sync-with-query": ["es/sync-with-query/index.d.ts"]}}, "files": ["es"], "sideEffects": false, "peerDependencies": {"lodash": "4.17.21"}, "dependencies": {}}