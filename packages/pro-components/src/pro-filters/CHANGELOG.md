# ProFilters

## 2023-10-09 (fix) /刘青/ _patch_

修复预设组件 Input 无法输入的问题 [PRIVATE-73324](https://jira.qiyuesuo.me/browse/PRIVATE-73324)

## 2023-12-21 (fix) /刘青/ _patch_

修复布尔值 `false` 会被当作空值处理的问题

## 2024-01-04 (style) /刘青/ _patch_

高级筛选最多显示 6 行，超出滚动

## 2024-01-04 (feat) /刘青/

筛选器优化：[PRIV-3681](https://jira.qiyuesuo.me/browse/PRIV-3681)
- 「更多」组件增加确定按钮、增加排序功能
- 新增预设组件下拉搜索 PopoverSearch、日期范围选择器
- 高级筛选布局与样式优化
- 支持传递事件监听函数

## 2024-01-26 (fix) /刘青/ _patch_

修复传入 `fixedExtraFilterList` 可能还会使用缓存中的数据问题

## 2024-03-14 (style) /杨甜/ _patch_

- 更多面板的宽高与表格组件操作列面板统一（宽度 `220px` -> `260px`、高度 `192px` -> `256px`）
- 更多面板提示文字溢出显示 tooltip

## 2024-03-28 (feat) /刘青/ _patch_

新增 `storage-strategy` 属性用于调整缓存策略 [PRIV-5851](https://jira.qiyuesuo.me/browse/PRIV-5851)

## 2024-04-10 (feat) /刘青/

 新增 `extraFilterListIncludeDefault` 以支持将固定筛选项放入更多中参与排序 [PRIV-5370](https://jira.qiyuesuo.me/browse/PRIV-5370)

## 2024-05-10 (fix) /刘青/ _patch_

修复 IE 下部分筛选项会重叠的问题 [PRIVATE-93440](https://jira.qiyuesuo.me/browse/PRIVATE-93440)

## 2024-05-30 (feat) /刘青/ _patch_

筛选项支持 tips 说明 [PRIV-6337](https://jira.qiyuesuo.me/browse/PRIV-6337)

## 2024-07-16 (feat) /刘青/

新增全选筛选项功能 [REQ-16435](https://jira.qiyuesuo.me/browse/REQ-16435)

## 2024-08-28 (fix) /刘青/ _patch_

修复部分情况下缓存数据不对的问题 [DEV-34084](https://jira.qiyuesuo.me/browse/DEV-34084)

## 2024-12-26 (feat) /刘青/ _patch_

新增 `resetButtonProps` 属性，支持修改重置按钮的 props

## 2025-04-07 (fix) /刘青/ _patch_

修复部分场景下 `cacheKeys` 无效的问题

## 2025-04-23 (fix) /刘青/ _patch_

- 修复高级筛选在直接卸载的情况下无法显示外面的查询按钮问题 [PRIVATE-112415](https://jira.qiyuesuo.me/browse/PRIVATE-112415)
- 修复日期范围选择器缓存中没有值时，默认快捷选项未生效的问题 [PRIVATE-112442](https://jira.qiyuesuo.me/browse/PRIVATE-112442)

## 2025-04-25 (feat) /王炎/

预设选择器默认开启 `clearable` [PRIV-8311](https://jira.qiyuesuo.me/browse/PRIV-8311)
