{"name": "@mitra-design/internal", "version": "2.9.5", "description": "mitra-design 内部公共组件包", "type": "module", "scripts": {"build": "pnpm build:component && pnpm build:style && pnpm build:tsc", "build:style": "mitra-design-cli build:style", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json"}, "exports": {".": "./es/index.js", "./es/*": "./es/*", "./src/*": "./src/*", "./components/*": "./components/*", "./locale/lang": "./es/locale/lang/index.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"locale/lang": ["es/locale/lang/index.d.ts"]}}, "files": ["es"], "main": "es/index.js", "peerDependencies": {"vue": "2.7.4", "vue-class-component": "7.2.6"}, "dependencies": {"@mitra-design/shared": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/locale": "workspace:*", "@mitra-design/icons": "workspace:*", "async-validator": "4.2.5", "overlayscrollbars": "~2.4.7", "resize-observer-polyfill": "^1.5.1", "uppercamelcase": "3.0.0"}, "devDependencies": {"@mitra-design/cli": "workspace:*", "@types/uppercamelcase": "3.0.0"}}