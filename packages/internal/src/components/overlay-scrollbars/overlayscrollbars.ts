import {
    OverlayScrollbars,
    ScrollbarsHidingPlugin,
    SizeObserverPlugin
} from 'overlayscrollbars';

OverlayScrollbars.plugin(ScrollbarsHidingPlugin);
OverlayScrollbars.plugin(SizeObserverPlugin);


OverlayScrollbars.env().setDefaultOptions({
    scrollbars: {
        autoHide: 'leave',
        autoHideDelay: 500,
        visibility: 'auto',
        theme: 'os-theme-light',
    },
});

export {
    OverlayScrollbars
};
