import { isIE } from '@mitra-design/shared/utils';
import type { DirectiveBinding, ObjectDirective } from 'vue';
import { nextTick } from 'vue';

import { OverlayScrollbars } from './overlayscrollbars';

type RawOptions = ReturnType<OverlayScrollbars['options']>;
interface ScrollDirectiveOptions {
    onScroll?: (e: Event) => void;
    onReachBottom?: () => void;
    visibility?: RawOptions['scrollbars']['visibility'];
    x?: RawOptions['overflow']['x'];
    y?: RawOptions['overflow']['y'];
    theme?: string;
    /**
     * 关于滚动条偏移方案的设计：
     * 目前场景非常非常有限，暂不提供 API 和解决方案，但可以初步构思一下方案：
     *
     * 背景：一个容器有 padding 和 content，content 没自己的 padding，但又要求有滚动条，此时滚动条会遮蔽在 content 上，UI 要求移至 padding 处以避免遮蔽。
     *
     * 原则：
     * 多数情况下，不应为了滚动条 offset 而重新布局 DOM 以适应滚动条。（使用自定义滚动条已经极大降低了 DOM 的灵活性，再改 DOM 来让滚动条出现在合适的位置，本末倒置了）
     *
     * 滚动条有如下偏移方案：
     * - 1: slot API，可以指定 scrollbars 依附的容器，可以解决一部分场景。
     * - 2: 提供一系列的 class 用于控制滚动条样式，譬如 v-scroll="{ className: 'offset-right-8' }" 表示垂直方向的滚动条向左平移 8px(只是偏移的方案，并不能解决上述背景问题，因并不能向右平移)。
     *
     */
    // slot?: Initialization['scrollbars']['slot'];
}

type Nullable<T> = T | undefined | null;

const SPECIFIC_VIEWPORT_KEY = Symbol('Scroll-viewport-element');

const setupHorizontalOnlyMode = (el: HTMLElement, osInstance: OverlayScrollbars) => {
    el.addEventListener('wheel', (e) => {
        const viewport = osInstance.elements().viewport;
        // 检查是否有横向滚动条(todo: 用 scrollTop 代替 delta)
        if (e.deltaY !== 0 && viewport.scrollWidth > viewport.clientWidth) {
            viewport.scrollLeft += e.deltaY;
            // 阻止纵向滚动
            e.preventDefault();
        }
    });
};

const initializeScrollbars = async (el: HTMLElement, binding: DirectiveBinding<Nullable<ScrollDirectiveOptions>>) => {
    const options = binding.value;

    /**
     * @description v-scroll 的直接直接子元素，是否被标记为 viewport
     * 标记有： `data-overlayscrollbars-contents`、`data-scroll-viewport`
     *
     * 为什么需要两个标记？ data-overlayscrollbars-contents 有样式声明，滚动条初始化时，它会表现为 display: content，这是 overlayscrollbars 的一些重绘优化；
     * 但极少数情况下，组件会受次影响，如 file-img-viewer，因此需要一个额外的、无样式的 viewport 标记声明。
     */

    const firstEleMarkedAsViewport =
        el.firstElementChild?.hasAttribute('data-overlayscrollbars-contents')
        || el.firstElementChild?.hasAttribute('data-scroll-viewport');

    const hasViewport =
        firstEleMarkedAsViewport
        && el.firstElementChild instanceof HTMLElement;

    const viewportOptions = hasViewport
        ? {
            viewport: el.firstElementChild,
            content: el.firstElementChild,
        }
        : {};

    if (hasViewport) {
        el[SPECIFIC_VIEWPORT_KEY] = true;
    }

    const theme = options?.theme;

    // waiting until vnode patched
    await nextTick();

    const osInstance = OverlayScrollbars(
        {
            target: el,
            elements: {
                ...viewportOptions,
            },
            scrollbars: {

            },
        },
        {
            scrollbars: {
                visibility: binding.value?.visibility,
                theme,
            },
            overflow: {
                x: binding.value?.x,
                y: binding.value?.y,
            },
        },
        {
            scroll(instance, e) {
                options?.onScroll?.(e);

                const scrollElement = instance.elements().viewport;
                const scrollTop = scrollElement.scrollTop;
                const scrollHeight = scrollElement.scrollHeight;
                const clientHeight = scrollElement.clientHeight;
                const isReachBottom = (Math.ceil(scrollTop + clientHeight) === scrollHeight);
                if (isReachBottom) {
                    options?.onReachBottom?.();
                }
            },
        }

    );

    const isHorizontalOnlyMode = binding.modifiers['h-only'] || binding.modifiers['horizontal-only'];
    if (isHorizontalOnlyMode) {
        setupHorizontalOnlyMode(el, osInstance);
    }
    return osInstance;
};


const Scroll: ObjectDirective<HTMLElement, Nullable<ScrollDirectiveOptions>> = {
    async inserted(el, binding) {
        // @todo emit event
        if (isIE()) {
            el.setAttribute('data-overlayscrollbars-compatible-host', '');
            el.addEventListener('scroll', (e) => binding.value?.onScroll?.(e));

            const scrollElement = el;
            const scrollTop = scrollElement.scrollTop;
            const scrollHeight = scrollElement.scrollHeight;
            const clientHeight = scrollElement.clientHeight;
            const isReachBottom = (Math.ceil(scrollTop + clientHeight) === scrollHeight);
            if (isReachBottom) {
                binding.value?.onReachBottom?.();
            }
            return;
        }
        const osInstance = await initializeScrollbars(el, binding);

        el.setAttribute('data-overlayscrollbars-host', '');

        // el[INSTANCE_INITIALIZED_KEY] = Boolean(osInstance.elements().content.firstElementChild);

        el['$scrollbar'] = osInstance;
    },
    async update(el, binding, vnode, prevnode) {
        if (isIE()) {
            return;
        }
        if (el[SPECIFIC_VIEWPORT_KEY]) {
            return;
        }

        // 若 vnode 更新时，初始化异常，尝试重新初始化；
        /**
         * 暂时不能这样做，最好还是手动包裹元素
         * 原因：
         * - 重新初始化来回操作 dom，性能不好
         * - 重新初始化会丢失滚动条（offset）状态
         * - 难以精准控制 vnode，可能会有很多不必要的重置滚动条状态，进而导致一个列表永远无法滑动到底之类的问题；
         */

        // (el['$scrollbar'] as OverlayScrollbars).destroy();
        // const osInstance = await initializeScrollbars(el, binding);
        // el['$scrollbar'] = osInstance;

    },
};

export {
    Scroll
};
