import { ref, watch, readonly, type Ref } from 'vue';

type PopupType = 'popup';

const DEFAULT_Z_INDEX = 2000;
const Z_INDEX_STEP = 1;

class PopupManager {
    private popupStack = {
        popup: new Set<number>(),
    };

    private getNextZIndex(type: PopupType, zIndex?: number) {
        const current = zIndex ?? Array.from(this.popupStack[type]).pop() ?? DEFAULT_Z_INDEX;
        return current + Z_INDEX_STEP;
    }

    public add(type: PopupType, zIndex?: number) {
        const nextZIndex = this.getNextZIndex(type, zIndex);
        this.popupStack[type].add(nextZIndex);

        return nextZIndex;
    }

    public delete(zIndex: number, type: PopupType) {
        this.popupStack[type].delete(zIndex);
    }
}


const popupManager = new PopupManager();

export const usePopupManager = (type: PopupType, options?: { visible?: Ref<boolean | undefined> }) => {
    const { visible } = options ?? {};

    const zIndex = ref(0);

    const open = () => {
        zIndex.value = popupManager.add(type);
    };

    const close = () => {
        popupManager.delete(zIndex.value, type);
    };

    const setZIndex = (index: number) => {
        zIndex.value = popupManager.add(type, index);
    };

    watch(
        () => visible?.value,
        (v) => {
            if (v) {
                open();
            } else {
                close();
            }
        },
        {
            immediate: true,
        }
    );

    return {
        zIndex: readonly(zIndex),
        setZIndex,
        open,
        close,
    };
};
