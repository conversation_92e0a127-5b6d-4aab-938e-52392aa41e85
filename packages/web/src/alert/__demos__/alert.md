---
author: 王炎
group: 反馈
projectVersion: private-sign@4.3.7
figma: https://www.figma.com/file/jPEpku6TQlEl8MAYxiYwIs/PC-%E6%96%B0%E8%A7%86%E8%A7%89%E8%A7%84%E8%8C%83?type=design&node-id=5679-44103&mode=design&t=CObvEIGkjq8OeiLX-0
---

# Alert - 警告提示

## 何时使用

当需要向用户显示警告的信息时，通过警告提示，展现需要关注的信息。

## 基础示例
```vue
<template>
    <mt-alert>a primary alert</mt-alert>
</template>
```

## 提示类型
警告提示有 `primary`、`info`、`success`、`warning`、`error` 五种类型
```demo
alert.type.demo.vue
```

## 可关闭
```vue
<template>
    <mt-alert closable>这是一条可关闭提示</mt-alert>
</template>
```

## 提示标题
通过设置 `title` 属性或 `title` 插槽可以给警告提示添加标题。
```demo
alert.title.demo.vue
```


## 文字颜色随类型变化

设置 `colorful` 可让文字颜色随类型变化。

```vue
<template>
    <div style="display: flex; flex-direction: column; gap: 8px">
        <mt-alert colorful title="Primary">primary</mt-alert>
        <mt-alert colorful title="Info" type="info">info</mt-alert>
        <mt-alert colorful title="Success" type="success">success</mt-alert>
        <mt-alert colorful title="Warning" type="warning">warning</mt-alert>
        <mt-alert colorful title="Error" type="error">error</mt-alert>
    </div>
</template>
```

## 自定义内容
自定义内容可通过默认插槽进行设置。
```demo
alert.content.demo.vue
```

## 操作项
通过设置 `action` 属性或 `action` 插槽自定义操作项。
当使用 action 属性的时候，操作项是和 type 相对应的风格。
```demo
alert.action.demo.vue
```

## 尺寸

通过设置 `size` 属性可以设置尺寸，默认为 `medium`。可以设置为 `small`。

```vue
<template>
    <div style="display: flex; flex-direction: column; gap: 8px">
        <mt-alert size="medium" title="Primary">primary</mt-alert>
        <mt-alert size="small" title="Primary">primary</mt-alert>
    </div>
</template>
```


## 无边框模式

通过设置 `borderless` 属性可以设置无边框模式。`:show-icon="false"` 属性可以设置不显示图标。

:::tip 说明
无 icon 的 Alert title 为 `13px` 或 `12px`
:::

```vue
<template>
    <div style="display: flex; flex-direction: column; gap: 8px">
        <mt-alert borderless :show-icon="false" title="Primary">primary</mt-alert>
        <mt-alert borderless :show-icon="false" title="Info" type="info">info</mt-alert>
        <mt-alert borderless :show-icon="false" title="Success" type="success">success</mt-alert>
        <mt-alert borderless :show-icon="false" title="Warning" type="warning">warning</mt-alert>
        <mt-alert borderless :show-icon="false" title="Error" type="error">error</mt-alert>
    </div>
</template>
```

## 顶部公告
通过设置 `banner` 可将 Alert 作为顶部公告使用（去除边框和圆角）。
```vue
<template>
    <mt-alert banner>顶部公告</mt-alert>
</template>
```

## API
