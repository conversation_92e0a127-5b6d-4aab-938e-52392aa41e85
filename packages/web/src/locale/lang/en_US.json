{"DateSelect": {"custom": "custom", "startPlaceholder": "Start date", "endPlaceholder": "End date", "shortcutNONE": "whole", "confirm": "determine"}, "MessageBox": {"messageText": "This is the message content", "confirm": "determine", "cancel": "cancel"}, "GroupTree": {"selectGroup": "Please select the group you belong to", "create": "New Group", "createPlaceholder": "Please enter a group name", "confirm": "determine", "remove": "Delete Group", "cancel": "cancel", "upperGroup": "Upper level grouping", "ungrouped": "Ungrouped", "deleteWarningWhenSubgroupExisting": "After deletion, the [%{groupname}] group and its subordinate groups will be deleted, and all %{customGroupText} will be moved to %{groupParentNameDesc}. Are you sure to delete", "deleteWarningWhenSubgroupInexistent": "After deletion, all %{customGroupText} in the [%{groupname}] group will be moved to %{groupParentNameDesc}. Are you sure to delete", "deleteCurrent": "Delete only the current group", "move": "Mobile grouping", "takeGroup": "Group", "moveToFollowingGroup": "Move to the following group", "rename": "rename", "delete": "delete", "grouping": "grouping", "searchPlaceholder": "Enter group name search", "template": "Template", "selectedLabel": "Selected:", "locationTip": "Return to selected node", "treeSettingTitle": "Group settings", "optionHidingLabel": "Hide groups without data", "optionHidingTips": "After enabling the function, groups without data will be hidden (grouping sorting function is not supported when enabled)"}, "InfiniteScrollList": {"loading": "Loading", "loaded": "- All data loaded -", "noData": "There is currently no data available"}, "PopoverMenu": {"secondaryMenuText": "Content of the secondary menu panel"}, "SelectPanel": {"noData": "There is currently no data available", "searchPlaceholder": "Enter search", "clearSelection": "Clear Selected", "loading": "LOADING", "selectAll": "Select All"}, "SelectPopover": {"label": "choice", "displayText": "whole"}, "SelectReference": {"label": "choice", "displayText": "whole"}, "AsyncGroupSelect": {"clearSelection": "Clear", "searchPlaceholder": "Enter search"}, "Typography": {"copy": "Copy", "copied": "<PERSON>pied", "edit": "Edit", "ellipsisText": "Expand", "collapseText": "Collapse"}, "InputSearch": {"placeholder": "Please enter search keywords"}, "ButtonGroup": {"more": "more"}, "TagSelect": {"new": "new"}, "ColorPicker": {"default": "default", "recentlyUsedColors": "Recently used colors", "moreColors": "More colors"}, "ImageCropper": {"switchToCropMode": "Switch to crop mode", "switchToMoveMode": "Switch to move mode"}, "InputSearchDeprecate": {"placeholder": "Please enter search keywords"}, "SelectInput": {"placeholder": "Please select", "new": "new"}, "InputPassword": {"capsLockTips": "Capitalization lock turned on"}, "Modal": {"cancel": "cancel", "confirm": "sure"}, "Drawer": {"cancel": "cancel", "confirm": "sure"}, "TagGroup": {"expand": "expand", "collapse": "retract"}, "Popconfirm": {"cancel": "cancel", "confirm": "sure"}}