@import '@mitra-design/internal/src/components/overlay-scrollbars/index.less';

.mt-second-sidebar {
    *,
    *::before,
    *::after {
        box-sizing: border-box;
    }

    height: 100%;
    display: flex;
    flex-shrink: 0;
    background-color: @color-background;

    &__menu {
        width: 184px;
        height: 100%;
        flex-shrink: 0;
        padding: 20px 16px 0;
        background-color: @color-background-white;
        border-right: 1px solid @color-border;
        > ul {
            padding: 0;
        }
    }

    &__menu-item {
        box-sizing: border-box;
        height: 36px;
        padding: 0 12px;
        margin-bottom: 4px;
        font-size: 13px;
        line-height: 36px;
        color: @color-text-2;
        white-space: nowrap;
        cursor: pointer;
        display: flex;
        position: relative;

        &:hover {
            background-color: @color-background;
            border-radius: 4px;
        }

        &.is-active {
            font-weight: 600;
            color: @color-text-1;
            background-color: @color-background;
            border-radius: 4px;
        }
    }

    &__menu-label {
        flex: 1;
        .ellipsis();
    }

    &__menu-icon {
        width: 14px;
        margin-right: 8px;
        font-size: 14px;

        &.is-right {
            position: absolute;
            right: 12px;
            margin-right: 0;
        }
    }

    &__menu-svg-icon {
        fill:  @color-icon-dark !important;

        &.is-active {
            fill: @color-text-1 !important;
        }
    }

    &__menu-number {
        margin-left: auto;
        font-size: 12px;
    }

    &__container {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: @color-background-white;
        padding: 24px;
    }

    &__container-title {
        height: 24px;
        line-height: 24px;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 600;
    }

    &__container-content {
        flex: 1;
        height: calc(100% - 40px);
    }


    &.mt-second-sidebar--borderless {
        .mt-second-sidebar__menu {
            border-right: none;
        }
    }
}


.mt-second-sidebar--size-small {
    .mt-second-sidebar__menu {
        .mt-second-sidebar__menu-item {
            height: 32px;
            margin-bottom: 2px;
            line-height: 32px;
        }    
    }
    
}