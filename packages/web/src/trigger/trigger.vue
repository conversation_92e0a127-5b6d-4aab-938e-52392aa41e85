<script setup lang="tsx">
import { useFirstElement } from '@mitra-design/shared/composables';
import Vue from 'vue';

import { useTriggerActions } from './composables/use-trigger-actions';
import { triggerInjectionKey } from './provide-inject-keys';
import TriggerPopup from './trigger-popup.vue';
import type { TriggerProps } from './types/props';
import { TriggerPopupManager } from './managers/trigger-popup-manager';
import { useWatchDisabled } from './composables/use-disabled';
import { TRIGGER_OPEN_CLASS_NAME } from './constants';

const props = withDefaults(defineProps<TriggerProps>(), {
    visible: undefined,
    placement: 'top',
    trigger: 'hover',
    destroyOnHide: true,
    disabled: undefined,
    arrow: undefined,
    delay: 300,
    singleton: undefined,
    renderContainer: () => document.body,
});

const emit = defineEmits<{
    (event: 'visible-change', visible: boolean): void;
}>();


const componentName = 'trigger';

const { getFirstElement } = useFirstElement();

const slots = useSlots();

// =================================
// Delay
// =================================

const computedDelay = computed(() => {
    const defaultShowDelay = 300;
    const defaultHideDelay = 200;

    if (typeof props.delay === 'number') {
        return {
            show: props.delay ?? defaultShowDelay,
            hide: defaultHideDelay,
        };
    }

    return {
        show: props.delay?.show ?? defaultShowDelay,
        hide: props.delay?.hide ?? defaultHideDelay,
    };
});


// =================================
// Register Sub Popup
// =================================
const triggerCtx = inject(triggerInjectionKey, undefined);

const subPopupElements = new Map<string, HTMLElement>();

const registerSubPopup = (id: string, popup: HTMLElement) => {
    subPopupElements.set(id, popup);
    triggerCtx?.registerSubPopup(id, popup);
};

const unregisterSubPopup = (id: string) => {
    subPopupElements.delete(id);
    triggerCtx?.unregisterSubPopup(id);
};


// =================================
// Popup visible
// =================================
const popupVM = ref();
const referenceRef = ref<HTMLElement>();
const triggerPopupRef = ref<HTMLElement>();

let popupUid = 0;

const popupVisible = ref(props.visible);

watchEffect(() => {
    popupVisible.value = props.visible;
})

const computedVisible = computed(() => {
    if (props.disabled) {
        return false;
    }

    return props.visible ?? popupVisible.value;
});

watch(computedVisible, (val) => {
    if (val) {
        referenceRef.value?.classList.add(TRIGGER_OPEN_CLASS_NAME);
    } else {
        referenceRef.value?.classList.remove(TRIGGER_OPEN_CLASS_NAME);
    }
}, { immediate: true });

useWatchDisabled({
    disabled: toRef(props, 'disabled'),
    singleton: toRef(props, 'singleton'),
    popupVisible,
});

watch(popupVisible, (val) => {
    emit('visible-change', val);
})

const updatePopupVisible = (visible: boolean) => {
    popupVisible.value = visible;
};

const vm = getCurrentInstance();

const createPopupVM = () => {
    if (!popupVM.value && props.singleton) {
        popupVM.value = TriggerPopupManager.getPopupVM();
    }

    if (!popupVM.value && !props.disabled && vm?.proxy) {
        popupVM.value = new Vue({
            parent: vm.proxy,
            data: {
                node: null,
            },
            render() {
                return this.node;
            },
        }).$mount();
    }

    if (popupVM.value && props.singleton) {
        popupUid = TriggerPopupManager.registerPopup(popupVM.value);
    }
}

// ==================================
// 针对初始就展示的 popup 组件，需要在 onBeforeMount 中创建 popupVM
// 其他情况通过 watch 监听 popupVisible，仅在初次展示时创建 popupVM
// ==================================

watch(popupVisible, async (val) => {
    if (val && !popupVM.value) {
        createPopupVM();

        await nextTick();

        triggerPopupRef.value = popupVM.value.node?.componentInstance?.getPopupEl?.();
    }
});

onBeforeMount(() => {
    if (props.visible) {
        createPopupVM();
    }
})

onBeforeUnmount(() => {
    if (props.singleton) {
        TriggerPopupManager.unregisterPopup(popupUid);
    }

    if (popupVM.value) {
        if (!props.singleton || TriggerPopupManager.isEmpty()) {
            popupVM.value.$destroy();
        }
    }
});

defineOptions({
    componentName,
    inheritAttrs: false,
});


// =================================
// Trigger Actions
// =================================
const { handlePopupMouseEnter, handlePopupMouseLeave } = useTriggerActions({
    trigger: props.trigger,
    referenceRef,
    popupVisible,
    delay: computedDelay,
    triggerContext: triggerCtx,
    subPopupElements,
    popupRef: triggerPopupRef,
    disabled: toRef(props, 'disabled'),
    singleton: toRef(props, 'singleton'),
});


// =================================
// Provider
// =================================
provide(triggerInjectionKey, reactive({
    registerSubPopup,
    unregisterSubPopup,
    handlePopupMouseEnter,
    handlePopupMouseLeave,
}))

// =================================
// Render
// =================================
defineRender(() => {
    const element = getFirstElement();

    if (!element) return null;

    // 由于 DOM 的渲染时机比 VNode 赋值要晚，需要使用 nextTick 来确保 reference DOM 已经渲染完成
    nextTick(() => {
        referenceRef.value = element.elm as HTMLElement;
    })

    if (referenceRef.value && popupVM.value) {
        popupVM.value.node = (
            <TriggerPopup
                visible={computedVisible.value}
                arrow={props.arrow}
                theme={props.theme}
                placement={props.placement}
                offset={props.offset}
                delay={computedDelay.value}
                reference={referenceRef.value}
                trigger={props.trigger}
                singleton={props.singleton}
                destroyOnHide={props.singleton ? false : props.destroyOnHide}
                renderContainer={props.renderContainer}
                transitionName={props.transitionName}
                classes={props.classes}
                triggerContext={triggerCtx}
                subPopupElements={subPopupElements}
                onUpdateVisible={updatePopupVisible}
                scopedSlots={slots}
            />
        )
    }

    return element;
});
</script>
