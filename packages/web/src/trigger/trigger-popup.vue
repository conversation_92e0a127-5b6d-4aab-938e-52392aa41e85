<template>
    <div
        v-if="rendered"
        ref="popupWrapperRef"
    >
        <div
            ref="popupRef"
            :style="{ zIndex }"
            :class="[bem('wrapper'), visible ? TRIGGER_POPUP_OPEN_CLASS_NAME : '']"
        >
            <transition
                appear
                :name="bem('popup-shift-fade')"
                @after-leave="onAfterLeave"
            >
                <div
                    v-show="popupVisible"
                    :class="[bem('popup'), classes?.popup]"
                    :style="popupStyle"
                >
                    <div
                        v-if="arrow"
                        ref="arrowRef"
                        :class="[bem('arrow', [currentPlacement]), classes?.arrow]"
                    >
                        <trigger-arrow
                            :placement="currentPlacement"
                            :theme="theme"
                        />
                    </div>
                    <slot name="popup" />
                </div>
            </transition>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    inheritAttrs: false,
};
</script>
<script setup lang="ts">
import { usePopupManager } from '@mitra-design/internal';
import { useVModel, useTeleportElement } from '@mitra-design/shared/composables';

import { usePopup } from './composables/use-popup';
import { useSubPopup } from './composables/use-sub-popup';
import { TRIGGER_POPUP_OPEN_CLASS_NAME } from './constants';
import type { TriggerContext } from './provide-inject-keys';
import TriggerArrow from './trigger-arrow.vue';
import type { PlacementType, TriggerActionType, TriggerDelay } from './types/common';

import { useBem } from '@/composables';

/** reference to trigger/types/props.ts */
interface TriggerProps {
    /** 控制显示状态 */
    visible?: boolean;

    /** 是否显示箭头 */
    arrow?: boolean;

    /** 箭头主题 */
    theme?: 'light' | 'dark';

    /**
     * 弹出层位置
     * @type {"top" | "top-start" | "top-end" | "bottom" | "bottom-start" | "bottom-end" | "left" | "left-start" | "left-end" | "right" | "right-start" | "right-end"}
     */
    placement?: PlacementType;

    /** 触发方式 */
    trigger?: TriggerActionType;

    /** 自定义渲染容器 */
    renderContainer?: string | HTMLElement | (() => HTMLElement | string);

    /** 隐藏时销毁子元素 */
    destroyOnHide?: boolean;

    /**
     * 延迟时间，传递数值设置延迟展示时间，传递对象则可以分别设置展示和隐藏的延迟时间，单位为 ms
     * 目前只支持触发方式为 `hover` 时设置延迟，后续有需要可扩展
     */
    delay?: number | TriggerDelay;

    /** 过渡动画名称 */
    transitionName?: string;

    /** 自定义类名 */
    classes?: { popup?: string; arrow?: string };

    /** 弹出层距离触发元素的偏移量 */
    offset?: number;

    /** 是否禁用弹出层，禁用之后将不会出现弹出层 */
    disabled?: boolean;

    /** 是否单例模式，单例模式下，所有 trigger 共享同一个弹出层实例 */
    singleton?: boolean;
}

interface TriggerPopupProps extends TriggerProps {
    reference: HTMLElement;
    triggerContext?: TriggerContext;
    subPopupElements?: Map<string, HTMLElement>;
    delay: TriggerDelay;
}

const props = withDefaults(defineProps<TriggerPopupProps>(), {
    visible: undefined,
    placement: 'top',
    trigger: 'hover',
    destroyOnHide: true,
    arrow: undefined,
    singleton: undefined,
    renderContainer: () => document.body,
});

const emit = defineEmits<{
    (event: 'updateVisible', visible: boolean): void;
}>();

const componentName = 'trigger';

const { bem } = useBem(componentName);

const popupRef = ref<HTMLDivElement>();
const arrowRef = ref<HTMLDivElement>();
const popupWrapperRef = ref<HTMLDivElement>();
const rendered = ref(false);

const { attach, detach } = useTeleportElement({ to: toRef(props, 'renderContainer') });

const popupVisible = useVModel(props, 'visible', emit, 'updateVisible');

const { destroyPopup, currentPlacement } = usePopup({
    visible: readonly(popupVisible),
    reference: toRef(props, 'reference'),
    popup: popupRef,
    arrow: arrowRef,
    placement: toRef(props, 'placement'),
    singleton: toRef(props, 'singleton'),
});

const { zIndex } = usePopupManager('popup', {
    visible: readonly(popupVisible),
});

watch(() => props.visible, async (val) => {
    rendered.value = true;

    await nextTick();

    if (val && popupWrapperRef.value) {
        attach(popupWrapperRef.value);
    }

}, { immediate: true });


const onAfterLeave = () => {
    if (props.destroyOnHide) {
        detach(popupWrapperRef.value);
        rendered.value = false;
        destroyPopup();
    }
};

onBeforeUnmount(() => {
    popupVisible.value = false;
    detach(popupWrapperRef.value);
    rendered.value = false;
});

// =================================
// Register SubPopup
// =================================

useSubPopup(popupRef, props.triggerContext, toRef(props, 'reference'));

// =================================
// Style
// =================================

const popupStyle = computed(() => {
    if (typeof props.offset !== 'number') return;

    const placement = currentPlacement.value;
    let translate = '';

    if (placement.startsWith('top')) {
        translate = `translateY(-${props.offset}px)`;
    } else if (placement.startsWith('bottom')) {
        translate = `translateY(${props.offset}px)`;
    } else if (placement.startsWith('left')) {
        translate = `translateX(-${props.offset}px)`;
    } else if (placement.startsWith('right')) {
        translate = `translateX(${props.offset}px)`;
    }

    return {
        transform: translate,
    };
});


// =================================
// Expose
// =================================

defineExpose({
    getPopupEl: () => popupRef,
});
</script>
