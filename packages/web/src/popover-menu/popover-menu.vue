<template>
    <el-popover
        ref="popover"
        v-model="visiblePopper"
        transition="el-zoom-in-top"
        :popper-class="bem()"
        :placement="placement"
        :visible-arrow="false"
        v-bind="{ ...$attrs }"
        @hide="doClose"
        @show="updateStamper"
    >
        <div :class="bem('container')">
            <!-- 一级操作菜单 -->
            <ul
                v-show="!subPanelVisible"
                :class="bem('operation')"
            >
                <li
                    v-for="menu of menuList"
                    :key="menu.key"
                    :class="bem('item', [menu.status])"
                    @click="onClickMenuItem(menu)"
                >
                    <component
                        :is="menu.icon"
                        v-if="menu.icon"
                        :class="bem('item-icon')"
                    />
                    <span :class="bem('item-text')">{{ menu.label }}</span>
                </li>
            </ul>

            <!-- 二级操作面板 -->
            <div
                v-if="subPanelVisible"
                :class="bem('sub-panel')"
            >
                <div
                    v-if="subTitle"
                    :class="bem('sub-panel-header')"
                >
                    <div :class="bem('sub-panel-header-left')">
                        <icon-left
                            v-if="isShowSubBack"
                            :class="bem('sub-panel-back')"
                            @click="doBack"
                        />
                        <span :class="bem('sub-panel-title')">{{ subTitle }}</span>
                    </div>
                    <icon-close
                        v-if="isShowSubClose"
                        :class="bem('sub-panel-close')"
                        @click="doClose"
                    />
                </div>
                <div :class="bem('sub-panel-content')">
                    <slot name="subPanel"> {{ t('secondaryMenuText') }} </slot>
                </div>
            </div>
        </div>

        <template slot="reference">
            <slot name="reference">
                <icon-ellipsis />
            </slot>
        </template>
    </el-popover>
</template>
<script lang="ts">
import type { PopoverPlacement } from 'element-ui/types/popover';
import Vue from 'vue';

import type { IMenuItem } from './types';

import { I18n } from '@/locale';

const componentName = 'popover-menu';

const __popperTimeStamper__ = Vue.observable({ stamper: Date.now() });

@Bem(componentName)
@I18n(componentName)
@Component({
    componentName,
    name: componentName,
})
export default class PopoverMenu extends Vue {
    /** 菜单配置数据 */
    @Prop({ default: () => [] })
    menuList?: IMenuItem[];

    @Prop({ default: 'bottom' })
    placement?: PopoverPlacement;

    /** 初始弹窗是否显示 */
    @Prop({ type: Boolean })
    defaultVisible?: boolean;

    /** 是否只用二级面板 */
    @Prop({ type: Boolean, default: false })
    onlyUseSubPanel!: boolean;

    /** 二级面板的标题，配合 `onlyUseSubPanel` 使用，menu item 的二级面板标题可单独配置 */
    @Prop()
    subPanelTitle?: string;

    /** 是否显示二级面板，如果某个 menu item 有特殊需求，可单独配置 */
    @Prop({ type: Boolean })
    showSubPanel?: boolean;

    /** 是否显示二级菜单的返回按钮，如果某个 menu item 有特殊需求，可单独配置 */
    @Prop({ type: Boolean })
    showSubBack?: boolean;

    /** 是否显示二级菜单的关闭按钮，如果某个 menu item 有特殊需求，可单独配置 */
    @Prop({ type: Boolean })
    showSubClose?: boolean;

    /** 是否阻断菜单项携带的回调事件，如果某个 menu item 有特殊需求，可单独配置 */
    @Prop({ type: Boolean })
    isSilent?: boolean;

    /** 是否在点击菜单项后隐藏菜单 */
    @Prop({ default: true })
    isHideOnClick?: boolean;

    /** 选择事件 */
    @Emit('command')
    doEmitClick(menu: IMenuItem) {}

    @Ref('popover')
    popoverRef!: any;

    /** 是否展示二级操作面板 */
    subPanelVisible = false;

    subTitle: string | undefined = '';

    isShowSubBack: boolean | undefined = false;

    isShowSubClose: boolean | undefined = false;

    stamper = 0;

    get visiblePopper() {
        return this.stamper === __popperTimeStamper__.stamper;
    }

    set visiblePopper(value: boolean) {
        if (!value) this.stamper = 0;
    }

    updateStamper() {
        this.stamper = Date.now();
        __popperTimeStamper__.stamper = this.stamper;

        if (this.onlyUseSubPanel) {
            this.subTitle = this.subPanelTitle;
            this.triggerSubPanel();
        }
    }

    onClickMenuItem(menu: IMenuItem) {
        // 如果配置了单个 item 的属性，以配置的为准
        const showSubPanel = menu.subPanel?.visible !== undefined ? menu.subPanel.visible : this.showSubPanel;
        if (showSubPanel) {
            this.isShowSubBack = menu.subPanel?.showBack !== undefined ? menu.subPanel.showBack : this.showSubBack;
            this.isShowSubClose = menu.subPanel?.showClose !== undefined ? menu.subPanel.showClose : this.showSubClose;
            this.subTitle = menu.subPanel?.title;
            this.triggerSubPanel();
        } else {
            if ((menu.isSilent !== undefined && !menu.isSilent) || !this.isSilent) {
                menu.action?.(menu);

                if (this.isHideOnClick) {
                    this.doClose();
                }
            }
        }

        this.doEmitClick(menu);
    }

    triggerSubPanel() {
        this.subPanelVisible = !this.subPanelVisible;
        this.$nextTick(() => {
            this.popoverRef?.updatePopper?.();
        });
    }

    doClose() {
        this.visiblePopper = false;

        // ↓↓↓ 防止 popover 关闭时出现抖动
        setTimeout(() => {
            this.subPanelVisible = false;
            this.subTitle = '';
        }, 300);
        // ↑↑↑ 300ms 是 el-zoom-in-top 的动画时间
    }

    doBack() {
        this.onlyUseSubPanel ? this.doClose() : this.triggerSubPanel();
    }

    /**
     * @public
     * 关闭 popover
     */
    close() {
        this.doClose();
    }

    mounted() {
        this.isShowSubBack = this.showSubBack;
        this.isShowSubClose = this.showSubClose;
        this.defaultVisible && this.updateStamper();
    }

}
</script>

