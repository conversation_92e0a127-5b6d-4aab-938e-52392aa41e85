<template>
    <mt-popover-menu
        :menu-list="data"
    >
        <div slot="subPanel">自定义二级面板的内容</div>
        <span slot="reference">...</span>
    </mt-popover-menu>
</template>

<script lang="ts">
import type { IMenuItem } from '@/popover-menu/types';
import { Message } from 'element-ui';
import { Vue, Component } from 'vue-property-decorator';

@Component
export default class Demo extends Vue {
    data: IMenuItem[] = [
        { label: '新增分组', key: 'ADD_GROUP', action: this.doAction },
        { label: '编辑', key: 'EDIT_GROUP', action: this.doAction, subPanel: { visible: true } },
        { label: '删除', key: 'DELETE_GROUP', status: 'danger', action: this.doAction, subPanel: { visible: true, title: '二级标题', showBack: true } },
    ];

    doAction(menu: IMenuItem) {
        Message.info(`点击了『${menu.label}(${menu.key})』`);
    }
}
</script>
