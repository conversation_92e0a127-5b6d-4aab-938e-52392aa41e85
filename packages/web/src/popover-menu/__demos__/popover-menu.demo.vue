<template>
    <mt-popover-menu :menu-list="data">
        <icon-ellipsis slot="reference" />
    </mt-popover-menu>
</template>

<script lang="ts">
import { IconEllipsis, IconCheck, IconFilter, IconSet } from '@mitra-design/icons';
import type { IMenuItem } from '@/popover-menu/types';
import { Message } from 'element-ui';
import { Vue, Component } from 'vue-property-decorator';

@Component({
    components: { IconEllipsis },
})
export default class Demo extends Vue {
    data: IMenuItem[] = [
        { label: '新增分组', key: 'ADD_GROUP', action: this.doAction, icon: IconCheck },
        { label: '编辑', key: 'EDIT_GROUP', action: this.doAction, icon: IconFilter },
        { label: '删除', key: 'DELETE_GROUP', status: 'danger', action: this.doAction, icon: IconSet },
    ];

    doAction(menu: IMenuItem) {
        Message.info(`点击了『${menu.label}(${menu.key})』`);
    }
}
</script>
