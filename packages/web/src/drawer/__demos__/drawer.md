---
author: 刘青
group: 反馈
projectVersion: private-sign@5.3
---

# Drawer 抽屉

## 何时使用

用于在页面中展示一个侧边栏，通常用于展示表单、表格等信息。API 与 [Modal 组件](/web/modal) 类似。

## 基础示例

可配置基础的 Title，内容区域通过默认插槽传入。

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="标题" title-tips="标题提示" @close="onClose">
            <div>内容</div>
        </mt-drawer>
        <mt-button @click="visible = true">打开抽屉</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);

const onClose = () => {
    console.log('close');
};
</script>
```

## 自定义底部操作区

通过 `footer` 插槽插入按钮，默认包含边距，可通过 `divider` 属性配置分割线。

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="抽屉标题">
            <div>抽屉内容</div>
            <template #footer>
                <mt-button variant="light" @click="onCancel">取消</mt-button>
                <mt-button @click="onConfirm">确定</mt-button>
            </template>
        </mt-drawer>
        <mt-button @click="visible = true">打开抽屉</mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);

const onCancel = () => {
    console.log('cancel');
    visible.value = false;
};

const onConfirm = () => {
    console.log('confirm');
    visible.value = false;
};
</script>
```

## 关闭前校验逻辑

通过给 `beforeClose` 传递一个函数，即可在点击遮罩层、Esc 键、关闭按钮时执行。

你可以使用同步或异步 Promise 来控制抽屉是否关闭：

-   同步：返回 `Boolean` 类型，如果返回 `false` 则不会关闭抽屉
-   异步：返回 `Promise`，如果 `Promise` 被 reject 或返回 `false` 则不会关闭抽屉

:::warning 提示
通过外部控制抽屉的显示和隐藏，如更改 `v-model` 值、调用 `ManuallyMount` 的 `close` 方法时，不会触发 `beforeClose` 函数。
:::

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="抽屉标题" :before-close="beforeClose">
            <div>抽屉内容</div>
        </mt-drawer>
        <mt-button @click="visible = true">打开抽屉</mt-button>
    </div>
</template>

<script setup lang="ts">
import { ConfirmModal } from '@mitra-design/web';
import { ref } from 'vue';

const visible = ref(false);

const beforeClose = async () => {
    return ConfirmModal.warning({
        title: '提示',
        content: '确定要关闭抽屉吗？',
    });
};
</script>
```

## 可拖拽调整抽屉大小与本地缓存

组件默认启用可拖拽调整抽屉大小功能，`resizable` 属性默认为 `{ minSize: 300, maxSize: 900 }`。
只需通过 `cache.componentName` 属性传入缓存 key 即可。

:::tip 说明
根据 `placement` 属性的不同，`minSize` 和 `maxSize` 的含义也不同：

-   `left` 和 `right` 方向：`minSize` 表示最小宽度，`maxSize` 表示最大宽度。
-   `top` 和 `bottom` 方向：`minSize` 表示最小高度，`maxSize` 表示最大高度。

使用时请注意区分
:::

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="标题" :cache="{ componentName: 'demo-drawer' }" @resize-end="onResizeEnd">
            <div>内容</div>
        </mt-drawer>
        <mt-button @click="visible = true">打开抽屉</mt-button>
    </div>
</template>
<script setup lang="ts">
import type { DrawerResizeObject } from '@mitra-design/web';
import { ref } from 'vue';

const visible = ref(false);

const onResizeEnd = (size: DrawerResizeObject, e: MouseEvent) => {
    console.log('resize-end', size, e);
};
</script>
```

如果需要任意调整抽屉大小，可将 `resizable` 属性设置为 `true`，不会有最小和最大尺寸的限制。

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="标题" resizable>
            <div>内容</div>
        </mt-drawer>
        <mt-button @click="visible = true">打开抽屉</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
</script>
```

## 4 个方向

可以通过 `placement` 属性设置抽屉的弹出方向，支持 `left`、`right`、`top`、`bottom` 四个方向。

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="标题" :placement="placement">
            <div>内容</div>
        </mt-drawer>
        <mt-button @click="onOpen('left')">Left</mt-button>
        <mt-button @click="onOpen('right')">Right</mt-button>
        <mt-button @click="onOpen('top')">Top</mt-button>
        <mt-button @click="onOpen('bottom')">Bottom</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
const placement = ref('');

const onOpen = (newPlacement) => {
    visible.value = true;
    placement.value = newPlacement;
};
</script>
```

## 自定义内容区 padding

内容区只能通过默认插槽传入，且附带默认的 `padding`，如果某些情况下默认 `padding` 无法满足，或者想去除默认 `padding`，可以通过 `padding` 属性设置，属性值与 [CSS padding](https://developer.mozilla.org/docs/Web/CSS/padding) 一致。

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="弹窗标题" padding="0">
            <mt-alert banner>提示内容</mt-alert>
            <div style="padding: 16px 20px;">弹窗内容</div>
        </mt-drawer>
        <mt-button @click="visible = true"> 打开弹窗 </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
</script>
```

## 多层嵌套抽屉

多层嵌套的抽屉只有一个显示的遮罩，不会因为嵌套的层级增加而增加遮罩的颜色。

```vue
<template>
    <div>
        <mt-drawer v-model="visible1" title="抽屉1" :size="800">
            <div>内容</div>
            <mt-button @click="visible2 = true">打开嵌套抽屉</mt-button>

            <mt-drawer v-model="visible2" title="抽屉2" :size="600">
                <div>内容</div>
            </mt-drawer>
        </mt-drawer>
        <mt-button @click="visible1 = true">打开抽屉</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const visible1 = ref(false);
const visible2 = ref(false);
</script>
```

## Tabs

当标题下方需要放置 tabs 时，可以使用`tabs`插槽自行插入。

:::info
由于当前没有自构建的 Tabs 组件，现在需要先在插槽中放置 ElTabs 使用。

在这种模式下，使用 ElTabs 时只能使用其插入头部，即内容区域渲染需要自己根据当前激活 tabs 自行放入 drawer 的 default 插槽。

为方便业务使用，在 tabs 插槽中插入的 el-tabs 的头部样式会被覆盖以与设计图对其，外部不需要再调整样式。

在自构建 Tabs 组件后，将会提供更方便的接入方式。
:::

```vue
<template>
    <div>
        <mt-drawer v-model="visible" title="弹窗标题">
            <template #tabs>
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="Tab1" name="a" />
                    <el-tab-pane label="Tab2" name="b" />
                </el-tabs>
            </template>
            <div>
                <div v-if="activeTab === 'a'">Tab1 内容</div>
                <div v-if="activeTab === 'b'">Tab2 内容</div>
            </div>
        </mt-drawer>
        <mt-button @click="visible = true"> 打开弹窗 </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);

const activeTab = ref('a');
</script>
```

## 业务场景示例

在实际使用中，一般会内嵌 Form 表单、Description 描述列表、Table 表格等组件。下面结合这些组件，展示一些业务场景示例。

```vue
<template>
    <div>
        <mt-drawer v-model="visibleDesc" title="抽屉" :size="600">
            <mt-descriptions v-bind="{ items, data }" />
            <mt-button variant="filled-outline" @click="visibleForm = true">编辑</mt-button>

            <mt-drawer v-model="visibleForm" title="编辑" :size="600">
                <mt-form ref="formRef" v-model="formData" :options="formOptions" />
                <template #footer>
                    <mt-button variant="light" @click="onCancel">取消</mt-button>
                    <mt-button @click="onConfirm">确定</mt-button>
                </template>
            </mt-drawer>
        </mt-drawer>
        <mt-button @click="visibleDesc = true">打开抽屉</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';

const visibleDesc = ref(false);
const visibleForm = ref(false);

const addressList = [
    {
        label: '北京',
        value: '1',
        children: [
            {
                label: '朝阳区',
                value: '1-1',
            },
            {
                label: '海淀区',
                value: '1-2',
            },
            {
                label: '昌平区',
                value: '1-3',
            },
        ],
    },
    {
        label: '上海',
        value: '2',
        children: [
            {
                label: '闵行区',
                value: '2-1',
            },
            {
                label: '徐汇区',
                value: '2-2',
            },
            {
                label: '静安区',
                value: '2-3',
            },
        ],
    },
    {
        label: '杭州',
        value: '3',
        children: [
            {
                label: '西湖区',
                value: '3-1',
            },
            {
                label: '拱墅区',
                value: '3-2',
            },
            {
                label: '下城区',
                value: '3-3',
            },
        ],
    },
];

class Data {
    company = '某公司';

    admin = '某人';

    address = '某地';

    phone = '*********';
}

const items = ref({
    company: { label: '公司', type: 'text' },
    admin: { label: '管理员', type: 'text' },
    address: {
        label: '地址',
        type: 'text',
        text: (value) => {
            let label = '';
            if (value) {
                const city = addressList.find((item) => value.startsWith(item.value));
                if (city) {
                    label = city.label;
                }
                const district = city?.children?.find((item) => value === item.value);
                if (district) {
                    label = `${label}·${district.label}`;
                }
            }
            return `中国${label ? '·' : ''}${label}`;
        },
    },
    phone: { label: '电话', type: 'text' },
});

const data = ref(new Data());
const formData = ref({ ...data.value });

const formOptions = ref([
    {
        name: 'company',
        label: '公司',
        component: 'Input',
        componentProps: {
            placeholder: '请输入',
        },
        rules: [{ required: true, message: '请输入内容' }],
    },
    {
        name: 'admin',
        label: '管理员',
        component: 'Input',
        componentProps: {
            placeholder: '请输入',
        },
    },
    {
        name: 'address',
        label: '地址',
        component: 'Cascader',
        componentProps: {
            placeholder: '请选择',
            options: addressList,
        },
    },
    {
        name: 'phone',
        label: '电话',
        component: 'Input',
        componentProps: {
            placeholder: '请输入',
        },
    },
]);

const onCancel = () => {
    console.log('cancel');
    formData.value = { ...data.value };
    visibleForm.value = false;
};

const onConfirm = () => {
    console.log('confirm');
    data.value = { ...formData.value };
    visibleForm.value = false;
};
</script>
```

## 可视化插槽

```vue no-shadow-dom
<template>
    <doc-visual-slots title="抽屉标题" visible inline :size="560" :render-container="null" :visual-slots="$slots" />
</template>
```

## API

%INTERFACE(DrawerProps, types/props.ts)
%INTERFACE(DrawerResizeBoxProps, types/common.ts)
