<template>
    <div
        v-if="withMainArea() || withTabsArea()"
        :class="bem('', { 'with-divider': divider })"
    >
        <slot name="header">
            <div
                v-if="withMainArea()"
                :class="bem('main')"
            >
                <slot name="title">
                    <div :class="bem('title-wrapper')">
                        <!-- Title -->
                        <mt-auto-ellipsis
                            v-if="title"
                            :content="title"
                        >
                            <mt-title
                                ref="titleWrapperRef"
                                :heading="3"
                                :class="bem('title')"
                            >
                                {{ title }}
                            </mt-title>
                        </mt-auto-ellipsis>
                        <mt-tips
                            v-if="titleTips"
                            v-bind="tipsProps"
                        />
                        <slot name="title-end" />
                    </div>
                </slot>

                <div
                    v-if="slots['operation'] || closable"
                    :class="bem('operation-wrapper')"
                >
                    <slot name="operation" />
                    <icon-close
                        v-if="closable"
                        :class="bem('close-icon')"
                        :size="20"
                        @click="onClickClose"
                    />
                </div>
            </div>
        </slot>
        <div
            v-if="withTabsArea()"
            :class="bem('tabs')"
        >
            <slot name="tabs" />
        </div>
    </div>
</template>
<script setup lang="ts">
import { IconClose } from '@mitra-design/icons';

import MtAutoEllipsis from '@/auto-ellipsis/auto-ellipsis.vue';
import { useBem } from '@/composables';
import MtTips from '@/tips/tips.vue';
import type { TipsProps } from '@/tips/types';
import MtTitle from '@/typography/typography-title.vue';


const props = defineProps<{
    title?: string;
    titleTips?: string | TipsProps;
    closable?: boolean;
    divider?: boolean;
}>();

const emit = defineEmits<{
    (event: 'close'): void;
}
>();

const componentName = 'drawer-header';

const { bem } = useBem(componentName);

const slots = useSlots();

const withMainArea = () => props.title ?? props.titleTips ?? slots.title ?? slots['title-end'] ?? props.closable;
const withTabsArea = () => slots.tabs;

const tipsProps = computed(() => {
    if (typeof props.titleTips === 'string') {
        return {
            content: props.titleTips,
        };
    }

    return props.titleTips;
});

const onClickClose = () => {
    emit('close');
};
</script>
