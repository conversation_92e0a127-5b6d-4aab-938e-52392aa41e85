<template>
    <div
        v-if="innerVisible || mounted"
        ref="wrapperRef"
    >
        <drawer-mask
            v-if="showMask"
            :visible="computedVisible"
            :z-index="zIndex"
            :style="{ ...computedTopStyle, ...inlineStyle }"
            @click-mask="onClickMask"
        />
        <div
            v-if="mounted"
            v-show="innerVisible"
            :class="bem()"
            :style="{ zIndex, ...computedTopStyle, ...inlineStyle }"
            @mousedown="onClickMask"
        >
            <transition
                :name="bem('fade')"
                appear
                @after-enter="onAfterEnter"
                @after-leave="onFadeAfterLeave"
            >
                <mt-resize-box
                    v-show="computedVisible"
                    ref="resizeBoxRef"
                    :direction="computedDirection"
                    :class="bem('container', [props.placement])"
                    :style="drawerStyle"
                    v-bind="resizableProps"
                    @mousedown.stop
                    @resize-start="onResizeStart"
                    @resize="onResize"
                    @resize-end="onResizeEnd"
                >
                    <slot />
                </mt-resize-box>
            </transition>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    inheritAttrs: false,
    model: {
        prop: 'visible',
        event: 'update:drawer-wrapper-visible',
    },
};
</script>
<script setup lang="ts">
import { useVModel, useTeleportElement, useAutoLockScroll } from '@mitra-design/shared/composables';
import { mitraDesignGlobalConfig } from '@mitra-design/shared/global-configs';
import { useDesignToken } from '@mitra-design/theme';
import { computed, ref } from 'vue';
import type { CSSProperties } from 'vue/types/jsx';

import { DrawerManager } from '../managers/drawer-manager';
import type { DrawerPlacement, DrawerResizeBoxProps, DrawerResizeObject } from '../types/common';

import DrawerMask from './drawer-mask.vue';

import { useBem } from '@/composables';
import MtResizeBox from '@/resize-box/resize-box.vue';
import type { ResizeBoxDirection } from '@/resize-box/types/common';

const props = withDefaults(defineProps<{
    visible?: boolean;
    showMask?: boolean;
    zIndex?: number;
    size?: number | string;
    destroyOnClose?: boolean;
    renderContainer?: null | string | HTMLElement | (() => HTMLElement | string);
    placement?: DrawerPlacement;
    resizable?: boolean | DrawerResizeBoxProps;
    top?: number | string;
    border?: boolean;
    inline?: boolean;
    cache?: { componentName: string };
}>(), {
    destroyOnClose: undefined,
    renderContainer: 'body',
    placement: 'right',
    resizable: undefined,
    border: undefined,
    inline: undefined,
});

const emit = defineEmits<{
    (event: 'update:drawer-wrapper-visible', value: boolean): void;
    (event: 'opened'): void;
    (event: 'click-mask', e: MouseEvent): void;
    (event: 'resize-start', e: MouseEvent): void;
    (event: 'resize', size: DrawerResizeObject, e: MouseEvent): void;
    (event: 'resize-end', size: DrawerResizeObject, e: MouseEvent): void;
}>();

const { designTokens } = useDesignToken();

const componentName = 'drawer-wrapper';

const { bem } = useBem(componentName);

const wrapperRef = ref<HTMLDivElement>();

// ===============================================
// Visible
// ===============================================
const modelVisible = useVModel(props, 'visible', emit, 'update:drawer-wrapper-visible');
const innerVisible = ref(false);

watchEffect(() => {
    if (props.visible) {
        innerVisible.value = true;
    }
});

const computedVisible = computed(() => {
    return modelVisible.value ?? innerVisible.value;
});

const renderContainer = computed<HTMLElement | null>(() => {
    const to = typeof props.renderContainer === 'function' ? props.renderContainer() : props.renderContainer;

    if (typeof to === 'string') {
        return document.querySelector(to) ?? document.body;
    }

    return to;
});

const { mounted, attach, detach } = useTeleportElement({ to: renderContainer });

if (props.showMask) {
    useAutoLockScroll(computedVisible, renderContainer);
}

watch(computedVisible, async (val) => {
    await nextTick();

    if (val && wrapperRef.value) {
        attach(wrapperRef.value);
    }

    if (val) {
        DrawerManager.add(wrapperRef.value!);
    } else {
        DrawerManager.remove(wrapperRef.value!);
    }

}, { immediate: true, flush: 'post' });


// ===============================================
// Drawer
// ===============================================
const computedTop = computed(() => {
    const top = props.top ?? mitraDesignGlobalConfig.webDrawer.top;

    return typeof top === 'number' ? `${top}px` : top;
});

const computedTopStyle = computed<CSSProperties>(() => {
    if (!computedTop.value) return {};

    return {
        top: computedTop.value,
        height: `calc(100% - ${computedTop.value})`,
    };
});

const inlineStyle = computed<CSSProperties>(() => {
    if (!props.inline) return {};

    return {
        position: 'absolute',
    };
});

const drawerStyle = computed<CSSProperties>(() => {
    if (!props.size && !props.cache?.componentName) {
        return {};
    }

    let sizeValue = props.size;

    if (props.cache?.componentName) {
        const drawerCache = JSON.parse(localStorage.getItem('MT_DRAWER') ?? '{}');

        const cacheValue = drawerCache[props.cache.componentName];

        if (cacheValue) {
            sizeValue = cacheValue.size;
        }
    }

    const size = typeof sizeValue === 'number' ? `${sizeValue}px` : sizeValue;

    return {
        width: props.placement === 'left' || props.placement === 'right' ? size : '100%',
        height: props.placement === 'top' || props.placement === 'bottom' ? size : '100%',
        borderTop: (computedTop.value && (props.border as boolean | undefined) !== false) ? `1px solid ${designTokens.colorBorder}` : undefined,
    };
});

const onAfterEnter = () => {
    emit('opened');
};

const onFadeAfterLeave = () => {
    innerVisible.value = false;

    // 动画结束，如果需要销毁，则移除 dom
    if (props.destroyOnClose) {
        detach(wrapperRef.value);
    }
};

const onClickMask = (e: MouseEvent) => {
    emit('click-mask', e);
};


// =================================
// Resize
// =================================
const resizableProps = computed(() => {
    if (!props.resizable || typeof props.resizable === 'boolean') {
        return {};
    }

    return {
        minWidth: props.resizable.minSize,
        maxWidth: props.resizable.maxSize,
        minHeight: props.resizable.minSize,
        maxHeight: props.resizable.maxSize,
    };
});

const computedDirection = computed(() => {
    if (!props.resizable) {
        return undefined;
    }

    switch (props.placement) {
        case 'left':
            return 'right';
        case 'right':
            return 'left';
        case 'top':
            return 'bottom';
        case 'bottom':
            return 'top';
        default:
            return undefined;
    }
});

const onResizeStart = (e: MouseEvent) => {
    emit('resize-start', e);
};

const onResize = (size: number, direction: ResizeBoxDirection, e: MouseEvent) => {
    const sizeObj: DrawerResizeObject = {
        key: props.placement === 'left' || props.placement === 'right' ? 'width' : 'height',
        value: size,
        direction,
    };

    emit('resize', sizeObj, e);
};

const onResizeEnd = (size: number, direction: ResizeBoxDirection, e: MouseEvent) => {
    const sizeObj: DrawerResizeObject = {
        key: props.placement === 'left' || props.placement === 'right' ? 'width' : 'height',
        value: size,
        direction,
    };

    if (props.cache?.componentName) {
        const drawerCache = JSON.parse(localStorage.getItem('MT_DRAWER') ?? '{}');

        drawerCache[props.cache.componentName] = {
            ...drawerCache[props.cache.componentName],
            size,
        };

        localStorage.setItem('MT_DRAWER', JSON.stringify(drawerCache));
    }

    emit('resize-end', sizeObj, e);
};


// =================================
// Unmount
// 处理一些非正常关闭时的逻辑
// =================================
onBeforeUnmount(() => {
    if (wrapperRef.value) {
        DrawerManager.remove(wrapperRef.value);
        detach(wrapperRef.value);
    }
});


// =================================
// Expose
// =================================
defineExpose({
    getDrawerWrapperEl: () => wrapperRef.value,
});
</script>
