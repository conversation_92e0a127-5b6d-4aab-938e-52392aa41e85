import type { Ref } from 'vue';
import { inject, nextTick, provide, watch } from 'vue';

import { drawerInjectionKey } from '../provide-inject-keys';

let drawerUid = 0;

export const useSubDrawer = (visible: Ref<boolean | undefined>, drawerWrapperRef: Ref) => {
    const subDrawers = new Map<string, HTMLElement>();
    const nextId = `drawer-id__${++drawerUid}`;

    const drawerContext = inject(drawerInjectionKey, undefined);

    const registerSubDrawer = (id: string, drawer: HTMLElement) => {
        subDrawers.set(id, drawer);
    };

    const unregisterSubDrawer = (id: string) => {
        subDrawers.delete(id);
    };

    watch(
        visible,
        async (val, _, onCleanup) => {
            await nextTick();

            if (val) {
                const drawerWrapperEl = drawerWrapperRef.value?.getDrawerWrapperEl?.();
                drawerContext?.registerSubDrawer(nextId, drawerWrapperEl);
            } else {
                drawerContext?.unregisterSubDrawer(nextId);
            }

            onCleanup(() => {
                drawerContext?.unregisterSubDrawer(nextId);
            });
        },
        { immediate: true }
    );

    provide(drawerInjectionKey, {
        registerSubDrawer,
        unregisterSubDrawer,
    });

    return {
        subDrawers,
    };
};
