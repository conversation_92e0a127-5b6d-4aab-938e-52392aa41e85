<template>
    <drawer-wrapper
        ref="drawerWrapperRef"
        v-model="modelValue"
        v-bind="{
            showMask,
            zIndex,
            size,
            placement,
            destroyOnClose,
            renderContainer,
            resizable,
            top,
            border,
            inline,
            cache,
        }"
        @opened="onOpened"
        @click-mask="onClickMask"
        @resize-start="onResizeStart"
        @resize="onResize"
        @resize-end="onResizeEnd"
    >
        <drawer-panel
            v-bind="{
                title,
                titleTips,
                divider,
                closable,
                padding,
            }"
            @close="onClose"
        >
            <template
                v-for="key of Object.keys($scopedSlots)"
                #[key]
            >
                <!--
                    @slot 透传 slot
                    @member default | 内容区（包含 padding） | `-`
                    @member header | 自定义头部区域 | `-`
                    @member tabs | 自定义标签页区域 | `-`
                    @member title | 自定义标题区域 | `-`
                    @member title-end | 标题尾部区域 | `-`
                    @member operation | 自定义关闭按钮前面的操作按钮区域 | `-`
                    @member footer | 自定义底部内容区域（包含 padding） | `-`
                -->
                <slot :name="key" />
            </template>
        </drawer-panel>
    </drawer-wrapper>
</template>
<script setup lang="ts">
import { usePopupManager } from '@mitra-design/internal';
import { useVModel } from '@mitra-design/shared/composables';

import DrawerPanel from './components/drawer-panel.vue';
import DrawerWrapper from './components/drawer-wrapper.vue';
import { useSubDrawer } from './composables/use-sub-drawer';
import { DrawerManager } from './managers/drawer-manager';
import type { DrawerProps } from './types/props';

import type { DrawerResizeObject } from '@/drawer/types/common';
import { TRIGGER_OPEN_CLASS_NAME, TRIGGER_POPUP_OPEN_CLASS_NAME } from '@/trigger/constants';

const props = withDefaults(defineProps<DrawerProps>(), {
    showMask: true,
    closable: true,
    size: 640,
    escClose: true,
    destroyOnClose: true,
    clickMaskClose: true,
    resizable: () => ({ minSize: 480, maxSize: 960 }),
    divider: true,
    border: undefined,
    placement: 'right',
    inline: undefined,
    renderContainer: () => document.body,
});
const emit = defineEmits<{
    (event: 'update:drawer-visible', value: boolean): void;
    /** 点击遮罩层、Esc 键、关闭按钮时触发 */
    (event: 'close'): void;
    /** 弹窗打开动画完成后触发，如果你要在弹窗打开时获取其中的 DOM 元素，需要借助此事件 */
    (event: 'opened'): void;
    /** 抽屉关闭动画完成后触发 */
    (event: 'closed'): void;
    /** 抽屉开始拖拽时触发 */
    (event: 'resize-start', e: MouseEvent): void;
    /** 拖拽抽屉时触发, 参数为当前抽屉的 size 对象，包含 key、value、direction */
    (event: 'resize', size: DrawerResizeObject, e: MouseEvent): void;
    /** 拖拽抽屉结束后触发, 参数为当前抽屉的 size 对象，包含 key、value、direction */
    (event: 'resize-end', size: DrawerResizeObject, e: MouseEvent): void;
}>();

const modelValue = useVModel(props, 'visible', emit, 'update:drawer-visible');

defineOptions({
    inheritAttrs: false,
    model: {
        prop: 'visible',
        event: 'update:drawer-visible',
    },
});

const { zIndex } = usePopupManager('popup', {
    visible: readonly(modelValue),
});

const onOpened = () => {
    emit('opened');
};

const onClose = () => {
    emit('close');
    close();
};

const close = async () => {
    if (modelValue.value) {
        if (typeof props.beforeClose === 'function') {
            let ret = true;

            try {
                ret = (await props.beforeClose()) ?? true;
            } catch (error) {
                console.error('[mitra-design log] Drawer beforeClose error: ', error);
                ret = false;
            }

            if (!ret) return;
        }

        modelValue.value = false;

        return new Promise((resolve) => {
            setTimeout(() => {
                emit('closed');
                resolve(true);
            }, 310);
        });
    }
};



const onResizeStart = (e: MouseEvent) => {
    emit('resize-start', e);
};

const onResize = (size: DrawerResizeObject, e: MouseEvent) => {
    emit('resize', size, e);
};

const onResizeEnd = (size: DrawerResizeObject, e: MouseEvent) => {
    emit('resize-end', size, e);
};


// ================================================
// Sub Drawer
// ================================================
const drawerWrapperRef = ref<InstanceType<typeof DrawerWrapper>>();

const { subDrawers } = useSubDrawer(modelValue, drawerWrapperRef);


// ================================================
// Click Mask Close
// ================================================
const onClickMask = (e: MouseEvent) => {
    if (props.clickMaskClose) {
        emit('close');
        close();
    }
};


// ================================================
// Esc Close / Window Close
// ================================================

let isKeydownEventRegistered = false;

const onKeyDown = (e: KeyboardEvent) => {
    // 仅支持 ESC 关闭最后一层弹窗
    if (e.key === 'Escape' && DrawerManager.isLastDrawer(drawerWrapperRef.value?.getDrawerWrapperEl?.())) {
        emit('close');
        close();
    }
};

const addKeydownEvent = () => {
    if (props.escClose && !isKeydownEventRegistered) {
        document.documentElement.addEventListener('keydown', onKeyDown);
        isKeydownEventRegistered = true;
    }
};

const removeKeydownEvent = () => {
    if (props.escClose && isKeydownEventRegistered) {
        document.documentElement.removeEventListener('keydown', onKeyDown);
        isKeydownEventRegistered = false;
    }
};

const onWindowClick = (e: Event) => {
    const eventTarget = (e.composedPath ? e.composedPath()[0] : e.target) as HTMLElement;
    const drawerWrapperEl = drawerWrapperRef.value?.getDrawerWrapperEl?.();

    // 1. 点击在当前抽屉中，不关闭当前抽屉
    if (drawerWrapperEl?.contains(eventTarget)) {
        return;
    }

    // 2. 点击在嵌套抽屉中，不关闭当前抽屉
    for (const drawer of subDrawers.values()) {
        if (drawer.contains(eventTarget)) {
            return;
        }
    }

    // 3. 点击在打开的 trigger-popup 元素上，且当前 trigger-popup 的 reference 元素在 Drawer 中，不关闭
    const triggerPopupEls = document.documentElement.querySelectorAll(`.${TRIGGER_POPUP_OPEN_CLASS_NAME}`);

    for (const triggerPopupEl of triggerPopupEls) {
        // trigger-popup DOM 元素中记录了 reference 的信息，相关代码在 trigger-popup 组件中
        // @ts-ignore
        const triggerReferenceEl = triggerPopupEl.triggerReferenceEl as HTMLElement | undefined;

        if (triggerPopupEl.contains(eventTarget)
            && triggerReferenceEl
            && drawerWrapperEl?.contains(triggerReferenceEl)
            && triggerReferenceEl.classList.contains(TRIGGER_OPEN_CLASS_NAME)
        ) {
            return;
        }
    }

    close();
};

const addWindowEvent = () => {
    if (props.showMask) return;

    window.addEventListener('mousedown', onWindowClick, true);
};

const removeWindowEvent = () => {
    window.removeEventListener('mousedown', onWindowClick, true);
};


watch(modelValue, (v) => {
    if (v) {
        addKeydownEvent();
        // addWindowEvent();
    } else {
        removeKeydownEvent();
        // removeWindowEvent();
    }
}, { immediate: true });

onBeforeUnmount(() => {
    removeKeydownEvent();
    // removeWindowEvent();
});


// ================================================
// Expose
// ================================================
defineExpose({
    close,
});

</script>
