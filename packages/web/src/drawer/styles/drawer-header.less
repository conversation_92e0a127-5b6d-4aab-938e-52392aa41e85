.mt-drawer-header {
    .mt-drawer-header__main {
        display: flex;
        justify-content: space-between;
        padding: 16px 20px 12px;
        width: 100%;
        gap: 16px;
    }


    &>* {
        box-sizing: border-box;
    }

    &--with-divider {
        border-bottom: 1px solid @color-border-light;
    }

    &__title-wrapper {
        display: flex;
        align-items: center;
        flex: 1 1 auto;
        min-width: 0;
    }

    &__operation-wrapper {
        display: flex;
        align-items: center;
        height: 32px;
        gap: 12px;
    }

    &__close-icon {
        color: @color-icon;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
            color: @color-icon-dark;
        }
    }

    .mt-drawer-header__tabs {
        .el-tabs {

            .el-tabs__header {
                margin-bottom: 0;

                .el-tabs__nav-wrap {
                    padding: 0 20px;
                }

                .el-tabs__item {
                    height: 46px;
                    line-height: 46px;
                    padding: 0 12px;

                    &:nth-child(2) {
                        padding-left: 0;
                    }

                }
            }

            &::after {
                content: unset;
            }
        }
    }
}
