<template>
    <div
        ref="refMain"
        v-scroll="{ onReachBottom: pageDown }"
        :class="bem('', { 'is-blank': isBlank })"
        v-on="$listeners"
    >
        <div
            ref="refContainer"
            :class="bem('container')"
        >
            <div :class="bem('wrapper')">
                <div
                    v-for="(item, index) of list"
                    :key="`key_${index}`"
                    :class="bem('item')"
                >
                    <slot
                        name="item"
                        :data="item"
                        :index="index"
                    />
                </div>
            </div>
            <div
                v-if="loading"
                key="loading"
                v-loading="{ active: loading, size: 'mini', text: t('loading'), textPlacement: 'right', background: 'white' }"
                :class="bem('', 'loading')"
            />
            <div
                v-if="isReachEnd && !loading && list.length && finishText"
                key="end"
                :class="bem('reach-end')"
            >
                {{ finishText }}
            </div>
            <template v-if="isBlank">
                <slot name="blank">
                    <div :class="bem('blank-text')">
                        {{ blankText }}
                    </div>
                </slot>
            </template>
        </div>
    </div>
</template>
<script lang="ts">
import Vue from 'vue';

import { vLoading } from '@/loading';
import { I18n } from '@/locale';
import { vScroll } from '@/scroll';

const componentName = 'infinite-scroll-list';

@Bem(componentName)
@I18n(componentName)
@Component({
    componentName,
    name: componentName,
    directives: {
        loading: vLoading,
        scroll: vScroll,
    },
})
export default class InfiniteScrollList extends Vue {
    /** 获取数据的方法 */
    @Prop({
        required: true,
        default: () => {
            return () => Promise.resolve([1, 2, 3, 4]);
        },
    })
    getData!: (pageNow: number, pageSize: number) => Promise<any[]>;

    /** 每页条数 */
    @Prop({ default: 10 })
    pageSize!: number;

    /** 空白占位文字 */
    @Prop({ default: function (this: Vue) { return this.t('noData'); } })
    blankText!: string;

    @Prop({ default: function (this: Vue) { return this.t('loaded'); }  })
    finishText!: string;

    /** 当数据更新时触发 */
    @Emit('update')
    emitUpdate(list: any[]) { }

    @Ref('refMain')
    refMain!: HTMLDivElement;

    @Ref('refContainer')
    refContainer!: HTMLDivElement;

    list: any[] = [];

    pageNow = 1;

    loading = false;

    isReachEnd = true;

    get isBlank() {
        return !this.loading && !this.list.length;
    }

    /** 此方法可被外部调用，用于重置当前页码、清空列表并重新获取数据 */
    reset() {
        this.pageNow = 1;
        this.list = [];

        this.$nextTick(() => {
            this.callGetData(this.pageNow, this.pageSize, true);
        });
    }

    pageDown() {
        const { loading, isReachEnd, pageSize } = this;
        if (!loading && !isReachEnd) {
            this.callGetData(this.pageNow += 1, pageSize);
        }
    }

    callGetData(pageNow: number, pageSize: number, timeout = false) {
        this.loading = true;

        // 为区分是否需要延迟，抽离实际调用的过程
        const callFunction = () => {
            this.getData(pageNow, pageSize)
                .then(([list, count]) => {
                    this.list.push(...list);

                    // 如果不传 count，使用 getData 返回的数据长度与 pageSize 进行比较来判断是否还有下一页数据
                    if (count === undefined) {
                        this.isReachEnd = list.length < this.pageSize;
                    } else {
                        this.isReachEnd = count <= this.pageNow * this.pageSize;
                    }

                })
                .catch()
                .finally(() => {
                    this.loading = false;
                    this.emitUpdate(this.list);
                });
        };

        // 对于是否需要延迟分情况处理
        // (延迟一般是为了重置滚动条位置，由于设置了一些动画，需要等待离开动画结束后，才能准确还原滚动条位置到顶部)
        if (!timeout) callFunction();
        else {
            setTimeout(() => {
                callFunction();
            }, 400);
        }
    }

    mounted() {
        this.reset();
        this.addResizeObserver();
    }

    beforeDestroy() {
        this.ro?.disconnect();
    }

    ro: ResizeObserver | null = null;

    refMainLastHeight = 0;

    addResizeObserver() {
        const resizeObserver = new ResizeObserver((entries) => {
            const target = entries[0].target as HTMLElement;
            const currentHeight = target.clientHeight;
            if (
                currentHeight && !this.refMainLastHeight
                && currentHeight <= this.refMain.clientHeight
                && !this.isReachEnd
            ) {
                this.pageDown();
            }

            currentHeight && (this.refMainLastHeight = currentHeight);
        });
        resizeObserver.observe(this.refMain);
    }

    @Watch('loading')
    async watcherLoading(newVal: boolean, oldVal: boolean) {
        if (!newVal && oldVal) {
            await this.$nextTick();
            if (
                this.refMain.clientHeight && this.refMain.clientHeight >= this.refContainer.clientHeight
                && !this.isReachEnd
            ) {
                this.pageDown();
            }
        }
    }
}
</script>
