import { install } from './install';


// $$COMPONENTS_EXPORT_START$$
export * from './alert';
export * from './anchor';
export * from './async-group-select';
export * from './auto-ellipsis';
export * from './breadcrumb';
export * from './button-group';
export * from './button';
export * from './card';
export * from './cascader';
export * from './checkbox';
export * from './color-picker';
export * from './date-select';
export * from './descriptions';
export * from './draggable-layout';
export * from './drawer';
export * from './dropdown';
export * from './form';
export * from './grid';
export * from './group-tree';
export * from './image-cropper';
export * from './infinite-scroll-list';
export * from './input-combined';
export * from './input-number';
export * from './input-password';
export * from './input-search';
export * from './input-tag';
export * from './input';
export * from './loading';
export * from './menu';
export * from './message-box';
export * from './modal';
export * from './popconfirm';
export * from './popover-menu';
export * from './popover';
export * from './radio';
export * from './resize-box';
export * from './result';
export * from './scened-empty';
export * from './scroll';
export * from './second-sidebar';
export * from './segmented';
export * from './select-input';
export * from './select-panel';
export * from './select-popover';
export * from './select';
export * from './skeleton';
export * from './smooth-scrollbar';
export * from './steps';
export * from './svg-icon';
export * from './switch';
export * from './tag-group';
export * from './tag-select';
export * from './tag';
export * from './tips';
export * from './tooltip';
export * from './trigger';
export * from './typography';
export * from './virtual-list';
export * from './virtual-tree';
// $$COMPONENTS_EXPORT_END$$


// for common module
export const MitraDesign = { install };

export { version } from '@mitra-design/shared/utils';
export { mitraDesignGlobalConfig, setMitraDesignGlobalConfig } from '@mitra-design/shared/global-configs';

export default MitraDesign;

export { __MitraDesignInner__ } from './install';

