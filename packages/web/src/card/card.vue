<template>
    <div
        :class="bem('', [size, {
            bordered,
            'with-header': withHeader(),
            'with-divider': divider && withHeader(),
            'with-description': (description || slots.description) && withHeader(),
            'hover-border': hover?.border,
            'hover-shadow': hover?.shadow,
        }])"
    >
        <div
            v-if="showHeader"
            :class="bem('header-wrapper')"
        >
            <!-- @slot 完全自定义头部 -->
            <slot name="header">
                <!-- Header -->
                <div
                    v-if="needShowHeader()"
                    :class="bem('header')"
                >
                    <div :class="bem('header-title')">
                        <!-- @slot 自定义整个 title 区域 -->
                        <slot name="title">
                            <!-- Title -->
                            <div :class="bem('title-wrapper')">
                                <!-- @slot 自定义 title 左侧区域 -->
                                <slot name="title-start" />
                                <mt-title
                                    v-if="title"
                                    :heading="titleHeading"
                                >
                                    {{ title }}
                                </mt-title>
                                <mt-tips
                                    v-if="titleTips"
                                    v-bind="tipsProps"
                                />
                                <!-- @slot 自定义 title 尾部区域 -->
                                <slot name="title-end" />
                            </div>
                        </slot>

                        <!-- @slot 自定义描述文本区域 -->
                        <slot name="description">
                            <div
                                v-if="description"
                                :class="bem('description', [size])"
                            >
                                {{ description }}
                            </div>
                        </slot>
                    </div>

                    <div :class="bem('header-operation')">
                        <!-- @slot 自定义标题右侧操作区 -->
                        <slot name="operation" />
                    </div>
                </div>
            </slot>
        </div>
        <!-- Body -->
        <div :class="bem('body')">
            <!-- @slot 卡片内容 -->
            <slot />
        </div>
    </div>
</template>
<script setup lang="ts">
import type { CardProps } from './types';

import { useBem } from '@/composables';
import MtTitle from '@/typography/typography-title.vue';

defineOptions({
    inheritAttrs: false,
});

const props = withDefaults(defineProps<CardProps>(), {
    size: 'medium',
    showHeader: true,
    bordered: undefined,
    divider: undefined,
});

const componentName = 'card';

const { bem } = useBem(componentName);

const slots = useSlots();

const sizeMap = {
    large: {
        titleHeading: 3,
        iconSize: 18,
    },
    // medium 和 small 没区别，只是 padding 会有区别
    medium: {
        titleHeading: 5,
        iconSize: 16,
    },
    small: {
        titleHeading: 5,
        iconSize: 16,
    },
};

const titleHeading = computed(() => {
    return sizeMap[props.size].titleHeading;
});

const tipsProps = computed(() => {
    const iconSize = sizeMap[props.size].iconSize;

    if (typeof props.titleTips === 'string' || Array.isArray(props.titleTips)) {
        return {
            iconSize,
            offset: 0,
            content: props.titleTips,
        };
    }

    return {
        iconSize,
        offset: 0,
        ...(props.titleTips || {}),
    };
});

const needShowHeader = () => Boolean(props.title || props.titleTips || props.description || slots.title || slots.titleTips || slots.description || slots.operation);

const withHeader = () => props.showHeader && (Boolean(slots.header) || needShowHeader());

</script>
