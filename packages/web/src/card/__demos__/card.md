---
author: 刘青
group: 数据展示
projectVersion: private-sign@5.3
---

# Card


## 何时使用

可用于页面布局，也可用于卡片式内容展示


## 基础示例

默认不带边框和分割线

```vue grey-background
<template>
    <mt-card
        title="标题"
        title-tips="标题后的说明文字"
        description="一段描述文本"
    >
        <mt-button slot="operation">操作按钮</mt-button>

        <p>卡片内容</p>
        <p>卡片内容</p>
        <p>卡片内容</p>
    </mt-card>
</template>
```

没有描述文本时，header 区域居中对齐

```vue grey-background
<template>
    <mt-card
        title="标题"
        title-tips="标题后的说明文字"
    >
        <mt-button slot="operation">操作按钮</mt-button>

        <p>卡片内容</p>
        <p>卡片内容</p>
        <p>卡片内容</p>
    </mt-card>
</template>
```


## 尺寸

各尺寸对应的元素大小如下：

- `large`: 标题 `18px`，问号 icon `18px`，标题描述 `13px`，标题上间距为 `16px`，左右间距 `20px`，内容区上间距 `12px`
- `medium`: 标题 `14px`，问号 icon `16px`，标题描述 `12px`，标题上间距为 `16px`，左右间距 `20px`，内容区上间距 `8px`
- `small`: 标题 `13px`，问号 icon `14px`，标题描述 `12px`，标题上间距为 `12px`，左右间距 `16px`，内容区上间距 `4px`

```demo
card.size.demo.vue
```


## 只有内容

没有标题就只会显示内容区，`large` 和 `medium` 尺寸的上下间距为 `16px`（左右为 `20px`），`small` 尺寸的上下间距为 `12px`（左右为 `16px`）

```vue grey-background
<template>
    <div>
        <div style="padding: 16px; background-color: #fff;">
            <mt-segmented
                v-model="size"
                :options="sizeOptions"
            />
        </div>
        <br>
        <mt-card :size="size">
            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const size = ref('large');

const sizeOptions = ref([
    {
        label: 'large',
        value: 'large',
    },
    {
        label: 'medium',
        value: 'medium',
    },
    {
        label: 'small',
        value: 'small',
    },
]);

</script>
```


## 带边框

使用 `bordered` 属性设置卡片边框

```vue
<template>
    <mt-card
        title="标题"
        title-tips="标题后的说明文字"
        description="一段描述文本"
        bordered
    >
        <mt-button slot="operation">操作按钮</mt-button>

        <p>卡片内容</p>
        <p>卡片内容</p>
        <p>卡片内容</p>
    </mt-card>
</template>
```


## 鼠标悬停效果

当设置了边框后，使用 `hover` 属性可以设置鼠标悬停效果

- `border`: 鼠标悬停显示主题色边框
- `shadow`: 鼠标悬停显示阴影
- 当两个一起设置时，边框颜色会变浅，且阴影会适配边框色

```vue
<template>
    <div>
        边框：<mt-switch v-model="border" />
        阴影：<mt-switch v-model="shadow" />
        <br>
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
            bordered
            :hover="hover"
        >
            <mt-button icon="icon-trash" variant="text-minor" link slot="operation" />

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

const border = ref(true);
const shadow = ref(false);

const hover = computed(() => ({
    border: border.value,
    shadow: shadow.value,
}));
</script>
```


## 分割线

使用 `divider` 属性设置头部与内容区的分割线，大尺寸卡片的分割线是通栏，其余尺寸卡片的分割线留有左右边距。

```vue
<template>
    <div>
        <mt-segmented
            v-model="size"
            :options="sizeOptions"
        />
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
            bordered
            divider
            :size="size"
        >
            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>

            <mt-button slot="operation">操作按钮</mt-button>
        </mt-card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const size = ref('large');

const sizeOptions = ref([
    {
        label: 'large',
        value: 'large',
    },
    {
        label: 'medium',
        value: 'medium',
    },
    {
        label: 'small',
        value: 'small',
    },
]);
</script>
```


## 配合 Tabs 使用

配合 `el-tabs` 使用时，我们**推荐使用 `title` 插槽放入 tabs**，一般没有描述文本，且需要设置分割线 `divider`，组件内部默认将 el-tabs 的分割线和下间距去除了，以便更好的实现布局。

顶部 padding 始终为 `8px`，且 `small` 尺寸的 el-tabs 的高度为 `36px`，其余尺寸的 el-tabs 的高度始终为 `46px`

:::warning 注意
组件内部默认覆盖了 el-tabs 的高度和间距，便于后续切换到自研组件，如果外面使用时有发现样式不对的，可联系组件开发者调整，切勿自行覆盖。
:::


```vue
<template>
    <div>
        <mt-segmented
            v-model="size"
            :options="sizeOptions"
        />
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            bordered
            divider
            :size="size"
        >
            <el-tabs slot="title" v-model="activeName">
                <el-tab-pane label="用户管理" name="first" />
                <el-tab-pane label="配置管理" name="second" />
                <el-tab-pane label="角色管理" name="third" />
                <el-tab-pane label="定时任务补偿" name="fourth" />
            </el-tabs>

            <mt-button :size="size" slot="operation">操作按钮</mt-button>

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>

        </mt-card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const size = ref('large');

const sizeOptions = ref([
    {
        label: 'large',
        value: 'large',
    },
    {
        label: 'medium',
        value: 'medium',
    },
    {
        label: 'small',
        value: 'small',
    },
]);

const activeName = ref('first')
</script>
```


## 自定义操作区

通过 `operation` 插槽自定义操作区，可插入 [Button 组件](/web/button)，也可配合 [ButtonGroup 按钮组组件](/web/button-group) 使用，或者任意元素

```vue grey-background
<template>
    <div>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
        >
            <template #operation>
                <mt-button variant="text" link>操作按钮</mt-button>
            </template>

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
        >
            <template #operation>
                <mt-button-group
                    :options="options"
                    :folding-threshold="3"
                />
            </template>

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
        >
            <template #operation>
                <mt-button-group
                    preset="text"
                    :options="options"
                    :folding-threshold="3"
                />
            </template>

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            description="一段描述文本"
        >
            <template #operation>
                <mt-button-group
                    preset="icon"
                    variant="text-minor"
                    size="small"
                    :options="iconOptions"
                    :folding-threshold="3"
                />
            </template>

            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const buttonOptions = Array.from({ length: 4 }).map((_, index) => ({ content: String(`操作${  index + 1}`), key: index }));

const options = ref(buttonOptions);

const iconOptions = ref(buttonOptions.map((item) => ({ ...item, icon: 'icon-warning-circle' })));
</script>
```


## 卡片嵌套使用

Card 组件可以嵌套使用，但需要设置 `bordered` 属性，否则会出现样式问题

```vue grey-background
<template>
    <mt-card
        title="标题"
        title-tips="标题后的说明文字"
        description="一段描述文本"
    >
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            bordered
        >
            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
        <br>
        <mt-card
            title="标题"
            title-tips="标题后的说明文字"
            bordered
        >
            <p>卡片内容</p>
            <p>卡片内容</p>
            <p>卡片内容</p>
        </mt-card>
    </mt-card>
</template>
```


## 配合栅格系统使用

Card 组件可以与 [Grid 栅格组件](/web/grid) 配合使用，灵活实现各种布局

```vue grey-background
<template>
    <mt-row :gutter="[12, 12]">
        <mt-col :span="24">
            <mt-card
                title="标题"
                title-tips="标题后的说明文字"
            >
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
            </mt-card>
        </mt-col>
        <mt-col :span="12">
            <mt-card
                title="标题"
                title-tips="标题后的说明文字"
            >
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
            </mt-card>
        </mt-col>
        <mt-col :span="12">
            <mt-card
                title="标题"
                title-tips="标题后的说明文字"
            >
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
            </mt-card>
        </mt-col>
        <mt-col :span="18">
            <mt-card
                title="标题"
                title-tips="标题后的说明文字"
            >
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
                <p>卡片内容</p>
            </mt-card>
        </mt-col>
        <mt-col :span="6">
            <mt-row :gutter="[12, 12]">
                <mt-col :span="24">
                    <mt-card
                        title="标题"
                        size="small"
                    >
                        <p>卡片内容</p>
                    </mt-card>
                </mt-col>
                <mt-col :span="24">
                    <mt-card
                        title="标题"
                        size="small"
                    >
                        <p>卡片内容</p>
                    </mt-card>
                </mt-col>
            </mt-row>
        </mt-col>
    </mt-row>
</template>
```


## 可视化插槽

```vue no-shadow-dom
<template>
    <doc-visual-slots
        title="卡片标题"
        title-tips="卡片标题后的说明文字"
        description="一段描述文本"
        bordered
        divider
        size="large"
        component-style="width: 460px;"
        :visual-slots="$slots"
    />
</template>
```


## API
