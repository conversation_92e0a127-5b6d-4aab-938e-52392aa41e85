@import '@/button/button.tokens.less';

// large
@mt-card-padding-x-large: 20px;
@mt-card-padding-y-large: 16px;
@mt-card-body-padding-top-large: 12px;


// medium
@mt-card-padding-x-medium: 20px;
@mt-card-padding-y-medium: 16px;
@mt-card-body-padding-top-medium: 8px;

// small
@mt-card-padding-x-small: 16px;
@mt-card-padding-y-small: 12px;
@mt-card-body-padding-top-small: 4px;

.mt-card {
    display: flex;
    flex-direction: column;
    background-color: @color-background-white;
    border-radius: @border-radius;

    &__header {
        display: flex;
        align-items: center;
        gap: 24px;
    }

    &__header-title {
        flex: 1 1 auto;
        min-width: 0;
    }

    &__title-wrapper {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    &__description {
        color: @color-text-4;
        font-size: @font-size-sm;
        line-height: 20px;

        &--large {
            font-size: @font-size;
        }
    }

    &__body {
        flex: 1 1 auto;
        min-height: 0;
    }

    &--bordered {
        border: 1px solid @color-border;
    }


    // ============ Hover ============

    &--hover-border {
        transition: border-color 0.3s ease-in-out;

        &:hover {
            border-color: @color-primary;
        }
    }

    &--hover-shadow {
        transition: box-shadow 0.3s ease-in-out;

        &:hover {
            box-shadow: @shadow-1-center;
        }
    }

    &--hover-border&--hover-shadow {
        transition: all 0.3s ease-in-out;

        &:hover {
            border-color: @color-primary-3;
            box-shadow: @shadow-blue-card;
        }
    }


    // ============ Description ============

    &--with-description {
        & > .mt-card__header-wrapper > .mt-card__header {
            align-items: flex-start;
        }
    }

    // ============ Size ============

    // 无 header 的情况和带 divider 的情况
    &--large, div&--with-divider&--large {
        & > .mt-card__body {
            padding: @mt-card-padding-y-large @mt-card-padding-x-large;
        }
    }

    &--medium, div&--with-divider&--medium {
        & > .mt-card__body {
            padding: @mt-card-padding-y-medium @mt-card-padding-x-medium;
        }
    }

    &--small, div&--with-divider&--small {
        & > .mt-card__body {
            padding: @mt-card-padding-y-small @mt-card-padding-x-small;
        }
    }

    // 有 header 的情况
    &--with-header&--large {
        & > .mt-card__header-wrapper {
            .mt-card__header {
                padding: @mt-card-padding-y-large @mt-card-padding-x-large 4px;
            }
        }

        & > .mt-card__body {
            padding: @mt-card-body-padding-top-large @mt-card-padding-x-large @mt-card-padding-y-large;
        }
    }
    &--with-header&--medium {
        & > .mt-card__header-wrapper {
            .mt-card__header {
                padding: @mt-card-padding-y-medium @mt-card-padding-x-medium 4px;
            }
        }

        & > .mt-card__body {
            padding: @mt-card-body-padding-top-medium @mt-card-padding-x-medium @mt-card-padding-y-medium;
        }
    }
    &--with-header&--small {
        & > .mt-card__header-wrapper {
            .mt-card__header {
                padding: @mt-card-padding-y-small @mt-card-padding-x-small 4px;
            }
        }

        & > .mt-card__body {
            padding: @mt-card-body-padding-top-small @mt-card-padding-x-small @mt-card-padding-y-small;
        }
    }


    // ============ Divider ============

    &--with-divider {
        & > .mt-card__header-wrapper {
            position: relative;

            &::after {
                display: table;
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: @color-border-light;
            }

            // 有分割线时，header 下间距为 12px
            div.mt-card__header.mt-card__header {
                padding-bottom: 12px;
            }
        }
    }

    &--with-divider&--large {
        & > .mt-card__header-wrapper::after {
            left: 0;
            width: 100%;
        }
    }

    &--with-divider&--medium, &--with-divider&--small {
        & > .mt-card__header-wrapper::after {
            left: 50%;
            transform: translateX(-50%);
        }
    }
    &--with-divider&--medium {
        & > .mt-card__header-wrapper::after {
            width: calc(100% - @mt-card-padding-x-medium * 2);
        }
    }

    &--with-divider&--small {
        & > .mt-card__header-wrapper::after {
            width: calc(100% - @mt-card-padding-x-small * 2);
        }
    }


    // ============ Tabs ============

    & > .mt-card__header-wrapper {
        // 去除 el-tabs 的分割线和下间距
        .el-tabs__nav-wrap:after {
            content: none;
        }

        // -margin 抵消间距
        // .el-tabs margin 15 - 1px 分割线
        .el-tabs--top {
            margin-bottom: -14px;
        }

        // 更改 tabs 高度
        .el-tabs__item {
            height: 46px;
            line-height: 46px;
        }

        .el-tabs__nav-next, .el-tabs__nav-prev {
            line-height: 46px;
        }

        // 有 tabs 时，header 顶部 padding 为 8px
        .mt-card__header.mt-card__header:has(.el-tabs) {
            padding-top: 8px;
            padding-bottom: 0;
        }
    }

    &--small {
        & > .mt-card__header-wrapper {
            .el-tabs__item {
                height: 36px;
                line-height: 36px;
                font-size: @font-size;
            }

            .el-tabs__nav-next, .el-tabs__nav-prev {
                line-height: 36px;
            }
        }
    }


    // ============ Operation ButtonGroup ============

    &__header-operation {
        .mt-button-group--icon {
            &:has(.mt-button__large) {
                margin-right: -@btn-large-padding--only-icon;
            }
            &:has(.mt-button__medium) {
                margin-right: -@btn-medium-padding--only-icon;
            }
            &:has(.mt-button__small) {
                margin-right: -@btn-small-padding--only-icon;
            }
        }
    }
}
