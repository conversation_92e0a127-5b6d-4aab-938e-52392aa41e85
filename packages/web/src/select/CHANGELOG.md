# Select

## 2023-09-15 (feat) /刘青/

支持手动控制下拉弹窗的状态

## 2023-12-07 (feat) /王炎/

新加 `placeholder` 属性

## 2024-07-08 (feat) /刘青/

新增 `disabled` 属性，支持禁用

## 2024-10-22 (feat) /刘青/

优化 Select 组件功能：
- 默认展示风格调整为输入框风格，原文本风格需要传入 `variant="text"` 开启
- 新增多选溢出省略功能
- 新增允许创建选项功能
- 支持在灰色背景下使用
- 新增全选功能
- 重构底层 SelectPanel 组件

## 2025-04-25 (feat) /王炎/

- 未选中选项时，触发器默认文本展示为灰色 [PRIV-8311](https://jira.qiyuesuo.me/browse/PRIV-8311)
- 文本风格选择器支持 `clearable` [PRIV-8311](https://jira.qiyuesuo.me/browse/PRIV-8311)

## 2025-05-22 (fix) /刘青/

修复点击已选中的值也会触发 `change` 事件的问题
