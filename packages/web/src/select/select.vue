<template>
    <mt-select-popover
        ref="popover"
        v-bind="$attrs"
        :display-text="finalDisplayText"
        :lazy="lazy"
        :class="bem('', `variant-${props.variant}`)"
        :disabled="mergedDisabled"
        :reference-size="mergedSize"
        @visible-change="onVisibleChange"
        @clear="onClear"
    >
        <mt-select-panel
            v-bind="{ ...$attrs, multiple, fieldMap }"
            ref="panel"
            v-model="modelValue"
            :default-selected-options="finalDefaultSelectedOptions"
            :width="finalPanelWidth"
            :footer="isInput ? false : footer"
            :searchable="isInput ? false : searchable"
            :keyword="searchKeyword"
            :prevent-focus="isInput"
            :sticky-options="finalStickyOptions"
            :debounced-time="debouncedTime"
            :options="options"
            @change="onSelectPanelChange"
            @select-all="onSelectAll"
            @selection-change="onSelectionChange"
            @display-options-change="onDisplayOptionsChange"
            @select="onSelect"
            @deselect="onDeselect"
        >
            <template
                v-for="key of Object.keys($scopedSlots)"
                #[key]="{ option, index, selected }"
            >
                <!--
                    @slot 透传 slot
                    @member input-suffix | 搜索框后缀 | -
                    @member blank | 自定义空状态 | -
                    @member footer | 自定义底部区域 | -
                    @member option | 自定义列表项 | `option` 单项的数据
                    @member option-label | 自定义列表项 label | `option` 单项的数据
                    @member option-label-text | 自定义列表项 label 文字 | `option` 单项的数据
                    @member option-tag | 自定义列表项 tag | `option` 单项的数据
                    @member option-extra | 自定义列表项 extra | `option` 单项的数据
                  -->
                <slot
                    :name="key"
                    :option="option"
                    :index="index"
                    :selected="selected"
                />
            </template>
        </mt-select-panel>

        <template #reference-display>
            <!-- @slot （仅文本风格生效）自定义显示文本区域，包含文字和箭头，参数是展示的文字和选中项 -->
            <slot
                name="reference-display"
                :selected-options="selectedOptions"
                :display-text="finalDisplayText"
            />
        </template>
        <template #reference-display-text>
            <!-- @slot （仅文本风格生效）自定义值的展示区域，仅包含文字，参数是展示的文字和选中项 -->
            <slot
                name="reference-display-text"
                :selected-options="selectedOptions"
                :display-text="finalDisplayText"
            />
        </template>

        <template #reference>
            <!-- @slot 自定义触发元素 -->
            <slot
                name="reference"
                :display-text="finalDisplayText"
            >
                <mt-select-input
                    v-if="isInput"
                    v-bind="$attrs"
                    v-model="selectedInputValue"
                    :allow-create="allowCreate ? allowCreateFn : undefined"
                    :search-value.sync="searchKeyword"
                    :size="mergedSize"
                    :multiple="multiple"
                    :placeholder="placeholder"
                    :disabled="mergedDisabled"
                    :searchable="searchable"
                    :popover-visible="popoverVisible"
                    :debounced-time="debouncedTime"
                    @input="onInput"
                    @clear="onClear"
                    @focus="isFocus = true"
                    @blur="isFocus = false"
                    @remove="onRemove"
                    @resize="onInputResize"
                    @create="onCreate"
                >
                    <template
                        v-for="slotKey of Object.keys($scopedSlots)"
                        #[slotKey]="{ option, index }"
                    >
                        <!--
                            @slot 透传 slot
                            @member selected-option-tag | 自定义选中项 tag 文字 | `option` 单项的数据 `index` 当前项索引
                        -->
                        <slot
                            :name="slotKey"
                            :option="option"
                            :index="index"
                        />
                    </template>
                </mt-select-input>
            </slot>
        </template>
    </mt-select-popover>
</template>

<script lang="ts">
export default {
    inheritAttrs: false,
    model: {
        prop: 'value',
        event: 'update:select-value',
    },
};
</script>

<script setup lang="ts">
import { useVModel } from '@mitra-design/shared/composables';

import { useCreateOption } from './hooks/useCerateOption';
import type { SelectRef } from './types';

import type { FormSizeType, FormVariantType } from '@/_types/form';
import { useBem } from '@/composables';
import { useFormItemInject } from '@/form/hooks/useFormItemInject';
import type { SelectInputTagData } from '@/select-input';
import MtSelectInput from '@/select-input/select-input.vue';
import type { SelectPanel } from '@/select-panel';
import MtSelectPanel from '@/select-panel/select-panel.vue';
import type { FieldMap, FlattenSelectOption, SelectOption } from '@/select-panel/types';
import { flattenOptions, getOptions } from '@/select-panel/utils';
import MtSelectPopover from '@/select-popover/select-popover.vue';

interface SelectProps {
    /**
     * 绑定值，支持双向绑定
     */
    value?: any | any[];

    /** 控制弹窗是否可见，支持.sync */
    visible?: boolean;

    /**
     * 触发器的样式，v2.8.0 后默认是输入框风格，文本风格请手动设置
     * @version v2.8.0
     */
    variant?: FormVariantType | 'text';

    /**
     * 尺寸
     * @version v2.8.0
     */
    size?: FormSizeType;

    /** 选项分隔符，默认 `,` */
    displaySeparator?: string;

    /** 显示文本，默认是选中项 label 与选项分隔符的拼接，如果需要显示自定义的文本，可以传入，支持字符串和函数（仅对 文本风格 的生效） */
    displayText?: string | ((selectedOptions: FlattenSelectOption[]) => string);

    /**
     * 是否多选，默认单选
     */
    multiple?: boolean;

    /**
     * 是否开启搜索
     */
    searchable?: boolean;

    /**
     * 是否允许创建
     * @version v2.8.0
     */
    allowCreate?: boolean | ((text: string) => Promise<boolean>);

    /**
     * 是否需要懒加载数据，为 `true` 时仅在弹窗打开时初始化列表
     */
    lazy?: boolean;

    /**
     * 数据化配置选项内容
     * - 支持以函数形式传入，该函数必须返回一个**数组类型的 Promise**
     * @type {SelectOption[] | () => Promise<SelectOption[]>}
     */
    options?: SelectOption[] | (() => Promise<SelectOption[]>);

    /**
     * 默认选中项，一般异步分页数据选中后，初次渲染需要提前补充不在本页的数据
     * - 可以直接传入数据
     * - 也支持以函数形式传入，该函数必须返回一个**数组类型的 Promise**
     * @type {SelectOption[] | (() => Promise<SelectOption[]>)}
     */
    defaultSelectedOptions?: SelectOption[] | (() => Promise<SelectOption[]>);

    /** 置顶的数据项 */
    stickyOptions?: SelectOption[];

    /**
     * 字段映射，用于将数据中的字段映射到组件中
     */
    fieldMap?: FieldMap;

    /**
     * 面板的宽度，一般情况下不需要指定，如果需要特定的宽度，可以传入
     */
    panelWidth?: number | string;

    /**
     * 未选择任何选项时，呈现的文本
     */
    placeholder?: string;

    /**
     * 是否禁用
     */
    disabled?: boolean;

    /**
     * 选择面板底部操作区文字，默认不显示
     * - 如果传 `true` 会显示默认的 「清空已选」
     * - 如果传文字会显示传进来的文字
     * - 底部操作默认是**清空所有选择项**，如果需要自定义，请使用 「footer」 插槽
     */
    footer?: string | boolean;

    /**
     * 搜索防抖时间
     * @version v2.8.0
     */
    debouncedTime?: number;
}


const props = withDefaults(defineProps<SelectProps>(), {
    variant: 'outlined',
    displaySeparator: ',',
    multiple: false,
    lazy: false,
    debouncedTime: 500,
    disabled: undefined,
    fieldMap: () => ({
        labelField: 'label',
        valueField: 'value',
        tagField: 'tag',
        extraField: 'extra',
    }),
});

const emit = defineEmits<{
    (event: 'update:select-value', value: string | string[]): void;
    /**
     * 值发生变化时触发，参数是变化后的值
     * @property {any} value 变化后的值
     */
    (event: 'change', value: any): void;
    /**
     * 选中某一项时触发
     * @property {FlattenSelectOption} option 选中的那一项
     */
    (event: 'select', option: FlattenSelectOption): void;
    /**
     * 反选中某一项时触发, 仅在多选模式下生效
     * @property {FlattenSelectOption | null} option 反选的那一项
     */
    (event: 'deselect', option: FlattenSelectOption | null): void;
    /**
     * 选中项变化时触发
     * @property {FlattenSelectOption[]} selectedOptions 已选中的所有项
     */
    (event: 'selection-change', selectedOptions: FlattenSelectOption[]): void;
    /**
     * 下拉框的显示/隐藏时触发，参数是当前的显示状态
     * @property {boolean} visible 当前的显示状态
     */
    (event: 'visible-change', visible: boolean): void;
    /**
     * 全选/取消全选时触发
     * @property {boolean} checked 是否全选
     * @property {any} value 选中的值
     * @version v2.8.0
     * @private
     */
    (event: 'select-all', checked: boolean, value: any): void;
    (event: 'update:visible', visible: boolean): void;
}>();

const { bem } = useBem('select');

const {
    mergedSize,
    mergedDisabled,
} = useFormItemInject(props, {
    size: 'medium',
});

const modelValue = useVModel(props, 'value', emit, 'update:select-value');

const isInput = computed(() => props.variant !== 'text');

const updateModelValue = (value: any) => {
    modelValue.value = value;

    nextTick(() => {
        emit('change', value);
    });
};

const onClear = () => {
    if (props.multiple) {
        updateModelValue([]);
    } else {
        updateModelValue('');
    }
};

const onRemove = (index: number) => {
    const value = modelValue.value as string[];
    const removeItem = value.find((item, i) => i === index);
    const option = selectedOptions.value[index];

    if (!removeItem || removeItem !== option.__value__) return;

    removeOptionFromTemp(option);

    value.splice(index, 1);

    updateModelValue(value);
};



// Popover Visible
const popoverVisible = ref(props.visible ?? false);
const popover = ref<InstanceType<typeof MtSelectPopover>>();
const panel = ref<SelectPanel>();

const reset = () => {
    panel.value?.reset();
};

const resetData = () => {
    panel.value?.resetData();
};

const doOpen = () => {
    popover.value?.doOpen();
};

const doClose = () => {
    popover.value?.doClose();
};

const onVisibleChange = (visible: boolean) => {
    emit('visible-change', visible);
    emit('update:visible', visible);
    popoverVisible.value = visible;

    // 为了虚拟列表的位置能在 popover 打开时正常显示
    nextTick(() => {
        panel.value?.syncState();
    });
};

watch(() => props.visible, (value) => {
    value ? doOpen() : doClose();
}, { immediate: true });



// Selected Options
const selectedOptions = ref<FlattenSelectOption[]>([]);

const finalDisplayText = computed(() => {
    let text = '';

    if (props.displayText) {
        text = typeof props.displayText === 'function' ? props.displayText(selectedOptions.value) : props.displayText;
    } else {
        text = selectedOptions.value.map(o => o.__label__).join(props.displaySeparator);
    }

    return text || props.placeholder;
});

const onSelectionChange = (value: FlattenSelectOption[]) => {
    selectedOptions.value = value;
    emit('selection-change', value);
};

const onSelectAll = (checked: boolean, value: any) => {
    emit('select-all', checked, value);
};


const finalDefaultSelectedOptions = ref<FlattenSelectOption[]>([]);

// 处理传入的 defaultSelectedOptions 数据，将最终数据传入 select-panel 组件
// 避免 select-panel 组件内部再次处理 defaultSelectedOptions 数据
watch([() => props.defaultSelectedOptions, modelValue], async ([value, bindValue]) => {
    if (!value) return;

    const options = await getOptions(value);
    finalDefaultSelectedOptions.value = flattenOptions(options, props.fieldMap).flattenList;
    // @ts-ignore
    finalDefaultSelectedOptions.value.__inner__ = true;

    // 仅当懒加载时，弹窗中的 select-panel 组件未加载时处理选中项逻辑
    // 根据选中项的值从 defaultSelectedOptions 中筛选出相应的选项数据，从而获取到 label
    if (panel.value) return;

    if (!props.multiple) {
        bindValue = [bindValue];
    }

    selectedOptions.value = bindValue.map((item: any) => finalDefaultSelectedOptions.value.find(o => o.__value__ === item)).filter(Boolean);
}, { immediate: true });



// Search
const searchKeyword = ref('');
const isFocus = ref(false);

const onInput = (value: string) => {
    if (props.searchable) {
        searchKeyword.value = value;
    }

    if (isFocus.value) {
        popover.value?.doOpen();
    }

    if (props.allowCreate) {
        searchKeyword.value = value;
        createPendingOption(value);
    }
};

const clearInputValue = () => {
    nextTick(() => {
        searchKeyword.value = '';
    });
};

const onSelectPanelChange = (value: any) => {
    emit('change', value);
};


// Create Option
const {
    allowCreateFn,
    tempAddedOptions,
    createPendingOption,
    createNewOption,
    finalStickyOptions,
    removeOptionFromTemp,
    onDisplayOptionsChange,
} = useCreateOption(
    toRef(props, 'allowCreate'),
    toRef(props, 'stickyOptions'),
    props.fieldMap
);

const onCreate = async () => {
    if (props.allowCreate && await allowCreateFn(searchKeyword.value)) {
        createNewOption();
        clearInputValue();
    }
};

const onSelect = async (option: FlattenSelectOption) => {
    emit('select', option);

    if (props.allowCreate && await allowCreateFn(searchKeyword.value)) {
        createNewOption();
    }

    // 多选选中时，清空搜索关键词
    if (props.multiple) {
        clearInputValue();
    }

    // 单选选中时，关闭弹层
    if (!props.multiple) {
        // clearInputValue();
        setTimeout(() => {
            doClose();
        }, 100);
    }
};

const onDeselect = (option: FlattenSelectOption | null) => {
    emit('deselect', option);

    removeOptionFromTemp(option);
};



// Select Input
const selectedInputValue = computed<SelectInputTagData | SelectInputTagData[]>({
    get() {
        // 单选
        if (!props.multiple) {
            const item = selectedOptions.value[0];
            return {
                label: item?.__label__,
                value: item?.__value__,
            };
        }

        return selectedOptions.value.map((item => {
            return {
                label: item.__label__,
                value: item.__value__,
                closable: !item.disabled,
                isNew: !!tempAddedOptions.value.find(o => o.__value__ === item.__value__),
            };
        }));
    },

    set(v) {
        if (!props.multiple) {
            const value = (v as SelectInputTagData).value;
            updateModelValue(value);
            return;
        }

        const value = (v as SelectInputTagData[]).map(item => item.value);
        updateModelValue(value);
    },
});


// Panel and Input width
const inputWidth = ref(0);
const inputHeight = ref(0);

const finalPanelWidth = computed(() => {
    return props.panelWidth || inputWidth.value || undefined;
});

const onInputResize = (size: { width: number; height: number }) => {
    if (inputWidth.value !== size.width) {
        inputWidth.value = size.width;
    }

    if (inputHeight.value !== size.height && popoverVisible.value) {
        inputHeight.value = size.height;

        updatePopoverPosition();
    }
};

const updatePopoverPosition = () => {
    popover.value?.updatePopper();
};


// Expose
defineExpose<SelectRef>({
    /**
     * 为了兼容私有云历史版本中通过 ref 获取 Select 实例中的 `selectedOptions`
     * 外界不应使用
     * @deprecated
     */
    selectedOptions,
    /** 重置 select 搜索关键字 */
    reset,
    /** 重置列表数据，将会使用初始参数调用 `getData` */
    resetData,
    /** 打开 popover */
    doOpen,
    /** 关闭 popover */
    doClose,
});
</script>
