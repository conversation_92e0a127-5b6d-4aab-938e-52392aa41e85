---
author: 刘青
group: 反馈
projectVersion: private-sign@5.3
---

# Modal 弹窗


## 何时使用

弹窗可以承载一些非打断性的内容，比如：

- 临时填写一个表单
- 查看某些数据详情
- 提示信息


:::tip 弹窗宽度说明
最新弹窗规范设定 4 个常规宽度和 3 个特殊宽度，具体使用场景如下：

常规宽度：

- **440px**
  - 常规文本提示
  - 数量较少的表单
- **560px**
  - 有横向布局的表单，且表单项中有下级可展开的子表单项
  - 有内嵌表格，且表格列数不超过 4 列
- **720px**
  - 弹窗内容需分为左右两个板块呈现
  - 弹窗内包含多个图片，且可以操作
  - 弹窗内容较多，且每项都可操作
  - 展示用户须知等文档类内容
- **960px**
  - 弹窗内容分左右两个板块呈现，且包含实时操作场景
  - 包含复杂数据的选择

特殊宽度：
- 512px
- 640px
- 800px
:::


## 基础示例

```vue
<template>
    <div>
        <mt-modal v-model="visible" title="弹窗标题">
            <div>弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible = true">
            打开弹窗
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const visible = ref(false)
</script>
```


## 与 ManuallyMount 一起使用

当使用 `ManuallyMount` 时，`v-model` 的值需要绑定为 `useManuallyMount` 返回的 `isActive`。

当使用默认的取消和确定按钮时，点击按钮后，<strong style="color: red;">会自动调用 `close` 方法关闭弹窗</strong>。所以外界可以无需调用 `close` 方法手动关闭弹窗。

如果你想要在弹窗关闭前执行一些操作（比如表单校验），可以使用 `beforeCancel` 和 `beforeConfirm` 回调。如果你想在关闭弹窗后执行一些操作，可以使用 `cancel` 和 `confirm` 事件处理（[见下方关闭前校验逻辑](#关闭前校验逻辑)）。

```
<template>
    <div>
        <mt-modal v-model="isActive" title="弹窗标题">
            <div>弹窗内容</div>
        </mt-modal>
    </div>
</template>

<script setup lang="ts">
import { useManuallyMount } from '@mitra-design/helpers/manually-mount'

const { isActive } = useManuallyMount()
</script>
```


## 带分割线

可通过 `divider` 属性设置分割线。
- 如果设置布尔值，则头部和底部都会设置分割线
- 如果设置 `{  header: true, footer: true }`，则可以分别设置头部和底部的分割线

```vue
<template>
    <div>
        <mt-modal
            v-model="visible1"
            title="弹窗标题"
            divider
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible2"
            title="弹窗标题"
            :divider="{ header: true }"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible3"
            title="弹窗标题"
            :divider="{ footer: true }"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible1 = true">
            带头部和底部分割线
        </mt-button>
        <mt-button @click="visible2 = true">
            仅头部有分割线
        </mt-button>
        <mt-button @click="visible3 = true">
            仅底部部有分割线
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible1 = ref(false)
const visible2 = ref(false)
const visible3 = ref(false)
</script>
```


## 自定义标题

通过 `title` 属性设置标题，默认一行展示，超出省略同时展示 Tooltip；通过 `titleTips` 为标题设置一个问号提示，也支持 `title` 插槽自定义整个标题区域

如果你想在默认的 title 后加点东西，可以使用 `title-end` 插槽

```vue
<template>
    <div>
        <mt-modal
            v-model="visible1"
            title="弹窗标题弹窗标题弹窗标题弹窗标题弹窗标题弹窗标题弹窗标题弹窗标题弹窗标题"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible2"
            title="弹窗标题"
            title-tips="标题的提示文字"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible3"
            title="弹窗标题"
        >
            <template #title-end>
                <mt-tag theme="success">已完成</mt-tag>
            </template>
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible4"
        >
            <template #title>
                <mt-title>自定义大标题</mt-title>
            </template>
            <div>弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible1 = true">
            通过 title 设置
        </mt-button>
        <mt-button @click="visible2 = true">
            展示 title 提示
        </mt-button>
        <mt-button @click="visible3 = true">
            title 尾部区域
        </mt-button>
        <mt-button @click="visible4 = true">
            完全自定义 title
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible1 = ref(false)
const visible2 = ref(false)
const visible3 = ref(false)
const visible4 = ref(false)
</script>
```



## 自定义内容区 padding

内容区只能通过默认插槽传入，且附带默认的 `padding`，如果某些情况下默认 `padding` 无法满足，或者想去除默认 `padding`，可以通过 `padding` 属性设置，属性值与 [CSS padding](https://developer.mozilla.org/docs/Web/CSS/padding) 一致。

```vue
<template>
    <div>
        <mt-modal
            v-model="visible"
            title="弹窗标题"
            padding="0"
        >
            <mt-alert banner>提示内容</mt-alert>
            <div style="padding: 16px 20px;">弹窗内容</div>
            </mt-modal>
        <mt-button @click="visible = true">
            打开弹窗
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible = ref(false)
</script>
```



## 自定义底部操作区

默认展示底部操作区，分别为取消和确认按钮，如果不需要展示，可通过 `:footer="false"` 属性关闭。

组件也提供 `footer` 插槽完全自定义底部操作区，对于预设的底部按钮不满意可通过插槽嵌入 [ButtonGroup](/web/button-group) 组件。

:::tip 使用说明
完整 footer 对象属性见下方 API 表格中的 `ModalFooterProps`
:::

```vue
<template>
    <div>
        <mt-modal
            v-model="visible1"
            title="弹窗标题"
            :footer="false"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible2"
            title="弹窗标题"
            :footer="{
                cancelText: 'Exit',
                confirmText: 'Next',
                cancelDisabled: true,
            }"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible3"
            title="弹窗标题"
        >
            <div>弹窗内容</div>
            <template #footer>
                <mt-steps
                    style="padding: 16px;"
                    :value="2"
                    :options="stepOptions"
                />
            </template>
        </mt-modal>
        <mt-button @click="visible1 = true">
            无 footer
        </mt-button>
        <mt-button @click="visible2 = true">
            配置底部按钮
        </mt-button>
        <mt-button @click="visible3 = true">
            整点别的
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible1 = ref(false)
const visible2 = ref(false)
const visible3 = ref(false)

const stepOptions = ref([
    {
        description: 'desc1',
        title: 'title1',
    },
    {
        description: 'desc2',
        title: 'title2',
    },
    {
        description: 'desc3',
        title: 'title3',
    },
])
</script>
```


## 关闭前校验逻辑

通过给 `footer` 传递一个对象，包含 `beforeCancel` 和 `beforeConfirm` 函数，即可在点击取消按钮和确认按钮时执行。

你可以使用同步或异步 Promise 来控制弹窗是否关闭：

- 同步：返回 `Boolean` 类型，<strong style="color: red;">返回 false</strong> 则不会关闭弹窗且不触发 `cancel/confirm` 事件
- 异步：返回 `Promise`，如果 <strong style="color: red;">Promise 被 reject 或返回 false</strong> 则不会关闭弹窗且不触发 `cancel/confirm` 事件

异步的回调触发时，按钮有个默认的 loading 状态，**仅在 Promise pending 时生效**，如果你需要手动控制，可以传入 `footer.cancelLoading` 和 `footer.confirmLoading`。

:::warning 提示
通过点击关闭按钮触发的弹窗关闭，不会触发 `beforeCancel` 函数。如果需要在 `beforeCancel` 执行校验逻辑，建议直接 `:closable="false"`隐藏关闭按钮。
:::

```vue
<template>
    <div>
        <mt-modal
            v-model="visible"
            title="弹窗标题"
            :footer="{
                beforeCancel,
                beforeConfirm,
                cancelLoading,
                confirmLoading,
            }"
            @cancel="onCancel"
            @confirm="onConfirm"
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible = true">
            显示弹窗
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
const cancelLoading = ref(false);
const confirmLoading = ref(false);

const beforeCancel = () => new Promise(resolve => {
    cancelLoading.value = true;
    setTimeout(() => {
        cancelLoading.value = false;
        resolve(true);
    }, 2000);
});
const beforeConfirm = () => new Promise(resolve => {
    confirmLoading.value = true;

    setTimeout(() => {
        confirmLoading.value = false;
        resolve(false);
    }, 2000);
});
const onCancel = () => console.log('cancel');
const onConfirm = () => console.log('confirm');
</script>
```


## 获取弹窗内 DOM 的时机

默认情况下，当弹窗未打开时，内容区是没有渲染的，只有当 Modal 组件 `v-model` 值为 `true` 时才会开始执行挂载逻辑，如果你想在弹窗打开时获取内部的 DOM 元素，目前的方法是通过 `opened` 事件获取。
`opened` 的事件是弹窗动画执行完毕后触发的，这个时候弹窗内部的元素都已经挂载完成。

```vue
<template>
    <div>
        <mt-modal
            v-model="visible"
            title="弹窗标题"
            destroy-on-close
            @opened="onOpened"
        >
            <div ref="contentRef">弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible = true">
            打开弹窗
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible = ref(false)
const contentRef = ref<HTMLElement>()

const onOpened = () => {
    console.log('opened')
    console.log(contentRef.value)

    contentRef.value.style.color = 'red'
}
</script>
```


## 可拖拽的弹窗

通过 `draggable` 属性，可以设置弹窗是否可拖拽。默认只有头部可以拖拽，就算自定义了 `header` 也同样生效

```vue
<template>
    <div>
        <mt-modal
            v-model="visible1"
            title="弹窗标题"
            draggable
        >
            <div>弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible2"
            draggable
        >
            <template #header>
                <div style="background-color: var(--mt-color-primary-1); border: 1px solid var(--mt-color-primary-3); color: var(--mt-color-primary); padding: 16px;">可拖拽区域</div>
            </template>
            <div>弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible1 = true">
            默认头部可拖拽
        </mt-button>
        <mt-button @click="visible2 = true">
            自定义 header
        </mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const visible1 = ref(false)
const visible2 = ref(false)
</script>
```


## 全屏弹窗

通过 `fullscreen` 属性，可以设置弹窗是否为全屏

- `modal`: 弹窗上下左右留有间距（上下间距为 `40px`，左右间距为 `60px`），弹窗宽高为屏幕宽高 - 上下左右间距
- `page`: 弹窗上下左右不留间距，弹窗宽高为屏幕宽高，且没有动画效果

```vue
<template>
    <div>
        <mt-modal
            v-model="visible1"
            title="全屏弹窗"
            fullscreen="modal"
        >
            <div v-for="item of 100" :key="item">弹窗内容</div>
        </mt-modal>
        <mt-modal
            v-model="visible2"
            title="全屏弹窗"
            fullscreen="page"
        >
            <div v-for="item of 100" :key="item">弹窗内容</div>
        </mt-modal>
        <mt-button @click="visible1 = true">
            打开 Modal 样式的全屏弹窗
        </mt-button>
        <mt-button @click="visible2 = true">
            打开 Page 样式的全屏弹窗
        </mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const visible1 = ref(false)
const visible2 = ref(false)
</script>
```


## 可视化插槽

```vue no-shadow-dom
<template>
    <doc-visual-slots
        title="弹窗标题"
        visible
        inline
        :render-container="null"
        :visual-slots="$slots"
    />
</template>
```


## FAQ

#### 1. cancel/confirm 事件和 beforeCancel/beforeConfirm 回调有什么区别？

在点击默认的取消按钮和确认按钮时，会先执行 `beforeCancel/beforeConfirm` 回调，如果显式返回 `false` 或 Promise 被 reject，则不会关闭弹窗且不触发 `cancel/confirm` 事件。

**即 `beforeCancel/beforeConfirm` 回调是在弹窗关闭前调用的，`cancel/confirm` 事件是在弹窗关闭后调用的，事件中是无法阻止弹窗关闭的。**


## API


%INTERFACE(ModalFooterProps, ../_types/modal/index.ts)
