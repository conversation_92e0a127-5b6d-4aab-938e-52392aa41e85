# ConfirmModal

ConfirmModal 组件支持多种类型的确认弹窗，通过设置 `type` 属性，可以控制弹窗的类型和样式。

## 基础用法

`type` 包括：

- `confirm`（默认）：问号图标，默认带取消和确认按钮
- `info`：信息图标，默认带确认按钮
- `success`：成功图标，默认带确认按钮
- `warning`：警告图标，默认带确认按钮
- `error`：错误图标，默认带取消和确认按钮

```vue
<template>
    <div>
        <mt-confirm-modal
            v-model="visible"
            title="弹窗标题"
            content="弹窗内容"
            :type="type"
        />
        <mt-button variant="light" @click="showModal('confirm')">Confirm</mt-button>
        <mt-button @click="showModal('info')">Info</mt-button>
        <mt-button status="success" @click="showModal('success')">Success</mt-button>
        <mt-button status="warning" @click="showModal('warning')">Warning</mt-button>
        <mt-button status="error" @click="showModal('error')">Error</mt-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
const type = ref('confirm');

const showModal = (modalType: string) => {
    type.value = modalType;
    visible.value = true;
};
</script>
```


## 函数式调用

ConfirmModal 组件提供了静态方法调用，可用于在部分场景下快速调用弹窗

```vue
<template>
    <div>
        <mt-button variant="light" @click="showConfirmModal">ConfirmModal.confirm</mt-button>
        <mt-button @click="showInfoModal">ConfirmModal.info</mt-button>
        <mt-button status="success" @click="showSuccessModal">ConfirmModal.success</mt-button>
        <mt-button status="warning" @click="showWarningModal">ConfirmModal.warning</mt-button>
        <mt-button status="error" @click="showErrorModal">ConfirmModal.error</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import { ConfirmModal } from '@mitra-design/web';

const showConfirmModal = () => {
    ConfirmModal.confirm({
        title: '确认弹窗',
        content: '弹窗内容',
        onConfirm: () => {
            console.log('confirm onConfirm');
        },
        onCancel: () => {
            console.log('confirm onCancel');
        }
    }).then(() => {
        console.log('confirm then');
    }).catch(() => {
        console.log('confirm catch');
    });
};

const showInfoModal = () => {
    ConfirmModal.info({
        title: '信息弹窗',
        content: '弹窗内容',
        onConfirm: () => {
            console.log('info onConfirm');
        },
        onCancel: () => {
            console.log('info onCancel');
        }
    }).then(() => {
        console.log('info then');
    }).catch(() => {
        console.log('info catch');
    });
};

const showSuccessModal = () => {
    ConfirmModal.success({
        title: '成功弹窗',
        content: '弹窗内容',
        onConfirm: () => {
            console.log('success onConfirm');
        },
        onCancel: () => {
            console.log('success onCancel');
        }
    }).then(() => {
        console.log('success then');
    }).catch(() => {
        console.log('success catch');
    });
};

const showWarningModal = () => {
    ConfirmModal.warning({
        title: '警告弹窗',
        content: '弹窗内容',
        onConfirm: () => {
            console.log('warning onConfirm');
        },
        onCancel: () => {
            console.log('warning onCancel');
        }
    }).then(() => {
        console.log('warning then');
    }).catch(() => {
        console.log('warning catch');
    });
};

const showErrorModal = () => {
    ConfirmModal.error({
        title: '错误弹窗',
        content: '弹窗内容',
        onConfirm: () => {
            console.log('error onConfirm');
        },
        onCancel: () => {
            console.log('error onCancel');
        }
    }).then(() => {
        console.log('error then');
    }).catch(() => {
        console.log('error catch');
    });
};
</script>
```


## 支持内容动态更新

通过函数式调用的弹窗，支持传入响应式属性，以便能够动态更新类型、内容、标题等

```vue
<template>
    <div>
        <mt-button @click="updateTime">动态更新当前时间</mt-button>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { ConfirmModal } from '@mitra-design/web';

const currentTime = ref();

let intervalId: number | undefined;

const updateCurrentTime = () => {
    currentTime.value = new Date().toLocaleString();
};

const updateTime = () => {
    updateCurrentTime()

    intervalId = setInterval(updateCurrentTime, 1000);

    ConfirmModal.info({
        title: '当前时间',
        content: currentTime,
    })
}

onBeforeUnmount(() => {
    if (intervalId !== undefined) {
        clearInterval(intervalId);
    }
});
</script>
```


## 嵌套弹窗

弹窗可以无限嵌套，但是遮罩只有一层，不会因此让整个页面都充满遮罩。

```vue
<template>
    <mt-button @click="showModal()">显示弹窗</mt-button>
</template>
<script setup lang="tsx">
import { ref } from 'vue';
import { ConfirmModal, Button as MtButton, Skeleton as MtSkeleton } from '@mitra-design/web';

const seconds = ref(3);

let intervalId: number | undefined;

const showNestedModal = () => {
    ConfirmModal.success({
        title: '没有了',
        content: () => <span>你已经成功打开5个弹窗，{seconds.value}s 后自动关闭所有弹窗</span>,
    })

    intervalId = setInterval(() => {
        seconds.value -= 1;
    }, 1000)

    setTimeout(() => {
        ConfirmModal.closeAll();
        clearInterval(intervalId);
        seconds.value = 3;
    }, 3000);
}

const showModal = (index = 1) => {
    if (index === 5) {
        return showNestedModal();
    }

    ConfirmModal.info({
        width: (6 - index) * 200,
        title: `弹窗${index}`,
        content: () => <div>
            <MtButton onClick={() => showModal(index + 1)}>显示子弹窗</MtButton>
            <br />
            <br />
            <MtSkeleton style={{ width: '100%', height: `${(6 - index) * 100}px` }} loading />
        </div>,
    })
}
</script>
```


## 配置说明

完整配置项说明：

```ts import-file
../types/confirm-modal-props.ts
```

```ts import-file
../../_types/modal/index.ts
```
