<template>
    <modal-wrapper
        ref="modalWrapperRef"
        v-model="modalVisible"
        v-bind="{
            showMask,
            zIndex,
            destroyOnClose,
            renderContainer,
            inline,
            fullscreen,
        }"
        @opened="onOpened"
    >
        <!-- @slot 完全自定义弹窗主体 -->
        <slot name="panel">
            <modal-panel
                v-bind="{
                    title,
                    titleTips,
                    width,
                    divider,
                    closable,
                    footer,
                    fullscreen,
                    draggable,
                    padding,
                    bodyHeight,
                }"
                @cancel="onCancel"
                @confirm="onConfirm"
            >
                <template
                    v-for="key of Object.keys($scopedSlots)"
                    #[key]
                >
                    <!--
                        @slot 透传 slot
                        @member default | 内容区（包含 padding） | -
                        @member header | 自定义头部区域 | -
                        @member title | 自定义标题区域 | -
                        @member title-end | 标题尾部区域 | -
                        @member footer | 自定义整个底部区域 | -
                        @member footer-content | 自定义底部内容区域（包含 padding） | -
                    -->
                    <slot :name="key" />
                </template>
            </modal-panel>
        </slot>
    </modal-wrapper>
</template>
<script lang="ts">
export default {
    inheritAttrs: false,
    model: {
        prop: 'visible',
        event: 'update:modal-visible',
    },
};
</script>
<script setup lang="ts">
import { usePopupManager } from '@mitra-design/internal';
import { useVModel } from '@mitra-design/shared/composables';

import ModalPanel from './components/modal-panel.vue';
import ModalWrapper from './components/modal-wrapper.vue';
import { ModalManager } from './managers/modal-manager';
import type { ModalProps } from './types/props';

const props = withDefaults(defineProps<ModalProps>(), {
    showMask: true,
    closable: true,
    footer: true,
    width: 440,
    escClose: true,
    destroyOnClose: undefined,
    draggable: undefined,
    renderContainer: () => document.body,
});
const emit = defineEmits<{
    (event: 'update:modal-visible', value: boolean): void;

    /** 弹窗打开动画完成后触发，如果你要在弹窗打开时获取其中的 DOM 元素，需要借助此事件 */
    (event: 'opened'): void;

    /** 弹窗关闭动画完成后触发 */
    (event: 'closed'): void;

    /** 点击关闭按钮和默认的取消按钮时触发 */
    (event: 'cancel'): void;

    /** 点击默认的确认按钮时触发 */
    (event: 'confirm'): void;
}>();

const modalVisible = useVModel(props, 'visible', emit, 'update:modal-visible');

const modalWrapperRef = ref<InstanceType<typeof ModalWrapper>>();

const { zIndex } = usePopupManager('popup', {
    visible: modalVisible,
});

const onOpened = () => {
    emit('opened');
};

const close = async () => {
    if (modalVisible.value) {
        if (typeof props.beforeClose === 'function') {
            let ret = true;

            try {
                ret = (await props.beforeClose()) ?? true;
            } catch (error) {
                console.error('[mitra-design log] Modal beforeClose error: ', error);
                ret = false;
            }

            if (!ret) return;
        }

        modalVisible.value = false;

        return new Promise<void>((resolve) => {
            setTimeout(() => {
                emit('closed');
                resolve();
            }, 310);
        });
    }
};

const onCancel = () => {
    emit('cancel');
    close();
};

const onConfirm = () => {
    emit('confirm');
    close();
};

// ================================================
// Esc Close
// ================================================

let isKeydownEventRegistered = false;

const onKeyDown = (e: KeyboardEvent) => {
    // 仅支持 ESC 关闭最后一层弹窗
    if (e.key === 'Escape' && ModalManager.isLastModal(modalWrapperRef.value?.getModalWrapperEl?.())) {
        onCancel();
    }
};

const addKeydownEvent = () => {
    if (props.escClose && !isKeydownEventRegistered) {
        document.documentElement.addEventListener('keydown', onKeyDown);
        isKeydownEventRegistered = true;
    }
};

const removeKeydownEvent = () => {
    if (props.escClose && isKeydownEventRegistered) {
        document.documentElement.removeEventListener('keydown', onKeyDown);
        isKeydownEventRegistered = false;
    }
};

watch(modalVisible, (v) => {
    if (v) {
        addKeydownEvent();
    } else {
        removeKeydownEvent();
    }
});

onMounted(() => {
    if (modalVisible.value) {
        addKeydownEvent();
    }
});

onBeforeUnmount(() => {
    removeKeydownEvent();
});

// ================================================
// Expose
// ================================================
defineExpose({
    close,
});
</script>
