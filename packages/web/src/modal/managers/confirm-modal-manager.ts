import type { ManuallyMountPromise } from '@mitra-design/helpers/manually-mount';

import { invoke_ConfirmModal } from '../invoker/invoke-confirm-modal';
import { CONFIRM_MODAL_TYPES } from '../types/common';
import type { ConfirmModalConfig } from '../types/confirm-modal-props';

class ConfirmModalManager {
    private static confirmModalList = new Set<ManuallyMountPromise<string>>();

    public closeAll: () => void;

    public info: (config: ConfirmModalConfig) => void = () => {};

    public success: (config: ConfirmModalConfig) => void = () => {};

    public warning: (config: ConfirmModalConfig) => void = () => {};

    public error: (config: ConfirmModalConfig) => void = () => {};

    public confirm: (config: ConfirmModalConfig) => void = () => {};

    constructor() {
        CONFIRM_MODAL_TYPES.reduce((pre, type) => {
            pre[type] = (config: ConfirmModalConfig) => {
                const modalInstance = invoke_ConfirmModal({
                    ...config,
                    type,
                });
                ConfirmModalManager.confirmModalList.add(modalInstance);
                return modalInstance;
            };
            return pre;
        }, this as any);

        this.closeAll = () => {
            ConfirmModalManager.confirmModalList.forEach((modalInstance) => {
                modalInstance.close();
            });
        };
    }
}

export const confirmModalManager = new ConfirmModalManager();
