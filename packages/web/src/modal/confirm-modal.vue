<template>
    <modal-wrapper
        v-model="modalVisible"
        v-bind="{
            showMask,
            zIndex,
            destroyOnClose,
            renderContainer,
        }"
    >
        <div
            :class="bem()"
            :style="bodyStyle"
        >
            <div :class="bem('main')">
                <div
                    v-if="showIcon"
                    :class="bem('icon-wrapper')"
                >
                    <mt-svg-icon
                        :class="bem('icon', [type])"
                        :icon="icon"
                        :size="20"
                    />
                </div>
                <div :class="bem('header-content')">
                    <div :class="bem('header')">
                        <div :class="bem('title')">
                            <mt-auto-ellipsis :content="title">
                                <mt-title :heading="4">{{ title }}</mt-title>
                            </mt-auto-ellipsis>
                        </div>
                    </div>
                    <div :class="bem('content')">
                        <!-- @slot 自定义内容-->
                        <slot>
                            <component
                                :is="finalContent"
                                v-if="(typeof content === 'function')"
                            />
                            <template v-else>
                                {{ content }}
                            </template>
                        </slot>
                    </div>
                </div>
            </div>
            <modal-footer
                v-if="footer"
                v-bind="{
                    divider: false,
                    footer: computedFooter,
                }"
                @cancel="onCancel"
                @confirm="onConfirm"
            >
                <template
                    v-for="key of Object.keys($scopedSlots)"
                    #[key]
                >
                    <!--
                        @slot 透传 slot
                        @member footer-content | 自定义底部内容区 | `-`
                    -->
                    <slot :name="key" />
                </template>
            </modal-footer>
        </div>
    </modal-wrapper>
</template>
<script lang="ts">
// eslint-disable-next-line vue/one-component-per-file
export default {
    inheritAttrs: false,
    model: {
        prop: 'visible',
        event: 'update:confirm-modal-visible',
    },
};
</script>

<script setup lang="ts">
import { IconQuestionFill, IconWarningFill, IconCheckFill, IconCloseCircleFill, IconInfoCircleFill } from '@mitra-design/icons';
import { usePopupManager } from '@mitra-design/internal';
import { useVModel } from '@mitra-design/shared/composables';

import ModalFooter from './components/modal-footer.vue';
import ModalWrapper from './components/modal-wrapper.vue';
import type { ConfirmModalProps } from './types/confirm-modal-props';

import { useBem } from '@/composables';
import MtSvgIcon from '@/svg-icon/svg-icon.vue';
import MtTitle from '@/typography/typography-title.vue';


const props = withDefaults(defineProps<ConfirmModalProps>(), {
    showMask: true,
    showIcon: true,
    footer: true,
    width: 440,
    destroyOnClose: true,
    type: 'confirm',
});

const emit = defineEmits<{
    (event: 'update:confirm-modal-visible', value: boolean): void;
    (event: 'closed'): void;
    (event: 'cancel'): void;
    (event: 'confirm'): void;
}>();

const componentName = 'confirm-modal';

const { bem } = useBem(componentName);

const modalVisible = useVModel(props, 'visible', emit, 'update:confirm-modal-visible');

const { zIndex } = usePopupManager('popup', {
    visible: readonly(modalVisible),
});

const icon = computed(() => {
    switch (props.type) {
        case 'confirm':
            return IconQuestionFill;
        case 'info':
            return IconInfoCircleFill;
        case 'success':
            return IconCheckFill;
        case 'warning':
            return IconWarningFill;
        case 'error':
            return IconCloseCircleFill;
        default:
            return IconQuestionFill;
    }
});

const bodyStyle = computed(() => {
    return {
        width: typeof props.width === 'number' ? `${props.width}px` : props.width,
    };
});

const computedFooter = computed(() => {
    let showCancel = false;

    if (props.type === 'confirm' || props.type === 'error') {
        showCancel = true;
    }

    if (typeof props.footer === 'boolean') {
        return {
            showCancel: showCancel,
            showConfirm: props.footer,
        };
    }

    return {
        showCancel,
        confirmStatus: props.type === 'error' ? 'error' : undefined,
        ...props.footer,
    };
});


// eslint-disable-next-line vue/one-component-per-file
const finalContent = defineComponent({
    render(h) {
        // JSX
        if (typeof props.content === 'function') {
            return props.content(h);
        }

        return null;
    },
});

const close = () => {
    if (modalVisible.value) {
        modalVisible.value = false;

        return new Promise<void>((resolve) => {
            setTimeout(() => {
                emit('closed');
                resolve();
            }, 310);
        });
    }
};

const onCancel = () => {
    emit('cancel');
    close();
};

const onConfirm = () => {
    emit('confirm');
    close();
};

// ================================================
// Expose
// ================================================
defineExpose({
    close,
});
</script>
