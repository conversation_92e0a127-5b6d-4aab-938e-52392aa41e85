<template>
    <div
        v-if="title ?? titleTips ?? slots.title ?? slots['title-end'] ?? closable"
        :class="bem('', { 'with-divider': divider })"
    >
        <slot name="title">
            <div :class="bem('title-wrapper')">
                <!-- Title -->
                <mt-auto-ellipsis
                    v-if="title"
                    :content="title"
                >
                    <mt-title :heading="4">{{ title }}</mt-title>
                </mt-auto-ellipsis>
                <mt-tips
                    v-if="titleTips"
                    v-bind="tipsProps"
                />
                <slot name="title-end" />
            </div>
        </slot>

        <!-- Close -->
        <icon-close
            v-if="closable"
            :size="18"
            :class="bem('close-icon')"
            @click="onClickClose"
            @mousedown.native.stop
        />
    </div>
</template>
<script setup lang="ts">
import { IconClose } from '@mitra-design/icons';

import MtAutoEllipsis from '@/auto-ellipsis/auto-ellipsis.vue';
import { useBem } from '@/composables';
import MtTips from '@/tips/tips.vue';
import type { TipsProps } from '@/tips/types';
import MtTitle from '@/typography/typography-title.vue';

const props = defineProps<{
    title?: string;
    titleTips?: string | TipsProps;
    closable?: boolean;
    divider?: boolean;
}>();

const emit = defineEmits<{
    (event: 'cancel'): void;
}
>();

const componentName = 'modal-header';

const { bem } = useBem(componentName);

const slots = useSlots();

const tipsProps = computed(() => {
    if (typeof props.titleTips === 'string') {
        return {
            content: props.titleTips,
        };
    }

    return props.titleTips;
});

const onClickClose = () => {
    emit('cancel');
};
</script>
