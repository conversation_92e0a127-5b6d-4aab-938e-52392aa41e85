<template>
    <div
        v-if="innerVisible || mounted"
        ref="containerRef"
    >
        <modal-mask
            v-if="showMask && fullscreen !== 'page'"
            :visible="computedVisible"
            :z-index="zIndex"
            :style="inlineStyle"
        />
        <div
            v-if="mounted"
            v-show="innerVisible"
            :class="bem()"
            :style="{ zIndex, ...inlineStyle }"
        >
            <transition
                :name="fullscreen !== 'page' ? bem('zoom-fade') : ''"
                appear
                @enter="onFadeEnter"
                @after-enter="onAfterEnter"
                @after-leave="onFadeAfterLeave"
            >
                <div
                    v-show="computedVisible"
                    :class="bem('container')"
                    :style="style"
                >
                    <slot />
                </div>
            </transition>
        </div>
    </div>
</template>
<script lang="ts">
let cursorPosition: { left: number; top: number } | null = null;

const getCursorPosition = (e: MouseEvent) => {
    cursorPosition = {
        left: e.clientX,
        top: e.clientY,
    };

    setTimeout(() => {
        cursorPosition = null;
    }, 100);
};

// 捕获阶段触发
document.documentElement.addEventListener('click', getCursorPosition, true);

export default {
    inheritAttrs: false,
    model: {
        prop: 'visible',
        event: 'update:modal-wrapper-visible',
    },
};
</script>
<script setup lang="ts">
import { useVModel, useTeleportElement, useAutoLockScroll } from '@mitra-design/shared/composables';
import { computed, ref } from 'vue';
import type { CSSProperties } from 'vue/types/jsx';

import { ModalManager } from '../managers/modal-manager';

import ModalMask from './modal-mask.vue';

import { useBem } from '@/composables';

const props = withDefaults(defineProps<{
    visible?: boolean;
    showMask?: boolean;
    zIndex?: number;
    destroyOnClose?: boolean;
    renderContainer?: null| string | HTMLElement | (() => HTMLElement | string);
    inline?: boolean;
    fullscreen?: 'modal' | 'page';
}>(), {
    destroyOnClose: undefined,
    renderContainer: 'body',
});

const emit = defineEmits<{
    (event: 'update:modal-wrapper-visible', value: boolean): void;
    (event: 'opened'): void;
}
>();

const componentName = 'modal-wrapper';

const { bem } = useBem(componentName);

const containerRef = ref<HTMLDivElement>();

// ===============================================
// Visible
// ===============================================
const modelVisible = useVModel(props, 'visible', emit, 'update:modal-wrapper-visible');
const innerVisible = ref(false);

watchEffect(() => {
    if (props.visible) {
        innerVisible.value = true;
    }
});

const computedVisible = computed(() => {
    return modelVisible.value ?? innerVisible.value;
});

const renderContainer = computed<HTMLElement | null>(() => {
    const to = typeof props.renderContainer === 'function' ? props.renderContainer() : props.renderContainer;

    if (typeof to === 'string') {
        return document.querySelector(to) ?? document.body;
    }

    return to;
});

const { mounted, attach, detach } = useTeleportElement({ to: renderContainer });

useAutoLockScroll(computedVisible, renderContainer);

watch(computedVisible, async (val) => {
    await nextTick();

    if (val && containerRef.value) {
        attach(containerRef.value);
    }

    if (val) {
        ModalManager.add(containerRef.value!);
    } else {
        ModalManager.remove(containerRef.value!);
    }

}, { immediate: true, flush: 'post' });


// ===============================================
// Modal
// ===============================================
const style = computed(() => {
    return {
        transformOrigin: modalTransformOrigin.value,
    };
});

const inlineStyle = computed<CSSProperties>(() => {
    return props.inline ? { position: 'absolute' } : {};
});


// ================================================
// Transition
// ================================================
const modalTransformOrigin = ref<string>();

const setTransformOrigin = async (el: HTMLDivElement) => {
    await nextTick();

    let transformOrigin = '';

    if (cursorPosition) {
        const rect = el.getBoundingClientRect();
        const width = el.clientWidth;
        const height = el.clientHeight;
        // 当初始状态缩小 50% 时，通过 getBoundingClientRect 获取的 left 是缩小后的值，需要一半宽度的 1/2
        transformOrigin = `${cursorPosition.left - (rect.left - width / 4)}px ${cursorPosition.top - (rect.top - height / 4)}px`;
    }

    modalTransformOrigin.value = transformOrigin;
};

const onFadeEnter = (el: HTMLDivElement) => {
    setTransformOrigin(el);
};

const onAfterEnter = () => {
    emit('opened');
};


const onFadeAfterLeave = () => {
    innerVisible.value = false;
    modalTransformOrigin.value = '';
    cursorPosition = null;

    // 动画结束，如果需要销毁，则移除 dom
    if (props.destroyOnClose) {
        detach(containerRef.value);
    }
};


// =================================
// Unmount
// 处理一些非正常关闭时的逻辑
// =================================
onBeforeUnmount(() => {
    if (containerRef.value) {
        ModalManager.remove(containerRef.value);
        detach(containerRef.value);
    }
});


// =================================
// Expose
// =================================
defineExpose({
    getModalWrapperEl: () => containerRef.value,
});
</script>
