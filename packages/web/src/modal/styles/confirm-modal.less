.mt-confirm-modal {
    margin: 0 auto;
    background-color: @color-background-white;
    border-radius: @border-radius;
    box-shadow: @shadow-2-down;

    * {
        box-sizing: border-box;
    }

    &__main {
        display: flex;
        width: 100%;
        padding: 24px 20px 0;
    }

    &__icon-wrapper {
        margin-top: 2px;
        margin-right: 8px;
    }

    &__icon {
        &--success {
            color: @color-success;
        }

        &--error {
            color: @color-error;
        }

        &--warning {
            color: @color-warning;
        }

        &--info {
            color: @color-processing;
        }

        &--confirm {
            color: @color-icon-dark;
        }
    }

    &__header-content {
        flex-grow: 1;
        flex-shrink: 1;
        flex-basis: auto;
    }

    &__content {
        padding: 16px 0;
        font-size: @font-size;
        line-height: 20px;
        color: @color-text-2;
    }
}
