@import './input-number.tokens.less';

.mt-input-number {
    position: relative;
    display: inline-block;

    &__controls {
        position: absolute;
        top: 0;
        width: 0;
        right: -10px;
        display: flex;
        flex-direction: column;
        height: 100%;
        background: @color-background;
        border-left: 1px solid @color-border;
        opacity: 0;
        transition: .3s;
        cursor: pointer;
        box-sizing: border-box;
        flex-shrink: 0;

        &--visible {
            right: 0;
            opacity: 1;
        }

        &--button-mode {
            position: static;
            opacity: 1;
            border: none;

            .mt-input-number__decrease, .mt-input-number__increase {
                color: @color-icon-dark;
            }

            .mt-input-number__decrease {
                border: none;
            }
        }
    }

    &__decrease, &__increase {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        color: @color-icon;
        transition: .3s;

        &:hover {
            background-color: @color-background-dark;
        }

        div&&--disabled {
            color: @color-icon-light;
            background-color: @color-background;
            cursor: not-allowed;
        }
    }

    &__decrease {
        order: 2;
        border-top: 1px solid @color-border;
    }

    &__increase {
        order: 1;
    }

    &__prefix-wrapper, &__suffix-wrapper {
        white-space: nowrap;
    }

    &--gray {
        .mt-input-number__controls--button-mode {
            &.mt-input-number__controls--prepend {
                border-right: 1px solid @color-border;
            }

            &.mt-input-number__controls--append {
                border-left: 1px solid @color-border;
            }
        }
    }

    &--button-mode {
        .mt-input {
            text-align: center;
        }
    }
}

.controls-size(@size) {
    .mt-input-number {
        &--@{size} {
            width: ~'@{mt-input-number-width--@{size}}';;

            .mt-input-number__controls {
                &--visible {
                    width: ~'@{mt-input-number-controls-width--@{size}}';
                }

                &--button-mode {
                    // 减去边框
                    width: calc(~'@{mt-input-number-controls-height--@{size}}' - 1px);
                    height: calc(~'@{mt-input-number-controls-height--@{size}}' - 2px);
                }
            }
        }
    }
}

.controls-size(large);
.controls-size(medium);
.controls-size(small);
.controls-size(mini);
