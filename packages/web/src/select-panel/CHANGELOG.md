# SelectPanel

## 2023-09-15 (fix) /刘青/

去除了仅多选模式下的宽度设定

## 2024-01-31 (feat) /刘青/ _patch_

增加虚拟列表的 `estimatedSize` 属性支持

## 2024-03-28 (feat) /刘青/ _patch_

新增 `blankText` 自定义空白数据的显示文字

## 2024-04-08 (style) /刘青/

- 去除分组中间 `1px` 的线
- 调整选项条目的左右边距，`16px` => `12px`
- tag 组件默认的样式调整为 `outline-light`

## 2024-06-13 (feat) /刘青/

新增空状态插槽 `blank`

## 2024-08-12 (feat) /刘青/ _patch_

- 新增 `input-suffix` 插槽
- 底部替换成文字按钮

## 2024-09-11 (fix) /刘青/

修复单选模式下，部分数据相同会出现多个选中项的问题

## 2024-12-06 (feat) /刘青/

- 新增全选功能
- 新增 `header` 插槽

## 2024-12-19 (feat) /刘青/

去除选项默认 title 的展示，仅在选项 label 超出时展示

## 2025-03-13 (fix) /刘青/ _patch_

修复 `options` 数据更新，但 `selection-change` 事件未触发的问题

## 2025-04-07 (fix) /刘青/ _patch_

全选出现条件由非搜索状态下出现改为仅在无搜索关键词时出现

## 2025-06-10 (fix) /刘青/ _patch_

优化组件性能，优化 `selection-change` 触发时机
