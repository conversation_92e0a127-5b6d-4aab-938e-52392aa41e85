@import '@/styles/vars.less';
@import '@/styles/mixins.less';
@menu-label-margin-top: 2px;

.mt-menu {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 8px 4px;
    box-sizing: border-box;
    position: relative;
    transition: all 0.3s linear;
    overflow-y: hidden;

    &.with-shadow {
        box-shadow: @shadow-1-right;
    }

    &__base-menu {
        display: flex;
        padding: 0 8px;
        margin-left: -8px;
        margin-right: -8px;
        flex-direction: column;
        flex: 1 0 0;
        overflow: hidden;
    }

    &__menu-item {
        padding: 10px 12px;
        margin-top: @menu-label-margin-top;
        line-height: 20px;
        font-size: 13px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        transition: all 0.3s linear;

        &.is-active {
            font-weight: 600;
        }

        &.is-disabled {
            cursor: not-allowed;
        }
    }

    &__menu-item-label {
        display: inline-flex;
        flex: 1;
        min-width: 0;
        .ellipsis();

        .mt-menu__menu-icon {
            flex-shrink: 0;
        }

        > span {
            .ellipsis();
        }
    }

    &__sub-menu {
        width: 100%;
        margin-top: @menu-label-margin-top;
        cursor: pointer;
        transition: all 0.3s linear;

        &.is-disabled {
            cursor: not-allowed;
        }
    }

    &__sub-menu-title {
        line-height: 20px;
        font-size: 13px;
        padding: 10px 12px;
        border-radius: 4px;
        display: inline-flex;
        transition: all 0.3s linear;
        .flex(space-between,center);
    }

    &__sub-menu-label {
        display: inline-flex;
        min-width: 0;

        svg {
            flex-shrink: 0;
        }

        &.is-active {
            font-weight: 600;
        }

        > span {
            .ellipsis();
        }
    }

    &__toolgle-icon {
        margin-left: 8px;
        transition: transform 0.3s;

        &.rotate {
            transform: rotate(180deg);
        }

        & > svg {
            vertical-align: -0.15em !important;
        }
    }

    &__toolgle-horizontal-icon {
        transform: rotate(-90deg);
    }

    &__menu-icon {
        flex: 20px 0 0;
        margin-right: 12px;
        vertical-align: -0.25em !important;

        &.is-collapsed {
            margin-right: 0;
        }
    }

    &__group-menu-item {
        padding: 0 12px;
        margin-top: @menu-label-margin-top;
        line-height: 20px;
        font-size: 12px;
    }

    &__collapse-button {
        height: 32px;
        .flex(flex-start,center);
        padding-left: 12px;
        margin-top: 4px;
        cursor: pointer;
    }

    // menu 具有定制的 scroll 样式
    .os-scrollbar {
        --os-size: 8px !important;
    }
}

.menu-popup {
    padding: 8px 8px 12px !important;
    margin: 0 12px !important;
    min-width: 130px !important;
    border-radius: 4px;
    border: none !important;
}

.menu-item-label-tooltip {
    box-shadow: @shadow-3-down;

    .popper__arrow {
        display: none !important;
    }
}
