<template>
    <mt-trigger
        :visible="visible"
        :arrow="arrow"
        :offset="8"
        :destroy-on-hide="destroyOnHide"
        :disabled="disabled"
        :placement="placement"
        :render-container="renderContainer"
        :trigger="trigger"
        :singleton="singleton"
        :delay="delay"
        @visible-change="onVisibleChange"
    >
        <slot />
        <div
            slot="popup"
            :class="bem('popup', [size])"
        >
            <!-- @slot 自定义气泡中的内容 -->
            <slot name="content">
                <!-- JSX Content -->
                <component
                    :is="finalContent"
                    v-if="typeof content === 'function'"
                />
                <template v-else>
                    <!-- List Content -->
                    <mt-paragraph
                        v-if="Array.isArray(content)"
                        component="div"
                        :class="bem('content-list')"
                        :show-list-marker="showListMarker"
                    >
                        <ul>
                            <div
                                v-if="content[0]"
                                :class="bem('title')"
                            >
                                {{ content[0] }}
                            </div>
                            <li
                                v-for="(item, index) of content.slice(1)"
                                :key="index"
                            >
                                {{ item }}
                            </li>
                        </ul>
                    </mt-paragraph>
                    <!-- Content -->
                    <div
                        v-else-if="typeof content === 'string'"
                        :class="bem('content')"
                    >
                        {{ content }}
                    </div>
                </template>
            </slot>
        </div>
    </mt-trigger>
</template>
<script setup lang="ts">
import MtTrigger from '../trigger/trigger.vue';

import type { TooltipProps } from './types';

import { useBem } from '@/composables';

const props = withDefaults(defineProps<TooltipProps>(), {
    visible: undefined,
    arrow: true,
    placement: 'top',
    trigger: 'hover',
    showListMarker: true,
    destroyOnHide: true,
    disabled: undefined,
    size: 'medium',
    delay: 300,
    renderContainer: () => document.body,
});

const emit = defineEmits<{
    /** 显示或隐藏时触发 */
    (event: 'visible-change', visible: boolean): void;
}>();

const componentName = 'tooltip';

const { bem } = useBem(componentName);

defineOptions({
    inheritAttrs: false,
});

const finalContent = defineComponent({
    render: (h) => {
        if (typeof props.content === 'function') {
            return props.content(h);
        }

        return null;
    },
});

const onVisibleChange = (visible: boolean) => {
    emit('visible-change', visible);
};
</script>
