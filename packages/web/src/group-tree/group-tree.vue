<template>
    <div
        :class="bem()"
        :style="{
            marginLeft: 0,
            marginRight: 0,
        }"
    >
        <!-- 搜索框 -->
        <mt-input-search
            v-if="showSearch"
            v-model="searchText"
            clearable
            :placeholder="placeholder"
            :size="inputSize"
            :style="{ margin: `0 ${paddingX}px` }"
            @clear="emitClearSearch"
        />

        <!-- 添加分组 -->
        <div
            v-if="showHeader"
            :class="[bem('header')]"
            :style="{
                marginLeft: `${paddingX}px`,
                marginRight: `${paddingX}px`,
            }"
        >
            <div :class="bem('header-title')">
                <!-- @slot 分组标题 -->
                <slot name="header-title">
                    {{ title }}
                </slot>
            </div>
            <el-popover
                v-if="treeSetting"
                ref="settingPopover"
                width="240"
                placement="bottom-start"
            >
                <group-tree-setting />
            </el-popover>
            <mt-button
                v-if="treeSetting"
                v-popover:settingPopover
                :class="bem('header-icon-setting')"
                variant="text-minor"
                size="mini"
            >
                <template #icon>
                    <icon-configuration />
                </template>
            </mt-button>

            <div :class="bem('header-append')">
                <slot name="header-append">
                    <popover-menu
                        v-if="allowManage"
                        ref="createGroup"
                        :only-use-sub-panel="true"
                    >
                        <create-group
                            slot="subPanel"
                            @confirm="onCreateConfirm"
                        />
                        <div
                            slot="reference"
                            :class="bem('header-icon-wrap')"
                        >
                            <mt-button
                                variant="text"
                                size="mini"
                            >
                                <template #icon>
                                    <icon-plus />
                                </template>
                            </mt-button>
                        </div>
                    </popover-menu>
                </slot>
            </div>
        </div>

        <!-- 定位功能， 开启了定位 && 节点不可见 && 未被折叠 && 未被过滤排除 -->
        <div
            v-if="locationCheckedNode && !currentNodeVisible && !isCurrentNodeFolded && !isCurrentNodeFilteredOut"
            :class="[bem('checked-info'), bem(`checked-info-${fadeDirection}`)]"
            :style="{
                paddingLeft: `${paddingX}px`,
                paddingRight: `${paddingX}px`,
            }"
        >
            <div :class="[bem('checked-info-label')]">
                {{ t('selectedLabel') }}
                <span :label="currentNode?.node?.data.name">
                    {{ currentNode?.node?.data.name }}
                </span>
            </div>
            <el-tooltip :content="locationTooltip">
                <icon-previous @click="scrollIntoCurrentNode" />
            </el-tooltip>
        </div>

        <!-- 分组树 -->
        <group-tree-list
            ref="tree"
            :search-text="searchText"
            :data="data"
            :custom-group-text="customGroupText"
            :default-expanded-keys="innerDefaultExpandedKeys"
            :loading="loading"
            @current-hide="onCurrentHide"
            @current-show="onCurrentShow"
            @node-expand="onNodeExpand"
            @node-collapse="onNodeCollapse"
            @current-change="onCurrentChange"
            @search="onSearch"
        >
            <template #operation="props">
                <slot
                    name="operation"
                    v-bind="props"
                />
            </template>
        </group-tree-list>
    </div>
</template>
<script lang="ts">
import type { ElTree } from '@mitra-design/element-ui';
import { IconConfiguration, IconPlus } from '@mitra-design/icons';
import type { ComponentSize } from 'element-ui';
import type { TreeNode, TreeProps } from 'element-ui/types/tree';
import Vue from 'vue';

import CreateGroup from './group-operation/create.vue';
import GroupTreeList from './group-tree-list.vue';
import GroupTreeSetting from './group-tree-setting.vue';
import {
    allowManage,
    customGroupText,
    currentGroupId,
    groupData,
    inputSize,
    groupNodeSize,
    onCreateGroup,
    onDeleteGroup,
    onMoveGroup,
    onNodeClick,
    onRenameGroup,
    onDragover,
    onDragend,
    onDrop,
    draggable,
    onDropSuccess,
    onCurrentChange,
    onNodeCollapse,
    onNodeExpand,
    onDeleteCurrentGroup,
    disabledGroupIds,
    cascaderData,
    operationConfigs,
    treeSetting,
    allowDrop,
    icon,
    lazy,
    load,
    props,
    offsetLeft,
    paddingX,
    highlightCurrent
} from './provide-inject-keys';
import type { IDropSuccessParams, IGroupData, IGroupTreeListNode, IOperationConfirmParams, TreeNodeComponent, TreeSetting } from './types';

import MtButton from '@/button/button.vue';
import MtInputSearch from '@/input-search/input-search.vue';
import { I18n } from '@/locale';
import { PopoverMenu } from '@/popover-menu';


const componentName = 'group-tree';

@Bem(componentName)
@I18n(componentName)
@Component({
    componentName,
    name: componentName,
    components: { CreateGroup, GroupTreeList, PopoverMenu, MtInputSearch, GroupTreeSetting, MtButton, IconConfiguration, IconPlus },
    inheritAttrs: false,
})
export default class GroupTree extends Vue {
    /** 搜索框 `placeholder` */
    @Prop({ default: function (this: Vue) { return this.t('searchPlaceholder'); } })
    placeholder!: string;

    /** 输入框大小 */
    @ProvideReactive(inputSize)
    @Prop({ default: 'medium' })
    inputSize!: ComponentSize;

    /**
     * 分组节点大小:
     * - large: 40px
     * - medium: 36px
     * - small: 32px
     * - mini: 28px
     */
    @ProvideReactive(groupNodeSize)
    @Prop({ default: 'medium' })
    groupNodeSize!: ComponentSize;

    /** 是否可以管理分组 */
    @ProvideReactive(allowManage)
    @Prop({ default: true })
    allowManage!: boolean;

    /** 是否可以拖拽分组 */
    @Prop({ default: true })
    draggable!: boolean;

    @ProvideReactive(draggable)
    get computedDraggbale() {
        if (this.treeSetting?.filterEmpty.value) return false;
        return this.draggable;
    }

    /** 分组数据 */
    @ProvideReactive(groupData)
    @Prop({ required: true })
    data!: [];

    /**
     * 级联选择框的数据源，当分组树和选择框数据不同时传入
     * - 正常来说，分组树和选择框的数据应该是一样的
     * - 这里仅作为私有云分组的定制需求，使用前需确认你的分组树和选择框的数据是不是同一个
     */
    @ProvideReactive(cascaderData)
    @Prop()
    cascaderData?: [];

    /** 当前选中的分组节点 `id` */
    @ProvideReactive(currentGroupId)
    @Prop()
    currentGroupId?: string;

    /** 禁止操作的分组 `id` 集合 */
    @ProvideReactive(disabledGroupIds)
    @Prop()
    disabledGroupIds?: string[];

    /** 操作配置 */
    @ProvideReactive(operationConfigs)
    @Prop()
    operationConfigs?: any[];

    /** 分组设置 */
    @ProvideReactive(treeSetting)
    @Prop()
    treeSetting?: TreeSetting;

    /** 透传的 element-ui props 拖拽时判定目标节点能否被放置 */
    @ProvideReactive(allowDrop)
    @Prop()
    allowDrop?: ElTree['allowDrop'];

    @ProvideReactive(highlightCurrent)
    @Prop({ type: Boolean, default: true })
    highlightCurrent?: ElTree['highlightCurrent'];

    /** 自定义节点图标 */
    @ProvideReactive(icon)
    @Prop()
    icon?: () => string;

    /** 是否懒加载子节点，需与 load 方法结合使用 */
    @ProvideReactive(lazy)
    @Prop({ type: Boolean, default: false })
    lazy?: boolean;

    /** 加载子树数据的方法，仅当 lazy 属性为true 时生效 */
    @ProvideReactive(load)
    @Prop()
    load?: ElTree['load'];

    /** 懒加载配置选项，可见 el-tree 文档 */
    @ProvideReactive(props)
    @Prop()
    props?: TreeProps;


    /** 默认展开的节点 `key` 数组 */
    @Prop()
    defaultExpandedKeys?: string[];

    /** 是否开启定位至选中节点功能 */
    @Prop({ type: Boolean, default: false })
    locationCheckedNode?: boolean;

    @Prop({ default: function (this: Vue) { return this.t('locationTip'); } })
    locationTooltip!: string;

    /** 是否加载中（挂载于树区域） */
    @Prop()
    loading?: boolean;

    /** 是否展示搜索框 */
    @Prop({ default: true })
    showSearch!: boolean;

    /** 是否展示头部 */
    @Prop({ default: true })
    showHeader!: boolean;

    @Prop({ default: function (this: Vue) {
        return this.t('grouping');
    } })
    title!: string;

    /** 开启此选项，将会使得每个节点增加 14px 的左侧空白，以避免展开箭头和拖拽图标重叠 */
    @ProvideReactive(offsetLeft)
    @Prop({ type: Boolean, default: false })
    offsetLeft!: boolean;


    /**
     * 树两侧留白历史实现有最外层 margin，现在此组件被用于各处，而 margin 不够灵活，业务代码里存在着 margin 覆盖，
     * margin 设计和外侧的 margin 覆盖，对于后续迭代此组件是一种阻塞。
     *
     * 以上，特此通过 style 重置抹平，新增水平 padding prop 来控制两侧留白。
     */

    /** 分组树水平方向内边距 */
    @ProvideReactive(paddingX)
    @Prop({ default: 16 })
    paddingX!: number;


    innerDefaultExpandedKeys: string[] = [];

    /** @see /packages/element-ui/src/components/tree/src/tree-node.vue  */
    currentNode: TreeNodeComponent | null = null;

    /** 判断当前节点任意祖先是否折叠 */
    isCurrentNodeFolded = false;

    /** 当前节点是否被过滤掉了 */
    isCurrentNodeFilteredOut = false;

    async updateCurrentNodeFolded() {
        if (!this.currentNode) return;
        await this.$nextTick();
        /** 是否被祖先折叠 */
        const getFoldedState = (node: TreeNode<any, IGroupData>): boolean => {
            if (node.level === 1) { // 加此判断，因为根节点的 parent 又是根节点了
                return !node.expanded;
            }
            if (!node.parent) {
                return false;
            }
            return node.parent.expanded ? getFoldedState(node.parent) : true;
        };

        this.isCurrentNodeFolded = getFoldedState(this.currentNode.node);
    }

    async updateCurrentNodeFiltered() {
        await this.$nextTick();
        /** 节点是否被删掉了 */
        if (!this.currentNode?.node.parent) {
            this.isCurrentNodeFilteredOut = true;
            return;
        }

        const traverse = (node: IGroupData, handler = (node: IGroupData) => {}) => {
            handler(node);
            if (node.children?.length) {
                node.children.forEach(child => {
                    traverse(child, handler);
                });
            }
        };

        let isRemoved = true;
        traverse({ children: this.data } as any as IGroupData, (node) => {

            if (node.id === this.currentNode?.node.data.id) {
                isRemoved = false;
            }
        });

        /** 当前节点是否实际渲染在树中：
         * - 未修改 data 引用，此节点 visible === false
         * - 修改了 data 引用，此节点无法在 data 中找到
         *
         * 对于 data 引用变更的情况，手动遍历，因 el-tree 的 getNode 方法不可靠。// https://github.com/ElemeFE/element/issues/18248
         */
        this.isCurrentNodeFilteredOut =
            !this.currentNode.node.visible || isRemoved;

    }

    @Watch('defaultExpandedKeys', { immediate: true })
    onDefaultExpandedKeysChange(newVal: string[]) {
        this.innerDefaultExpandedKeys = newVal;
    }

    /** 自定义删除弹窗中的分组文字，如模板、用印流程 */
    @ProvideReactive(customGroupText)
    @Prop({ default: function (this: Vue) { return this.t('template'); } })
    customGroupText!: string;

    /**
     * 在点击搜索框清空按钮时触发
     */
    @Emit('clear-search')
    emitClearSearch() {}

    /**
     * 新增分组的确定回调，参数分别为：
     * - 分组名称
     * - 分组父 `id`
     */
    @Emit('create-group')
    @Provide(onCreateGroup)
    onCreateGroup(groupName: string, groupParentId?: string) {}

    /**
     * 删除分组
     * - 当前删除分组的 `id`
     * - 当前删除分组的父分组 `id`
     */
    @Emit('delete-group')
    @Provide(onDeleteGroup)
    onDeleteGroup(groupId: string, groupParentId: string) {}

    /**
     * 仅删除分组
     * - 当前删除分组的 `id`
     * - 当前删除分组的父分组 `id`
     */
    @Emit('delete-current-group')
    @Provide(onDeleteCurrentGroup)
    onDeleteCurrentGroup(groupId: string, groupParentId: string) {}

    /**
     * 重命名分组
     * - 当前重命名分组的 `id`
     * - 当前重命名分组新的名字
     * - 当前重命名分组的父分组 `id`
     */
    @Emit('rename-group')
    @Provide(onRenameGroup)
    onRenameGroup(groupId: string, groupName: string, groupParentId: string) {}

    /**
     * 移动分组
     * - 当前移动分组的 `id`
     * - 当前移动分组的名字
     * - 当前移动分组的父分组 `id`
     */
    @Emit('move-group')
    @Provide(onMoveGroup)
    onMoveGroup(groupId: string, groupName: string, groupParentId: string) {}

    /**
     * 点击结点触发的事件，参数：
     * - 当前点击的结点
     */
    @Emit('node-click')
    @Provide(onNodeClick)
    onNodeClick(currentNode: IGroupTreeListNode) {}

    /**
     * 节点被展开时触发的事件，参数分别为：
     * - 节点对应的 data
     * - 节点对应的 Node
     */
    @Emit('node-expand')
    @Provide(onNodeExpand)
    onNodeExpand(data: IGroupData, node: IGroupTreeListNode) {
        this.updateCurrentNodeFolded();
    }

    /**
     * 节点被关闭时触发的事件，参数分别为：
     * - 节点对应的 data
     * - 节点对应的 Node
     */
    @Emit('node-collapse')
    @Provide(onNodeCollapse)
    onNodeCollapse(data: IGroupData, node: IGroupTreeListNode) {
        this.updateCurrentNodeFolded();
    }

    /**
     * 拖拽成功后触发的事件，参数为一个对象，可以从它身上取需要的属性：
     * - 当前拖拽结点的 `id`
     * - 当前拖拽结点的名称
     * - 拖拽结束结点的 `id`
     * - 拖拽结束结点的名称
     * - 当前拖拽的结点
     * - 拖拽结束的结点
     * - 被拖拽节点的放置位置 `（before、after、inner）`
     * - event
     */
    @Emit('drop-success')
    @Provide(onDropSuccess)
    onDropSuccess(params: IDropSuccessParams) {}

    /** 暂不考虑抛出去，因行为和 el-tree 不一致， */
    // @Emit('current-change')
    @Provide(onCurrentChange)
    onCurrentChange(data: IGroupData, currentNode: IGroupTreeListNode) {
        this.currentNode = (this.treeRef?.treeRef as any)?.currentNode;
        this.isCurrentNodeFilteredOut = false;
        this.isCurrentNodeFolded = false;
    }

    /**
     * 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件），参数为：
     * - 当前拖拽的节点
     * - event
     */
    @Emit('dragover')
    @Provide(onDragover)
    onDragover(currentNode: IGroupTreeListNode, event: Event) {}

    /**
     * 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件），参数为：
     * - 当前拖拽的节点
     * - event
     */
    @Emit('dragend')
    @Provide(onDragend)
    onDragend(currentNode: IGroupTreeListNode, event: Event) {}

    /**
     * 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件），参数为：
     * - 当前拖拽的节点
     * - event
     */
    @Emit('drop')
    @Provide(onDrop)
    onDrop(currentNode: IGroupTreeListNode, event: Event) {}

    @Ref('createGroup')
    createGroupRef?: PopoverMenu;

    @Ref('tree')
    treeRef?: GroupTreeList;

    searchText = '';

    /** 当前选中节点是否在可视区，相对于树 */
    currentNodeVisible = true;

    /** 节点退出可视区域的方向 */
    fadeDirection: 'top' | 'bottom' = 'top';

    onCreateConfirm(params: IOperationConfirmParams) {
        const { name, parentId } = params;
        this.onCreateGroup(name!, parentId);
        this.createGroupRef?.close();

        this.searchText = '';
    }

    onCurrentHide(options: { direction: 'top' | 'bottom' }) {
        this.currentNodeVisible = false;
        this.fadeDirection = options.direction;
    }

    onCurrentShow() {
        this.currentNodeVisible = true;
    }

    onSearch() {
        this.updateCurrentNodeFiltered();
    }

    @Watch('data', { deep: true })
    onDataChange() {
        this.updateCurrentNodeFiltered();
        this.searchText = '';
    }

    /**
     * 定位至指定节点
     */
    async locationToNode(idOrEle: string | HTMLElement, options?: { animation?: boolean }) {
        await this.$nextTick();
        const { animation = true } = options ?? {};
        const nodeEle = typeof idOrEle === 'string' ? this.treeRef?.$el.querySelector(`.${this.bem()}-list-node[data-id="${idOrEle}"]`) : idOrEle;
        if (
            !nodeEle
            || !(nodeEle.parentElement instanceof HTMLElement)
        ) {
            return;
        }

        if (animation) {
            const className = `${this.bem()}-node-animated-fade`;

            nodeEle.parentElement.classList.add(className);
            nodeEle.parentElement.addEventListener('animationend', () => {
                nodeEle.parentElement?.classList.remove(className);
            }, {
                once: true,
            });
        }

        nodeEle.scrollIntoView({
            block: 'nearest',
            behavior: 'smooth',
        });
    }

    scrollIntoCurrentNode() {
        if (this.currentNode && this.treeRef?.$el) {
            const currentNodeDom = this.currentNode.$el;
            const content = currentNodeDom.querySelector(`.el-tree-node__content .${this.bem()}-list-node`);
            if (!(content instanceof HTMLElement)) {
                return;
            }

            this.locationToNode(content, { animation: false });
        }
    }

    get shouldWarning() {
        return this.lazy && (this.allowManage || this.draggable);
    }

    @Watch('shouldWarning', { immediate: true })
    onShouldWarningChange(nv: boolean) {
        if (nv) {
            console.error('[@mitra-design/web] [GroupTree] Prop "lazy" cannot be utilized in conjunction with either prop "allowManager" or prop "draggable")');
        }
    }

    // expose el-tree instance methods
    setCurrentKey(...rest: Parameters<ElTree['setCurrentKey']>) {
        return this.treeRef?.treeRef?.setCurrentKey(...rest);
    }

}
</script>
