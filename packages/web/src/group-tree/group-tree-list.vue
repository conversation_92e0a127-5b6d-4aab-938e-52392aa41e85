<template>
    <div
        v-loading="{ active: loading, background: 'white' }"
        v-scroll
        :class="cls"
        :style="{
            padding: `0 ${paddingX}px`,
        }"
    >
        <el-tree
            ref="elTree"
            :class="elTreeClasses"
            :data="data"
            :props="computedTreeProps"
            :allow-drag="allowDrag"
            :allow-drop="modifiedAllowDrop"
            :default-expanded-keys="defaultExpandedKeys"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :auto-expand-parent="true"
            :highlight-current="highlightCurrent"
            :current-node-key="currentGroupId"
            :draggable="draggable"
            :lazy="lazy"
            :load="load"
            node-key="id"
            check-on-click-node
            @node-drop="onNodeDrop"
            @node-expand="onNodeExpand"
            @node-collapse="onNodeCollapse"
            @current-change="handleCurrentChange"
        >
            <template #default="{ node }">
                <group-tree-list-node
                    :data-id="node.data.id"
                    :current-node="node"
                >
                    <template #operation="props">
                        <slot
                            name="operation"
                            v-bind="props"
                        />
                    </template>
                </group-tree-list-node>
            </template>
        </el-tree>
    </div>
</template>

<script lang="ts">
import type { ElTree } from '@mitra-design/element-ui';
import { vScroll } from '@mitra-design/internal';
import { vLoading } from '@mitra-design/web';
import type { ComponentSize } from 'element-ui';
import type { TreeProps } from 'element-ui/types/tree';
import { debounce } from 'lodash';
import Vue from 'vue';

import GroupTreeListNode from './group-tree-list-node.vue';
import {
    currentGroupId,
    disabledGroupIds,
    draggable,
    groupNodeSize,
    onDropSuccess,
    onNodeCollapse,
    onCurrentChange,
    onNodeExpand,
    allowDrop,
    treeSetting,
    lazy,
    load,
    props,
    offsetLeft,
    paddingX,
    highlightCurrent
} from './provide-inject-keys';
import type {
    IDropSuccessParams,
    IGroupData,
    IGroupTreeListNode,
    TreeSetting
} from './types';

const componentName = 'group-tree-list';

@Bem(componentName)
@Component({
    componentName,
    name: componentName,
    components: { GroupTreeListNode },
    directives: {
        loading: vLoading,
        scroll: vScroll,
    },
})
export default class GroupTreeList extends Vue {
    @InjectReactive(groupNodeSize)
    groupNodeSize!: ComponentSize;

    @InjectReactive(currentGroupId)
    currentGroupId?: string;

    @InjectReactive(disabledGroupIds)
    disabledGroupIds?: string[];

    @InjectReactive(draggable)
    draggable?: boolean;

    @InjectReactive(allowDrop)
    allowDrop?: ElTree['allowDrop'];

    @InjectReactive(highlightCurrent)
    highlightCurrent?: ElTree['highlightCurrent'];

    @InjectReactive(treeSetting)
    treeSetting?: TreeSetting;

    @InjectReactive(lazy)
    lazy?: boolean;

    @InjectReactive(load)
    load?: ElTree['load'];

    @InjectReactive(props)
    props?: TreeProps;

    @InjectReactive(offsetLeft)
    offsetLeft!: boolean;

    @InjectReactive(paddingX)
    paddingX!: number;

    /** 分组数据 */
    @Prop()
    data!: IGroupData[];

    /** 默认展开的节点 `key` 数组 */
    @Prop()
    defaultExpandedKeys!: string[];

    /** 用作树结点的搜索文本 */
    @Prop()
    searchText!: string;

    @Prop()
    loading?: boolean;

    /** 树列表的引用 */
    @Ref('elTree')
    treeRef?: ElTree;

    @Inject(onDropSuccess)
    onDropSuccess!: (params: IDropSuccessParams) => void;

    @Inject(onNodeExpand)
    onNodeExpand!: (data: IGroupData, node: IGroupTreeListNode) => void;

    @Inject(onNodeCollapse)
    onNodeCollapse!: (data: IGroupData, node: IGroupTreeListNode) => void;

    @Inject(onCurrentChange)
    onCurrentChange!: (data: IGroupData, node: IGroupTreeListNode) => void;

    intersectionObserver?: IntersectionObserver = undefined;


    async handleCurrentChange(data: IGroupData, node: IGroupTreeListNode) {
        this.intersectionObserver?.disconnect();
        await this.$nextTick();
        const currentNode = (this.treeRef as any)?.currentNode;
        const target = currentNode.$el.querySelector('.el-tree-node__content');
        this.intersectionObserver?.observe(target);

        this.onCurrentChange(data, node);
    }

    async handleSetCurrent() {
        this.currentGroupId && this.treeRef?.setCurrentKey(this.currentGroupId);
        await this.$nextTick();
        const currentNodeEle = this.treeRef?.$el.querySelector('.is-current');
        if (currentNodeEle instanceof HTMLElement) {
            currentNodeEle.click();
        }
    }

    debouncedHandleSetCurrent = debounce(this.handleSetCurrent, 10);

    readonly defaultProps: Partial<TreeProps> = {
        label: 'name',
    };

    get computedTreeProps() {
        return {
            ...this.defaultProps,
            ...this.props,
        };
    }

    get cls() {
        return this.bem('', [this.groupNodeSize]);
    }

    get elTreeClasses() {
        return {
            [this.bem('origin-tree')]: true,
            'offset-left': this.offsetLeft,
        };
    }

    @Watch('searchText')
    doWatchSearchText(newValue: string, oldValue: string) {
        if (newValue !== oldValue) {
            this.treeRef?.filter(newValue);
            this.$emit('search');
        }
    }

    // 动态切换高亮的结点
    @Watch('currentGroupId')
    async doWatchCurrentGroupId(nv: string, ov: string) {
        if (nv !== ov) {
            this.debouncedHandleSetCurrent();
        }
    }

    @Watch('data')
    async doWatchData(nv: any, ov: any) {
        if (nv !== ov) {
            this.debouncedHandleSetCurrent();
        }
    }

    /**
     * 搜索树结点
     */
    filterNode(value: string, data?: IGroupData) {
        if (!value) return true;
        return (data?.name.toLocaleLowerCase())?.includes(value.toLocaleLowerCase());
    }

    allowDrag(node?: IGroupTreeListNode) {
        return !this.disabledGroupIds?.includes((node?.data)?.id ?? '');
    }

    modifiedAllowDrop(node: IGroupTreeListNode, dropNode: IGroupTreeListNode, type: 'prev' | 'inner' | 'next') {
        if (this.disabledGroupIds?.includes(dropNode.data.id)) {
            return false;
        }
        if (!(typeof this.allowDrop === 'function')) return true;
        return this.allowDrop(node, dropNode, type);
    }

    onNodeDrop(
        currentNode?: IGroupTreeListNode,
        nextNode?: IGroupTreeListNode,
        pos?: string,
        event?: Event
    ) {
        const currentName = currentNode?.data.name;
        const currentId = currentNode?.data.id;
        const nextId = nextNode?.data.id;
        const nextName = nextNode?.data.name;

        if (pos === 'inner') {
            // 拖动结束，将放置的目标节点展开
            // 使用的 el-tree node 的内部方法，无类型
            // @ts-ignore
            nextNode?.expand?.();
            this.onNodeExpand(nextNode!.data!, nextNode!);
        }

        this.$nextTick(() => {
            // currentNode 不是最终被拖拽节点的实例，需要通过 key 获取真正被拖拽的那个节点实例
            const draggedNode = this.treeRef?.getNode(currentNode?.key);
            draggedNode?.setDraggedState(true);

            setTimeout(() => {
                draggedNode?.setDraggedState(false);
            }, 3500);
        });

        this.onDropSuccess({
            currentId,
            currentName,
            nextId,
            nextName,
            currentNode,
            nextNode,
            pos,
            event,
        });
    }

    mounted() {
        this.intersectionObserver = new IntersectionObserver(async (entries) => {
            await this.$nextTick();
            if (entries[0].intersectionRatio <= 0) {
                /** @param direction: 从哪边消失  */
                const direction = (entries[0].rootBounds!.top - entries[0].boundingClientRect.top) > 0 ? 'top' : 'bottom';
                this.$emit('current-hide', { direction });
            } else {
                this.$emit('current-show');
            }
        }, {
            root: this.$el,
            threshold: 0.01,
        });
    }
}
</script>
