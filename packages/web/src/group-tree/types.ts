import type { TreeNode } from 'element-ui/types/tree';
import type Vue from 'vue';

export interface IGroupData {
    id: string;
    name: string;
    total: number;
    parentId?: string;
    children?: IGroupData[];
    subname?: string;
}

export type IGroupTreeListNode = TreeNode<string, IGroupData>;

export type TOperationType = 'create' | 'move' | 'rename' | 'delete';

export interface IOperationConfirmParams {
    id?: string;
    name?: string;
    parentId?: string;
}

export interface IDropSuccessParams {
    currentId?: string;
    currentName?: string;
    nextId?: string;
    nextName?: string;
    pos?: string;
    currentNode?: IGroupTreeListNode;
    nextNode?: IGroupTreeListNode;
    event?: Event;
}

export interface TreeNodeComponent extends Vue {
    node: TreeNode<any, IGroupData>;
}


export interface TreeSetting {
    filterEmpty: {
        value: boolean;
        change: (value: boolean) => void;
    };
}
