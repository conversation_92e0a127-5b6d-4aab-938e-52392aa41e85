{"name": "@mitra-design/web", "version": "2.9.5", "description": "web 组件库", "scripts": {"build": "pnpm build:component && pnpm build:style && pnpm build:tsc", "build:style": "mitra-design-cli build:style", "build:component": "mitra-design-cli build:component --vue=2", "build:tsc": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json", "postinstall": "echo \"----------------> 🚂__MITRA_DESIGN_VERSION__: 2.9.5 <----------------\""}, "main": "es/index.js", "exports": {".": "./es/index.js", "./*": "./es/*", "./es/*": "./es/*", "./locale/lang": "./es/locale/lang/index.js", "./element-ui/types/*": {"typings": "./es/element-ui/types/*.d.ts"}, "./package.json": "./package.json"}, "typesVersions": {"*": {"locale/lang": ["es/locale/lang/index.d.ts"]}}, "files": ["es"], "sideEffects": ["*.css"], "homepage": "https://mitra-design.qiyuesuo.net/web", "peerDependencies": {"dayjs": "1.10.4", "lodash": "4.17.21", "vue": "2.7.4", "vue-class-component": "7.2.6", "vue-property-decorator": "9.1.2"}, "dependencies": {"@mitra-design/color": "workspace:*", "@mitra-design/element-ui": "workspace:*", "@mitra-design/helpers": "workspace:*", "@mitra-design/icons": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/locale": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/theme": "workspace:*", "@mitra-design/universal": "workspace:*", "@popperjs/core": "2.11.8", "@tanstack/virtual-core": "3.0.0-beta.53", "cropperjs": "^1.6.2", "element-ui": "2.15.8", "intersection-observer": "0.12.2", "lodash.clamp": "4.0.3", "lodash.debounce": "4.0.8", "scroll-into-view-if-needed": "2.2.25", "smooth-scrollbar": "8.8.1", "tippy.js": "^6.3.7"}, "devDependencies": {"@mitra-design/cli": "workspace:*"}}