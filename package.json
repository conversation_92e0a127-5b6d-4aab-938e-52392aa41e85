{"name": "mitra-design", "version": "1.0.0", "private": true, "description": "公共组件", "scripts": {"dev": "pnpm docs:dev", "docs:dev": "pnpm --dir packages/docs dev", "docs:build": "pnpm build && pnpm --dir packages/docs docs:build", "new": "tsx scripts/new/new.ts", "delete": "tsx scripts/new/remove.ts", "test": "vitest", "i18n:web": "node scripts/i18n/translate/index.js --pkg=web", "i18n:e-sign": "node scripts/i18n/translate/index.js --pkg=e-sign", "i18n:pro-components": "node scripts/i18n/translate/index.js --pkg=pro-components", "i18n:e-sign-mobile": "node scripts/i18n/translate/index.js --pkg=e-sign-mobile", "i18n:mobile": "node scripts/i18n/translate/index.js --pkg=mobile", "build": "pnpm -r run build", "publish-all": "pnpm --filter \"@mitra-design/*\" publish --no-git-checks --force", "publish-pkg": "tsx scripts/publish/index.ts", "publish-test": "pnpm publish-pkg --bump-versions --publish --check", "publish-master": "pnpm publish-pkg --bump-versions --master --check", "publish-ci": "pnpm publish-pkg --master --publish && pnpm check-versions", "testnode": "node -v", "lint": "eslint \"packages/*/src/**/*.{js,ts,jsx,tsx,vue}\" --quiet", "check-versions": "tsx scripts/check-latest-versions.ts"}, "repository": {"type": "git", "url": "https://git.qiyuesuo.me/scm/winf/components.git"}, "author": "QYS FE", "license": "MIT", "dependencies": {"@mitra-design/color": "workspace:*", "@mitra-design/internal": "workspace:*", "@mitra-design/shared": "workspace:*", "@mitra-design/theme": "workspace:*", "@vueuse/core": "10.10.0", "async-validator": "4.2.5", "dayjs": "1.10.4", "element-ui": "2.15.8", "lodash": "4.17.21", "node": "20.12.2", "overlayscrollbars": "2.4.7", "scroll-into-view-if-needed": "2.2.25", "vant": "2.12.1", "vue": "2.7.4", "vue-class-component": "7.2.6", "vue-demi": "^0.14.10", "vue-property-decorator": "9.1.2"}, "devDependencies": {"@babel/parser": "7.20.5", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.18.10", "@babel/traverse": "7.20.5", "@changesets/cli": "2.24.4", "@qys/eslint-plugin": "git+https://git.qiyuesuo.me/scm/winf/eslint-plugin.git#mitra-design", "@qys/stylelint-plugin": "git+https://git.qiyuesuo.me/scm/winf/stylelint-plugin.git", "@tsconfig/node20": "^20.1.4", "@types/dedent": "0.7.0", "@types/fs-extra": "9.0.13", "@types/glob": "8.0.0", "@types/js-yaml": "^4.0.5", "@types/less": "^3.0.3", "@types/lodash": "4.14.186", "@types/node": "^20.4.5", "@types/shelljs": "0.8.11", "@types/uppercamelcase": "3.0.0", "@types/webpack-env": "1.18.2", "@vitejs/plugin-react": "3.1.0", "@vue/babel-helper-vue-jsx-merge-props": "1.4.0", "@vue/test-utils": "1.3.0", "@vue/tsconfig": "^0.5.1", "babel-plugin-component": "1.1.1", "babel-plugin-import": "1.13.5", "commander": "9.4.1", "cross-env": "^7.0.3", "dedent": "0.7.0", "fast-glob": "3.2.12", "fs-extra": "10.1.0", "glob": "8.0.3", "he": "1.2.0", "js-yaml": "4.1.0", "jsdom": "20.0.0", "less": "4.1.3", "magic-string": "0.27.0", "npm-run-all": "4.1.5", "ora": "7.0.1", "parse5": "7.1.2", "postcss": "8.4.28", "prettier": "2.8.0", "rimraf": "3.0.2", "rollup": "4.13.2", "shelljs": "0.8.5", "shiki": "0.14.3", "simple-git": "3.16.0", "spark-md5": "3.0.2", "superagent": "8.0.5", "ts-morph": "^25.0.0", "tsc-alias": "1.8.7", "tslib": "2.4.0", "tsx": "4.7.2", "type-fest": "^4.32.0", "typedoc": "0.25.12", "typescript": "5.4.5", "unplugin-auto-import": "0.17.5", "unplugin-vue-components": "0.26.0", "unplugin-vue-macros": "2.9.5", "uppercamelcase": "3.0.0", "vite": "5.2.8", "vite-plugin-dts": "4.5.3", "vite-plugin-inspect": "0.8.3", "vitest": "1.5.0", "vue-i18n": "8.24.5", "vue-router": "3.6.5", "vue-template-compiler": "2.7.4", "vuedraggable": "2.24.3", "vuex": "3.6.2"}, "packageManager": "pnpm@8.15.9", "engines": {"node": ">=14.18.3", "pnpm": ">=7"}, "volta": {"node": "20.12.2"}}